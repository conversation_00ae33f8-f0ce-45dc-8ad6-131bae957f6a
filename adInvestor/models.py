# investor/models.py
from django.db import models
from django.contrib.auth.models import User


class Investor(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    business_email = models.EmailField(default="<EMAIL>")
    entity_name = models.CharField(max_length=100)
    website = models.URLField(null=True, blank=True)
    type = models.CharField(max_length=100)
    phone_number = models.CharField(max_length=15, blank=True)
    contact_name = models.CharField(max_length=100)
    billing_address = models.TextField()

    def __str__(self):
        return self.name
