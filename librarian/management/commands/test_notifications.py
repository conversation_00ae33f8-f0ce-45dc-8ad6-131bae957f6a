from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from librarian.notification_service import notification_service
from librarian.models import NotificationTemplate, NotificationCategory
from studentsData.models import Invoice, StudentData
import random


class Command(BaseCommand):
    help = 'Test the notification system with sample data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user-id',
            type=int,
            help='User ID to send test notifications to',
        )
        parser.add_argument(
            '--event-type',
            type=str,
            help='Specific event type to test',
        )
        parser.add_argument(
            '--list-events',
            action='store_true',
            help='List all available event types',
        )

    def handle(self, *args, **options):
        if options['list_events']:
            self.list_event_types()
            return

        user_id = options.get('user_id')
        event_type = options.get('event_type')

        if user_id:
            try:
                user = User.objects.get(id=user_id)
                self.stdout.write(f'Testing notifications for user: {user.username}')
            except User.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'User with ID {user_id} not found'))
                return
        else:
            # Get the first available user
            user = User.objects.filter(is_active=True).first()
            if not user:
                self.stdout.write(self.style.ERROR('No active users found'))
                return
            self.stdout.write(f'Using first available user: {user.username}')

        if event_type:
            self.test_specific_event(user, event_type)
        else:
            self.test_all_events(user)

    def list_event_types(self):
        """List all available event types"""
        self.stdout.write(self.style.SUCCESS('Available Event Types:'))
        templates = NotificationTemplate.objects.filter(is_active=True)
        
        for template in templates:
            self.stdout.write(f'  - {template.event_type}: {template.get_event_type_display()}')
            self.stdout.write(f'    Category: {template.category.name}')
            self.stdout.write(f'    Title: {template.title_template}')
            self.stdout.write('')

    def test_specific_event(self, user, event_type):
        """Test a specific event type"""
        self.stdout.write(f'Testing event type: {event_type}')
        
        try:
            template = NotificationTemplate.objects.get(event_type=event_type, is_active=True)
        except NotificationTemplate.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'Event type {event_type} not found'))
            return

        context_data = self.get_sample_context_data(event_type)
        
        result = notification_service.send_notification(
            event_type, user, **context_data
        )
        
        if result:
            self.stdout.write(self.style.SUCCESS(
                f'✓ Sent {event_type} notification (ID: {result.id}, Status: {result.status})'
            ))
        else:
            self.stdout.write(self.style.ERROR(f'✗ Failed to send {event_type} notification'))

    def test_all_events(self, user):
        """Test all available event types"""
        self.stdout.write('Testing all event types...')
        
        templates = NotificationTemplate.objects.filter(is_active=True)
        successful = 0
        failed = 0
        
        for template in templates:
            event_type = template.event_type
            context_data = self.get_sample_context_data(event_type)
            
            result = notification_service.send_notification(
                event_type, user, **context_data
            )
            
            if result:
                self.stdout.write(self.style.SUCCESS(
                    f'✓ {event_type}: {result.status}'
                ))
                successful += 1
            else:
                self.stdout.write(self.style.ERROR(f'✗ {event_type}: Failed'))
                failed += 1

        self.stdout.write('')
        self.stdout.write(f'Results: {successful} successful, {failed} failed')

    def get_sample_context_data(self, event_type):
        """Get sample context data for QR registration event"""
        base_data = {
            'library_name': 'Test Library',
            'student_name': 'John Doe',
        }

        event_specific_data = {
            'qr_registration': {
                'student_name': 'John Doe',
                'student_email': '<EMAIL>',
                'student_mobile': '9876543210',
                'course': 'Computer Science',
                'library_name': 'Test Library',
                'registration_date': '2024-01-10',
                'temp_student_id': 123,
            }
        }

        # Merge base data with event-specific data
        context_data = base_data.copy()
        if event_type in event_specific_data:
            context_data.update(event_specific_data[event_type])

        return context_data
