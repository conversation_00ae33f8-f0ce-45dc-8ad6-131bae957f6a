from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta
import random

from librarian.models import Li<PERSON>rian_param, AnalyticsCache
from librarian.views import calculate_dashboard_analytics
from studentsData.models import StudentData, Invoice, Courses, States
from visitorsData.models import Visitor


class Command(BaseCommand):
    help = 'Test analytics system with real data scenarios'

    def add_arguments(self, parser):
        parser.add_argument(
            '--librarian-id',
            type=int,
            help='Specific librarian ID to test (optional)'
        )
        parser.add_argument(
            '--create-test-data',
            action='store_true',
            help='Create sample test data for testing'
        )
        parser.add_argument(
            '--test-cache',
            action='store_true',
            help='Test cache invalidation and refresh logic'
        )
        parser.add_argument(
            '--test-performance',
            action='store_true',
            help='Test performance with multiple dashboard loads'
        )
        parser.add_argument(
            '--validate-ui',
            action='store_true',
            help='Validate UI data accuracy'
        )

    def handle(self, *args, **options):
        librarian_id = options.get('librarian_id')
        
        if options.get('create_test_data'):
            self.create_test_data(librarian_id)
        
        if options.get('test_cache'):
            self.test_cache_logic(librarian_id)
        
        if options.get('test_performance'):
            self.test_performance(librarian_id)
        
        if options.get('validate_ui'):
            self.validate_ui_data(librarian_id)
        
        # Run all tests if no specific test is requested
        if not any([options.get('create_test_data'), options.get('test_cache'), 
                   options.get('test_performance'), options.get('validate_ui')]):
            self.run_comprehensive_tests(librarian_id)

    def get_test_librarian(self, librarian_id=None):
        """Get librarian for testing"""
        if librarian_id:
            try:
                return Librarian_param.objects.get(id=librarian_id, is_librarian=True)
            except Librarian_param.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'Librarian with ID {librarian_id} not found'))
                return None
        else:
            return Librarian_param.objects.filter(is_librarian=True).first()

    def create_test_data(self, librarian_id=None):
        """Create sample test data for comprehensive testing"""
        self.stdout.write(self.style.SUCCESS('🔧 Creating test data...'))
        
        librarian = self.get_test_librarian(librarian_id)
        if not librarian:
            return

        # Get or create required objects
        course, _ = Courses.objects.get_or_create(name='Test Course')
        state, _ = States.objects.get_or_create(name='Test State')
        
        current_date = timezone.now().date()
        
        with transaction.atomic():
            # Create students for current month (positive growth)
            for i in range(15):
                # Generate unique random values
                random_suffix = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=8))
                random_mobile = random.randint(7000000000, 9999999999)

                student = StudentData.objects.create(
                    librarian=librarian,
                    course=course,
                    name=f'Test Student {i+1}',
                    gender=random.choice(['male', 'female']),
                    email=f'test{random_suffix}@example.com',
                    mobile=random_mobile,
                    locality='Test Area',
                    city='Test City',
                    state=state,
                    registration_date=current_date - timedelta(days=random.randint(0, 30)),
                    registration_fee=5000
                )
                
                # Create invoice for this student
                Invoice.objects.create(
                    student=student,
                    issue_date=student.registration_date,
                    due_date=student.registration_date + timedelta(days=30),
                    total_amount=random.randint(3000, 8000),
                    is_active=True
                )

            # Create students for last month (for comparison)
            last_month_start = current_date - relativedelta(months=1)
            for i in range(10):
                # Generate unique random values
                random_suffix = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=8))
                random_mobile = random.randint(7000000000, 9999999999)

                student = StudentData.objects.create(
                    librarian=librarian,
                    course=course,
                    name=f'Last Month Student {i+1}',
                    gender=random.choice(['male', 'female']),
                    email=f'lastmonth{random_suffix}@example.com',
                    mobile=random_mobile,
                    locality='Test Area',
                    city='Test City',
                    state=state,
                    registration_date=last_month_start + timedelta(days=random.randint(0, 28)),
                    registration_fee=5000
                )

            # Create visitors
            for i in range(20):
                Visitor.objects.create(
                    librarian=librarian,
                    date=current_date - timedelta(days=random.randint(0, 30)),
                    name=f'Test Visitor {i+1}',
                    contact=7000000000 + i,
                    email=f'visitor{i+1}@example.com',
                    notes='Test visitor inquiry',
                    status=random.choice(['pending', 'completed', 'cancelled'])
                )

            # Create some invoices due today
            for i in range(3):
                student = StudentData.objects.filter(librarian=librarian).order_by('?').first()
                if student:
                    Invoice.objects.create(
                        student=student,
                        issue_date=current_date - timedelta(days=15),
                        due_date=current_date,
                        total_amount=random.randint(2000, 6000),
                        is_active=True
                    )

        self.stdout.write(self.style.SUCCESS(f'✅ Test data created for {librarian.library_name}'))

    def test_cache_logic(self, librarian_id=None):
        """Test cache invalidation and refresh logic"""
        self.stdout.write(self.style.SUCCESS('🧪 Testing cache logic...'))
        
        librarian = self.get_test_librarian(librarian_id)
        if not librarian:
            return

        # Test 1: Initial calculation
        self.stdout.write('📊 Test 1: Initial analytics calculation')
        analytics1 = calculate_dashboard_analytics(librarian)
        cache = AnalyticsCache.objects.get(librarian=librarian)
        
        self.stdout.write(f'   Students this month: {analytics1["students_this_month"]}')
        self.stdout.write(f'   Cache last calculated: {cache.last_calculated}')
        self.stdout.write(f'   Cache needs refresh: {cache.needs_recalculation()}')

        # Test 2: Immediate recalculation (should use cache)
        self.stdout.write('\n📊 Test 2: Immediate recalculation (should use cache)')
        start_time = timezone.now()
        analytics2 = calculate_dashboard_analytics(librarian)
        end_time = timezone.now()
        
        cache.refresh_from_db()
        self.stdout.write(f'   Calculation time: {(end_time - start_time).total_seconds():.3f}s')
        self.stdout.write(f'   Used cache: {analytics1 == analytics2}')

        # Test 3: Force refresh
        self.stdout.write('\n📊 Test 3: Force refresh')
        cache.force_refresh()
        analytics3 = calculate_dashboard_analytics(librarian)
        cache.refresh_from_db()
        
        self.stdout.write(f'   Force flag cleared: {not cache.force_recalculate}')
        self.stdout.write(f'   New calculation time: {cache.last_calculated}')

        self.stdout.write(self.style.SUCCESS('✅ Cache logic tests completed'))

    def test_performance(self, librarian_id=None):
        """Test performance with multiple dashboard loads"""
        self.stdout.write(self.style.SUCCESS('⚡ Testing performance...'))
        
        librarian = self.get_test_librarian(librarian_id)
        if not librarian:
            return

        # Test multiple rapid calls
        times = []
        for i in range(10):
            start_time = timezone.now()
            analytics = calculate_dashboard_analytics(librarian)
            end_time = timezone.now()
            times.append((end_time - start_time).total_seconds())

        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)

        self.stdout.write(f'📈 Performance Results (10 calls):')
        self.stdout.write(f'   Average time: {avg_time:.3f}s')
        self.stdout.write(f'   Min time: {min_time:.3f}s')
        self.stdout.write(f'   Max time: {max_time:.3f}s')
        
        if avg_time < 0.1:
            self.stdout.write(self.style.SUCCESS('✅ Performance: Excellent (using cache)'))
        elif avg_time < 0.5:
            self.stdout.write(self.style.WARNING('⚠️  Performance: Good'))
        else:
            self.stdout.write(self.style.ERROR('❌ Performance: Needs optimization'))

    def validate_ui_data(self, librarian_id=None):
        """Validate UI data accuracy"""
        self.stdout.write(self.style.SUCCESS('🎨 Validating UI data accuracy...'))
        
        librarian = self.get_test_librarian(librarian_id)
        if not librarian:
            return

        analytics = calculate_dashboard_analytics(librarian)
        
        # Validate data types and ranges
        validations = [
            ('students_this_month', analytics['students_this_month'], int, lambda x: x >= 0),
            ('students_growth_percent', analytics['students_growth_percent'], (int, float), lambda x: True),
            ('new_registrations_this_month', analytics['new_registrations_this_month'], int, lambda x: x >= 0),
            ('todays_collection', analytics['todays_collection'], (int, float), lambda x: x >= 0),
            ('growth_months', analytics['growth_months'], list, lambda x: len(x) >= 0),
            ('growth_counts', analytics['growth_counts'], list, lambda x: len(x) >= 0),
        ]

        all_valid = True
        for field, value, expected_type, validator in validations:
            type_valid = isinstance(value, expected_type)
            range_valid = validator(value)
            
            if type_valid and range_valid:
                self.stdout.write(f'   ✅ {field}: {value} (valid)')
            else:
                self.stdout.write(f'   ❌ {field}: {value} (invalid)')
                all_valid = False

        # Test growth indicators
        growth_positive = analytics['students_growth_positive']
        growth_percent = analytics['students_growth_percent']
        
        if (growth_percent > 0 and growth_positive) or (growth_percent <= 0 and not growth_positive):
            self.stdout.write('   ✅ Growth indicators: Consistent')
        else:
            self.stdout.write('   ❌ Growth indicators: Inconsistent')
            all_valid = False

        if all_valid:
            self.stdout.write(self.style.SUCCESS('✅ All UI data validations passed'))
        else:
            self.stdout.write(self.style.ERROR('❌ Some UI data validations failed'))

    def run_comprehensive_tests(self, librarian_id=None):
        """Run all tests in sequence"""
        self.stdout.write(self.style.SUCCESS('🧪 Running comprehensive analytics tests...\n'))
        
        self.create_test_data(librarian_id)
        self.stdout.write('')
        
        self.test_cache_logic(librarian_id)
        self.stdout.write('')
        
        self.test_performance(librarian_id)
        self.stdout.write('')
        
        self.validate_ui_data(librarian_id)
        
        self.stdout.write(self.style.SUCCESS('\n🎉 All tests completed!'))
