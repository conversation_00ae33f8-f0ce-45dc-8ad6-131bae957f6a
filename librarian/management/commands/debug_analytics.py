from django.core.management.base import BaseCommand
from django.utils import timezone
from dateutil.relativedelta import relativedelta
from datetime import timedelta
from django.db.models import Sum, Count
from librarian.models import Librarian_param
from studentsData.models import StudentData, Invoice


class Command(BaseCommand):
    help = 'Debug analytics calculations'

    def add_arguments(self, parser):
        parser.add_argument('--librarian-id', type=int, help='Librarian ID to debug')

    def handle(self, *args, **options):
        librarian_id = options.get('librarian_id')
        
        if librarian_id:
            try:
                librarian = Librarian_param.objects.get(id=librarian_id)
            except Librarian_param.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'Librarian with ID {librarian_id} not found'))
                return
        else:
            # Get first librarian
            librarian = Librarian_param.objects.first()
            if not librarian:
                self.stdout.write(self.style.ERROR('No librarians found'))
                return

        self.stdout.write(f'🔍 Debugging analytics for: {librarian.user.first_name} {librarian.user.last_name} (ID: {librarian.id})')
        
        # Date calculations
        current_date = timezone.now().date()
        current_month_start = timezone.now().replace(day=1).date()
        last_month_start = (current_month_start - relativedelta(months=1))
        last_month_end = current_month_start - timedelta(days=1)
        
        self.stdout.write(f'📅 Current date: {current_date}')
        self.stdout.write(f'📅 Current month start: {current_month_start}')
        self.stdout.write(f'📅 Last month: {last_month_start} to {last_month_end}')
        
        # Get all students for this librarian
        all_students = StudentData.objects.filter(librarian=librarian)
        self.stdout.write(f'👥 Total students: {all_students.count()}')
        
        # 1. Students This Month (who paid fees this month)
        current_month_invoices = Invoice.objects.filter(
            student__librarian=librarian,
            issue_date__gte=current_month_start,
            is_active=True
        )
        students_this_month = current_month_invoices.values('student').distinct().count()
        
        self.stdout.write(f'📊 Current month invoices: {current_month_invoices.count()}')
        self.stdout.write(f'📊 Students this month (with invoices): {students_this_month}')
        
        # Last month's students who paid for comparison
        last_month_invoices = Invoice.objects.filter(
            student__librarian=librarian,
            issue_date__gte=last_month_start,
            issue_date__lte=last_month_end,
            is_active=True
        )
        students_last_month = last_month_invoices.values('student').distinct().count()
        
        self.stdout.write(f'📊 Last month invoices: {last_month_invoices.count()}')
        self.stdout.write(f'📊 Students last month (with invoices): {students_last_month}')
        
        # Calculate percentage growth
        if students_last_month > 0:
            students_growth_percent = round(((students_this_month - students_last_month) / students_last_month) * 100, 1)
        else:
            students_growth_percent = 100 if students_this_month > 0 else 0
            
        self.stdout.write(f'📈 Student growth percentage: {students_growth_percent}%')
        
        # 2. New Registrations This Month
        new_registrations_this_month = all_students.filter(
            registration_date__gte=current_month_start
        ).count()
        
        new_registrations_last_month = all_students.filter(
            registration_date__gte=last_month_start,
            registration_date__lte=last_month_end
        ).count()
        
        self.stdout.write(f'🆕 New registrations this month: {new_registrations_this_month}')
        self.stdout.write(f'🆕 New registrations last month: {new_registrations_last_month}')
        
        # 3. Today's Collection (invoices due today)
        todays_collection_due = Invoice.objects.filter(
            student__librarian=librarian,
            due_date=current_date,
            is_active=True
        ).aggregate(total=Sum('total_amount'))['total'] or 0
        
        self.stdout.write(f'💰 Today\'s collection (due today): ₹{todays_collection_due}')
        
        # Alternative: Today's payments (invoices issued today)
        todays_payments = Invoice.objects.filter(
            student__librarian=librarian,
            issue_date=current_date,
            is_active=True
        ).aggregate(total=Sum('total_amount'))['total'] or 0
        
        self.stdout.write(f'💰 Today\'s payments (issued today): ₹{todays_payments}')
        
        # Show recent invoices for context
        recent_invoices = Invoice.objects.filter(
            student__librarian=librarian,
            is_active=True
        ).order_by('-issue_date')[:5]
        
        self.stdout.write(f'\n📋 Recent invoices:')
        for invoice in recent_invoices:
            self.stdout.write(f'  - {invoice.student.name}: ₹{invoice.total_amount} (issued: {invoice.issue_date}, due: {invoice.due_date})')
        
        # Show recent students for context
        recent_students = all_students.order_by('-registration_date')[:5]
        
        self.stdout.write(f'\n👥 Recent students:')
        for student in recent_students:
            self.stdout.write(f'  - {student.name}: registered {student.registration_date}')
