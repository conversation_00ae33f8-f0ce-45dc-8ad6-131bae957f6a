from django.core.management.base import BaseCommand
from django.utils import timezone
from librarian.models import Librarian_param, AnalyticsCache
from studentsData.models import StudentData, Invoice, Shift, Months
from datetime import timedelta


class Command(BaseCommand):
    help = 'Test dashboard analytics fix'

    def add_arguments(self, parser):
        parser.add_argument('--create-test-invoice', action='store_true', help='Create a test invoice for today')
        parser.add_argument('--librarian-id', type=int, default=8, help='Librarian ID to test')

    def handle(self, *args, **options):
        librarian_id = options.get('librarian_id', 8)
        
        try:
            librarian = Librarian_param.objects.get(id=librarian_id)
        except Librarian_param.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'Librarian with ID {librarian_id} not found'))
            return

        self.stdout.write(f'🧪 Testing dashboard analytics for: {librarian.user.first_name} {librarian.user.last_name}')
        
        if options.get('create_test_invoice'):
            self.create_test_invoice(librarian)
        
        # Clear cache to force recalculation
        AnalyticsCache.objects.filter(librarian=librarian).delete()
        self.stdout.write('🗑️ Cleared analytics cache')
        
        # Test analytics calculation
        from librarian.views import calculate_dashboard_analytics
        
        self.stdout.write('📊 Calculating fresh analytics...')
        analytics = calculate_dashboard_analytics(librarian)
        
        self.stdout.write('📈 Analytics Results:')
        self.stdout.write(f'  Students this month: {analytics["students_this_month"]}')
        self.stdout.write(f'  Student growth: {analytics["students_growth_percent"]}% ({"positive" if analytics["students_growth_positive"] else "negative"})')
        self.stdout.write(f'  New registrations: {analytics["new_registrations_this_month"]}')
        self.stdout.write(f'  Today\'s collection: ₹{analytics["todays_collection"]}')
        
        # Test template context
        self.stdout.write('\n🎨 Template context test:')
        context = {
            'students_this_month': analytics["students_this_month"],
            'students_growth_percent': analytics["students_growth_percent"],
            'students_growth_positive': analytics["students_growth_positive"],
            'todays_collection': analytics["todays_collection"],
        }
        
        for key, value in context.items():
            self.stdout.write(f'  {key}: {value} (type: {type(value).__name__})')
        
        # Test template logic
        self.stdout.write('\n🔍 Template logic test:')
        if context['students_growth_percent'] != 0:
            if context['students_growth_positive']:
                self.stdout.write(f'  ✅ Should show: +{context["students_growth_percent"]}% (green, up arrow)')
            else:
                self.stdout.write(f'  ✅ Should show: {context["students_growth_percent"]}% (red, down arrow)')
        else:
            self.stdout.write('  ✅ Should show: No change from last month')
            
        if context['todays_collection'] > 0:
            self.stdout.write(f'  ✅ Should show: ₹{context["todays_collection"]} - Collected today')
        else:
            self.stdout.write('  ✅ Should show: ₹0 - No payments received today')

    def create_test_invoice(self, librarian):
        """Create a test invoice for today to test today's collection"""
        self.stdout.write('💰 Creating test invoice for today...')
        
        # Get a student for this librarian
        student = StudentData.objects.filter(librarian=librarian).first()
        if not student:
            self.stdout.write(self.style.ERROR('No students found for this librarian'))
            return
            
        # Get shifts and months
        shift = Shift.objects.filter(librarian=librarian).first()
        month = Months.objects.first()
        
        if not shift:
            self.stdout.write(self.style.ERROR('No shifts found for this librarian'))
            return
            
        if not month:
            self.stdout.write(self.style.ERROR('No months found'))
            return
        
        # Create invoice for today
        current_date = timezone.now().date()
        due_date = current_date + timedelta(days=30)
        
        invoice = Invoice.objects.create(
            student=student,
            issue_date=current_date,
            due_date=due_date,
            total_amount=1000,
            discount_amount=0,
            mode_pay='Cash',
            description='Test invoice for dashboard analytics'
        )
        invoice.shift.add(shift)
        invoice.months.add(month)
        
        self.stdout.write(f'✅ Created test invoice: {invoice.invoice_id} for ₹{invoice.total_amount}')
        self.stdout.write(f'   Student: {student.name}')
        self.stdout.write(f'   Issue date: {invoice.issue_date}')
        self.stdout.write(f'   Due date: {invoice.due_date}')
