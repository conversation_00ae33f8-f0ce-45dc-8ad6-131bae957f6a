"""
Notification Templates Configuration
Defines all notification templates for different events in the LMS
"""

# Comprehensive notification templates for all events
NOTIFICATION_TEMPLATES = {
    # QR Registration notification
    'qr_registration': {
        'title': '📝 New QR Registration',
        'body': 'New student registration: {student_name}\nCourse: {course}\nEmail: {student_email}',
        'category': 'registration',
        'priority': 'high',
        'icon': '/favicon.ico'
    },
    
    # Visitor callback notifications
    'visitor_callback_due': {
        'title': '📞 Visitor Callback Due Today',
        'body': 'Callback scheduled for: {visitor_name}\nMobile: {visitor_mobile}\nDate: {callback_date}',
        'category': 'visitor',
        'priority': 'high',
        'icon': '/favicon.ico'
    },
    
    # Admission processed notifications
    'admission_processed': {
        'title': '✅ Admission Processed',
        'body': 'Student: {student_name}\nProcessed by: {processed_by}\nDate: {admission_date}',
        'category': 'admission',
        'priority': 'normal',
        'icon': '/favicon.ico'
    },
    
    # Member expiry notifications
    'member_expiry_10_days': {
        'title': '⏰ Member Expiry Warning - 10 Days',
        'body': 'Member: {member_name}\nExpires: {expiry_date}\nMobile: {member_mobile}',
        'category': 'membership',
        'priority': 'normal',
        'icon': '/favicon.ico'
    },
    'member_expiry_5_days': {
        'title': '⚠️ Member Expiry Warning - 5 Days',
        'body': 'Member: {member_name}\nExpires: {expiry_date}\nMobile: {member_mobile}',
        'category': 'membership',
        'priority': 'high',
        'icon': '/favicon.ico'
    },
    'member_expiry_1_day': {
        'title': '🚨 Member Expiry Warning - 1 Day',
        'body': 'Member: {member_name}\nExpires: {expiry_date}\nMobile: {member_mobile}',
        'category': 'membership',
        'priority': 'urgent',
        'icon': '/favicon.ico'
    },
    'member_expired': {
        'title': '❌ Member Expired Today',
        'body': 'Member: {member_name}\nExpired: {expiry_date}\nMobile: {member_mobile}',
        'category': 'membership',
        'priority': 'urgent',
        'icon': '/favicon.ico'
    },
    
    # Invoice created notifications
    'invoice_created': {
        'title': '💰 New Invoice Created',
        'body': 'Invoice: {invoice_number}\nStudent: {student_name}\nAmount: ₹{amount}\nCreated by: {created_by}',
        'category': 'financial',
        'priority': 'normal',
        'icon': '/favicon.ico'
    },
    
    # Sales milestone notifications
    'sales_milestone_reached': {
        'title': '🎉 Sales Milestone Reached!',
        'body': 'Milestone: {milestone_amount}\nCurrent Sales: {current_sales}\nMonth: {month}',
        'category': 'financial',
        'priority': 'high',
        'icon': '/favicon.ico'
    },
    
    # Monthly sales summary
    'monthly_sales_summary': {
        'title': '📊 Monthly Sales Summary',
        'body': 'Month: {month}\nTotal Sales: {total_sales}\nInvoices: {invoice_count}\nAverage: {average_invoice}',
        'category': 'financial',
        'priority': 'normal',
        'icon': '/favicon.ico'
    },
    
    # Visitor added notifications
    'visitor_added': {
        'title': '👥 New Visitor Added',
        'body': 'Visitor: {visitor_name}\nMobile: {visitor_mobile}\nPurpose: {visitor_purpose}\nAdded by: {added_by}',
        'category': 'visitor',
        'priority': 'normal',
        'icon': '/favicon.ico'
    },
    
    # Daily galla reminder
    'daily_galla_reminder': {
        'title': '📝 Daily Galla Submission Reminder',
        'body': 'Hello {user_name},\nPlease submit your daily galla for {day_name}, {reminder_date}',
        'category': 'reminder',
        'priority': 'normal',
        'icon': '/favicon.ico'
    },
    
    # Custom admin notifications
    'custom_admin_notification': {
        'title': '{custom_title}',
        'body': '{custom_message}\n\nSent by: {sender}\nDate: {sent_date}',
        'category': 'admin',
        'priority': 'normal',  # Will be overridden by actual priority
        'icon': '/favicon.ico'
    },
    
    # Payment notifications
    'payment_received': {
        'title': '💰 Payment Received',
        'body': 'Amount: ₹{amount}\nFrom: {student_name}\nInvoice: {invoice_number}\nMethod: {payment_method}',
        'category': 'financial',
        'priority': 'normal',
        'icon': '/favicon.ico'
    },
    
    # Facility booking notifications
    'facility_booked': {
        'title': '📚 Facility Booking',
        'body': 'Facility: {facility_name}\nBooked by: {student_name}\nTime: {booking_time}\nDate: {booking_date}',
        'category': 'facility',
        'priority': 'normal',
        'icon': '/favicon.ico'
    },
    
    # System notifications
    'system_maintenance': {
        'title': '🔧 System Maintenance',
        'body': 'Scheduled maintenance: {maintenance_date}\nDuration: {duration}\nAffected services: {services}',
        'category': 'system',
        'priority': 'high',
        'icon': '/favicon.ico'
    },
    
    # Library notifications
    'book_overdue': {
        'title': '📚 Book Overdue',
        'body': 'Student: {student_name}\nBook: {book_title}\nDue date: {due_date}\nDays overdue: {days_overdue}',
        'category': 'library',
        'priority': 'normal',
        'icon': '/favicon.ico'
    },
    
    'book_reserved': {
        'title': '📖 Book Reserved',
        'body': 'Book: {book_title}\nReserved by: {student_name}\nReservation date: {reservation_date}',
        'category': 'library',
        'priority': 'normal',
        'icon': '/favicon.ico'
    },
    
    # Staff notifications
    'staff_attendance': {
        'title': '👥 Staff Attendance Alert',
        'body': 'Staff: {staff_name}\nStatus: {attendance_status}\nDate: {attendance_date}\nTime: {attendance_time}',
        'category': 'staff',
        'priority': 'normal',
        'icon': '/favicon.ico'
    },
    
    # Emergency notifications
    'emergency_alert': {
        'title': '🚨 EMERGENCY ALERT',
        'body': 'Emergency: {emergency_type}\nLocation: {location}\nInstructions: {instructions}\nTime: {alert_time}',
        'category': 'emergency',
        'priority': 'urgent',
        'icon': '/favicon.ico'
    },
}

# Event type choices for Django models
EVENT_TYPE_CHOICES = [
    ('qr_registration', 'QR Registration'),
    ('visitor_callback_due', 'Visitor Callback Due'),
    ('admission_processed', 'Admission Processed'),
    ('member_expiry_10_days', 'Member Expiry - 10 Days'),
    ('member_expiry_5_days', 'Member Expiry - 5 Days'),
    ('member_expiry_1_day', 'Member Expiry - 1 Day'),
    ('member_expired', 'Member Expired'),
    ('invoice_created', 'Invoice Created'),
    ('sales_milestone_reached', 'Sales Milestone Reached'),
    ('monthly_sales_summary', 'Monthly Sales Summary'),
    ('visitor_added', 'Visitor Added'),
    ('daily_galla_reminder', 'Daily Galla Reminder'),
    ('custom_admin_notification', 'Custom Admin Notification'),
    ('payment_received', 'Payment Received'),
    ('facility_booked', 'Facility Booked'),
    ('system_maintenance', 'System Maintenance'),
    ('book_overdue', 'Book Overdue'),
    ('book_reserved', 'Book Reserved'),
    ('staff_attendance', 'Staff Attendance'),
    ('emergency_alert', 'Emergency Alert'),
]

# Priority levels
PRIORITY_CHOICES = [
    ('low', 'Low'),
    ('normal', 'Normal'),
    ('high', 'High'),
    ('urgent', 'Urgent'),
]

# Category choices
CATEGORY_CHOICES = [
    ('registration', 'Registration'),
    ('visitor', 'Visitor'),
    ('admission', 'Admission'),
    ('membership', 'Membership'),
    ('financial', 'Financial'),
    ('reminder', 'Reminder'),
    ('admin', 'Admin'),
    ('facility', 'Facility'),
    ('system', 'System'),
    ('library', 'Library'),
    ('staff', 'Staff'),
    ('emergency', 'Emergency'),
]


def get_notification_template(event_type):
    """Get notification template for a specific event type"""
    return NOTIFICATION_TEMPLATES.get(event_type, {
        'title': 'Notification',
        'body': 'You have a new notification.',
        'category': 'general',
        'priority': 'normal',
        'icon': '/favicon.ico'
    })


def format_notification(event_type, **kwargs):
    """Format notification template with provided data"""
    template = get_notification_template(event_type)
    
    try:
        formatted_template = {
            'title': template['title'].format(**kwargs),
            'body': template['body'].format(**kwargs),
            'category': template['category'],
            'priority': kwargs.get('priority', template['priority']),
            'icon': template['icon']
        }
        return formatted_template
    except KeyError as e:
        # Handle missing template variables
        return {
            'title': f'Notification - {event_type}',
            'body': f'Notification data: {kwargs}',
            'category': template.get('category', 'general'),
            'priority': template.get('priority', 'normal'),
            'icon': template.get('icon', '/favicon.ico')
        }


def get_all_event_types():
    """Get all available event types"""
    return list(NOTIFICATION_TEMPLATES.keys())


def get_events_by_category(category):
    """Get all events for a specific category"""
    return [
        event_type for event_type, template in NOTIFICATION_TEMPLATES.items()
        if template.get('category') == category
    ]


def get_urgent_events():
    """Get all urgent priority events"""
    return [
        event_type for event_type, template in NOTIFICATION_TEMPLATES.items()
        if template.get('priority') == 'urgent'
    ]
