import firebase_admin
from firebase_admin import credentials
from django.conf import settings
import os
import logging
import json

logger = logging.getLogger(__name__)

def initialize_firebase():
    """Initialize Firebase Admin SDK using environment variables"""
    try:
        # Check if Firebase is already initialized
        if firebase_admin._apps:
            logger.info("Firebase already initialized")
            return True

        project_id = os.getenv("FIREBASE_PROJECT_ID")
        if not project_id:
            logger.error("FIREBASE_PROJECT_ID not found in environment variables")
            return False

        # Try different initialization methods

        # Method 1: Try with service account file if it exists
        service_account_path = os.path.join(settings.BASE_DIR, 'firebase-service-account.json')
        if os.path.exists(service_account_path):
            cred = credentials.Certificate(service_account_path)
            firebase_admin.initialize_app(cred)
            logger.info("Firebase initialized with service account file")
            return True

        # Method 2: Try with environment variable JSON
        service_account_json = os.getenv("FIREBASE_SERVICE_ACCOUNT_JSON")
        if service_account_json:
            service_account_info = json.loads(service_account_json)
            cred = credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(cred)
            logger.info("Firebase initialized with service account JSON from environment")
            return True

        # Method 3: Try with Application Default Credentials (for Google Cloud)
        try:
            cred = credentials.ApplicationDefault()
            firebase_admin.initialize_app(cred, {
                'projectId': project_id
            })
            logger.info("Firebase initialized with Application Default Credentials")
            return True
        except Exception as adc_error:
            logger.debug(f"Application Default Credentials failed: {adc_error}")

        # Method 4: Initialize without credentials for testing (limited functionality)
        logger.warning("Initializing Firebase in test mode (limited functionality)")
        firebase_admin.initialize_app(options={'projectId': project_id})
        logger.info("Firebase initialized in test mode")
        return True

    except Exception as e:
        logger.warning(f"Firebase initialization failed: {e}")
        logger.info("Firebase will use fallback mode for notifications")
        return False

def get_firebase_app():
    """Get the Firebase app instance"""
    try:
        return firebase_admin.get_app()
    except ValueError:
        # App not initialized, try to initialize
        if initialize_firebase():
            return firebase_admin.get_app()
        return None

# Initialize Firebase when module is imported
firebase_initialized = initialize_firebase()
