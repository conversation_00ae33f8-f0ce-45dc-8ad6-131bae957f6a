{"version": 3, "file": "bootstrap.esm.js", "sources": ["../../js/src/dom/selector-engine.js", "../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/base-component.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/util/scrollbar.js", "../../js/src/util/backdrop.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children)\n      .filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (ancestor.matches(selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "import SelectorEngine from '../dom/selector-engine'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLower<PERSON>ase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return SelectorEngine.findOne(obj)\n  }\n\n  return null\n}\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst customEventsRegex = /^(mouseenter|mouseleave)/i\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            // eslint-disable-next-line unicorn/consistent-destructuring\n            EventHandler.off(element, event.type, selector, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  let typeEvent = getTypeEvent(originalTypeEvent)\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (customEventsRegex.test(originalTypeEvent)) {\n    const wrapFn = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    if (delegationFn) {\n      delegationFn = wrapFn(delegationFn)\n    } else {\n      handler = wrapFn(handler)\n    }\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\nimport {\n  emulateTransitionEnd,\n  execute,\n  getElement,\n  getTransitionDurationFromElement\n} from './util/index'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.0.1'\n\nclass BaseComponent {\n  constructor(element) {\n    element = getElement(element)\n\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    Object.getOwnPropertyNames(this).forEach(propertyName => {\n      this[propertyName] = null\n    })\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    if (!isAnimated) {\n      execute(callback)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n    EventHandler.one(element, 'transitionend', () => execute(callback))\n\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.get(element, this.DATA_KEY)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-bs-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_ALERT = 'alert'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASS_NAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(element), element, isAnimated)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.get(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toL<PERSON>er<PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_INDICATOR = '[data-bs-target]'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(ORDER_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(ORDER_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const order = index > activeIndex ?\n      ORDER_NEXT :\n      ORDER_PREV\n\n    this._slide(order, this._items[index])\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    this._slide(direction > 0 ? DIRECTION_RIGHT : DIRECTION_LEFT)\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      this.touchDeltaX = event.touches && event.touches.length > 1 ?\n        0 :\n        event.touches[0].clientX - this.touchStartX\n    }\n\n    const end = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    if (event.key === ARROW_LEFT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_RIGHT)\n    } else if (event.key === ARROW_RIGHT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_LEFT)\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByOrder(order, activeElement) {\n    const isNext = order === ORDER_NEXT\n    const isPrev = order === ORDER_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrev && activeIndex === 0) || (isNext && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = isPrev ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n      activeIndicator.removeAttribute('aria-current')\n\n      const indicators = SelectorEngine.find(SELECTOR_INDICATOR, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        if (Number.parseInt(indicators[i].getAttribute('data-bs-slide-to'), 10) === this._getItemIndex(element)) {\n          indicators[i].classList.add(CLASS_NAME_ACTIVE)\n          indicators[i].setAttribute('aria-current', 'true')\n          break\n        }\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(directionOrOrder, element) {\n    const order = this._directionToOrder(directionOrOrder)\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || this._getItemByOrder(order, activeElement)\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    const isNext = order === ORDER_NEXT\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n    const eventDirectionName = this._orderToDirection(order)\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    const triggerSlidEvent = () => {\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const completeCallBack = () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(triggerSlidEvent, 0)\n      }\n\n      this._queueCallback(completeCallBack, activeElement, true)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      triggerSlidEvent()\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (![DIRECTION_RIGHT, DIRECTION_LEFT].includes(direction)) {\n      return direction\n    }\n\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (![ORDER_NEXT, ORDER_PREV].includes(order)) {\n      return order\n    }\n\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.get(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.get(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getSelectorFromElement,\n  getElementFromSelector,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${this._element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-bs-target=\"#${this._element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-bs-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Data.get(tempActiveData, DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.set(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    parent = getElement(parent)\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-bs-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.get(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getElementFromSelector,\n  isDisabled,\n  isElement,\n  isVisible,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null,\n  autoClose: true\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)',\n  autoClose: '(boolean|string)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (isDisabled(this._element)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    if (isActive) {\n      this.hide()\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (this._inNavbar) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'none')\n    } else {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = getElement(this._config.reference)\n      } else if (typeof this._config.reference === 'object') {\n        referenceElement = this._config.reference\n      }\n\n      const popperConfig = this._getPopperConfig()\n      const isDisplayStatic = popperConfig.modifiers.find(modifier => modifier.name === 'applyStyles' && modifier.enabled === false)\n\n      this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n\n      if (isDisplayStatic) {\n        Manipulator.setDataAttribute(this._menu, 'popper', 'static')\n      }\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', noop))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      this.toggle()\n    })\n  }\n\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.off(elem, 'mouseover', noop))\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _selectMenuItem(event) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    // Up\n    if (event.key === ARROW_UP_KEY && index > 0) {\n      index--\n    }\n\n    // Down\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) {\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const context = Data.get(toggles[i], DATA_KEY)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      if (!context._element.classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      const relatedTarget = {\n        relatedTarget: context._element\n      }\n\n      if (event) {\n        const composedPath = event.composedPath()\n        const isMenuTarget = composedPath.includes(context._menu)\n        if (\n          composedPath.includes(context._element) ||\n          (context._config.autoClose === 'inside' && !isMenuTarget) ||\n          (context._config.autoClose === 'outside' && isMenuTarget)\n        ) {\n          continue\n        }\n\n        // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n        if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n          continue\n        }\n\n        if (event.type === 'click') {\n          relatedTarget.clickEvent = event\n        }\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (!isActive && event.key === ESCAPE_KEY) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const getToggleButton = () => this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n\n    if (event.key === ESCAPE_KEY) {\n      getToggleButton().focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive && (event.key === ARROW_UP_KEY || event.key === ARROW_DOWN_KEY)) {\n      getToggleButton().click()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    Dropdown.getInstance(getToggleButton())._selectMenuItem(event)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.dropdownInterface(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nconst getWidth = () => {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = document.documentElement.clientWidth\n  return Math.abs(window.innerWidth - documentWidth)\n}\n\nconst hide = (width = getWidth()) => {\n  _disableOverFlow()\n  // give padding to element to balances the hidden scrollbar width\n  _setElementAttributes('body', 'paddingRight', calculatedValue => calculatedValue + width)\n  // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements, to keep shown fullwidth\n  _setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n  _setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n}\n\nconst _disableOverFlow = () => {\n  const actualValue = document.body.style.overflow\n  if (actualValue) {\n    Manipulator.setDataAttribute(document.body, 'overflow', actualValue)\n  }\n\n  document.body.style.overflow = 'hidden'\n}\n\nconst _setElementAttributes = (selector, styleProp, callback) => {\n  const scrollbarWidth = getWidth()\n  SelectorEngine.find(selector)\n    .forEach(element => {\n      if (element !== document.body && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      const actualValue = element.style[styleProp]\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n      element.style[styleProp] = `${callback(Number.parseFloat(calculatedValue))}px`\n    })\n}\n\nconst reset = () => {\n  _resetElementAttributes('body', 'overflow')\n  _resetElementAttributes('body', 'paddingRight')\n  _resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n  _resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n}\n\nconst _resetElementAttributes = (selector, styleProp) => {\n  SelectorEngine.find(selector).forEach(element => {\n    const value = Manipulator.getDataAttribute(element, styleProp)\n    if (typeof value === 'undefined') {\n      element.style.removeProperty(styleProp)\n    } else {\n      Manipulator.removeDataAttribute(element, styleProp)\n      element.style[styleProp] = value\n    }\n  })\n}\n\nconst isBodyOverflowing = () => {\n  return getWidth() > 0\n}\n\nexport {\n  getWidth,\n  hide,\n  isBodyOverflowing,\n  reset\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { emulateTransitionEnd, execute, getTransitionDurationFromElement, reflow, typeCheckConfig } from './index'\n\nconst Default = {\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  isAnimated: false,\n  rootElement: document.body, // give the choice to place backdrop under different elements\n  clickCallback: null\n}\n\nconst DefaultType = {\n  isVisible: 'boolean',\n  isAnimated: 'boolean',\n  rootElement: 'element',\n  clickCallback: '(function|null)'\n}\nconst NAME = 'backdrop'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nclass Backdrop {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    if (this._config.isAnimated) {\n      reflow(this._getElement())\n    }\n\n    this._getElement().classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  // Private\n\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = CLASS_NAME_BACKDROP\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n\n    config.rootElement = config.rootElement || document.body\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    this._config.rootElement.appendChild(this._getElement())\n\n    EventHandler.on(this._getElement(), EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._getElement().parentNode.removeChild(this._element)\n    this._isAppended = false\n  }\n\n  _emulateAnimation(callback) {\n    if (!this._config.isAnimated) {\n      execute(callback)\n      return\n    }\n\n    const backdropTransitionDuration = getTransitionDurationFromElement(this._getElement())\n    EventHandler.one(this._getElement(), 'transitionend', () => execute(callback))\n    emulateTransitionEnd(this._getElement(), backdropTransitionDuration)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isRTL,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport { getWidth as getScroll<PERSON><PERSON><PERSON>idth, hide as scrollBarHide, reset as scrollBarReset } from './util/scrollbar'\nimport BaseComponent from './base-component'\nimport Backdrop from './util/backdrop'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"modal\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._isShown = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._isAnimated()) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    scrollBarHide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, event => this.hide(event))\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const isAnimated = this._isAnimated()\n\n    if (isAnimated) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    this._queueCallback(() => this._hideModal(), this._element, isAnimated)\n  }\n\n  dispose() {\n    [window, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    this._backdrop.dispose()\n    super.dispose()\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const isAnimated = this._isAnimated()\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (isAnimated) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, isAnimated)\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      scrollBarReset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _showBackdrop(callback) {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n      if (this._ignoreBackdropClick) {\n        this._ignoreBackdropClick = false\n        return\n      }\n\n      if (event.target !== event.currentTarget) {\n        return\n      }\n\n      if (this._config.backdrop === true) {\n        this.hide()\n      } else if (this._config.backdrop === 'static') {\n        this._triggerBackdropTransition()\n      }\n    })\n\n    this._backdrop.show(callback)\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n    EventHandler.off(this._element, 'transitionend')\n    EventHandler.one(this._element, 'transitionend', () => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        EventHandler.one(this._element, 'transitionend', () => {\n          this._element.style.overflowY = ''\n        })\n        emulateTransitionEnd(this._element, modalTransitionDuration)\n      }\n    })\n    emulateTransitionEnd(this._element, modalTransitionDuration)\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = getScrollBarWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if ((!isBodyOverflowing && isModalOverflowing && !isRTL()) || (isBodyOverflowing && !isModalOverflowing && isRTL())) {\n      this._element.style.paddingLeft = `${scrollbarWidth}px`\n    }\n\n    if ((isBodyOverflowing && !isModalOverflowing && !isRTL()) || (!isBodyOverflowing && isModalOverflowing && isRTL())) {\n      this._element.style.paddingRight = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getInstance(this) || new Modal(this, typeof config === 'object' ? config : {})\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  const data = Modal.getInstance(target) || new Modal(target)\n\n  data.toggle(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  isVisible,\n  typeCheckConfig\n} from './util/index'\nimport { hide as scrollBarHide, reset as scrollBarReset } from './util/scrollbar'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Manipulator from './dom/manipulator'\nimport Backdrop from './util/backdrop'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: 'boolean',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\nconst CLASS_NAME_SHOW = 'show'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"offcanvas\"]'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._element.style.visibility = 'visible'\n\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      scrollBarHide()\n      this._enforceFocusOnElement(this._element)\n    }\n\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const completeCallBack = () => {\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.setAttribute('aria-hidden', true)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n      this._element.style.visibility = 'hidden'\n\n      if (!this._config.scroll) {\n        scrollBarReset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    super.dispose()\n    EventHandler.off(document, EVENT_FOCUSIN)\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: this._config.backdrop,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: () => this.hide()\n    })\n  }\n\n  _enforceFocusOnElement(element) {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n        element !== event.target &&\n        !element.contains(event.target)) {\n        element.focus()\n      }\n    })\n    element.focus()\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (this._config.keyboard && event.key === ESCAPE_KEY) {\n        this.hide()\n      }\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Offcanvas(this, typeof config === 'object' ? config : {})\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const allReadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (allReadyOpen && allReadyOpen !== target) {\n    Offcanvas.getInstance(allReadyOpen).hide()\n  }\n\n  const data = Data.get(target, DATA_KEY) || new Offcanvas(target)\n\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(OPEN_SELECTOR).forEach(el => (Data.get(el, DATA_KEY) || new Offcanvas(el)).show())\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue) || DATA_URL_PATTERN.test(attr.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attrName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  findShadowRoot,\n  getElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this._config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip && this.tip.parentNode) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this.isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = shadowRoot === null ?\n      this._element.ownerDocument.documentElement.contains(this._element) :\n      shadowRoot.contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const tipId = getUID(this.constructor.NAME)\n\n    tip.setAttribute('id', tipId)\n    this._element.setAttribute('aria-describedby', tipId)\n\n    this.setContent()\n\n    if (this._config.animation) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    const placement = typeof this._config.placement === 'function' ?\n      this._config.placement.call(this, tip, this._element) :\n      this._config.placement\n\n    const attachment = this._getAttachment(placement)\n    this._addAttachmentClass(attachment)\n\n    const { container } = this._config\n    Data.set(tip, this.constructor.DATA_KEY, this)\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.appendChild(tip)\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    const customClass = typeof this._config.customClass === 'function' ? this._config.customClass() : this._config.customClass\n    if (customClass) {\n      tip.classList.add(...customClass.split(' '))\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children).forEach(element => {\n        EventHandler.on(element, 'mouseover', noop)\n      })\n    }\n\n    const complete = () => {\n      const prevHoverState = this._hoverState\n\n      this._hoverState = null\n      EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n      if (prevHoverState === HOVER_STATE_OUT) {\n        this._leave(null, this)\n      }\n    }\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      if (this._popper) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this._config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (isElement(content)) {\n      content = getElement(content)\n\n      // content is a DOM node or a jQuery\n      if (this._config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this._config.html) {\n      if (this._config.sanitize) {\n        content = sanitizeHtml(content, this._config.allowList, this._config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this._element.getAttribute('data-bs-original-title')\n\n    if (!title) {\n      title = typeof this._config.title === 'function' ?\n        this._config.title.call(this._element) :\n        this._config.title\n    }\n\n    return title\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _initializeOnDelegatedTarget(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.get(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(event.delegateTarget, this._getDelegateConfig())\n      Data.set(event.delegateTarget, dataKey, context)\n    }\n\n    return context\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this._config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this._config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this._config.selector) {\n      this._config = {\n        ...this._config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context._config.delay || !context._config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context._config.delay.show)\n  }\n\n  _leave(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = context._element.contains(event.relatedTarget)\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context._config.delay || !context._config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context._config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this._config) {\n      for (const key in this._config) {\n        if (this.constructor.Default[key] !== this._config[key]) {\n          config[key] = this._config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n                '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this._element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContent() {\n    return this._element.getAttribute('data-bs-content') || this._config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.set(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = this._element.tagName === 'BODY' ? window : this._element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, () => this._process())\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getInstance(this) || new ScrollSpy(this, typeof config === 'object' ? config : {})\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE))) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = previous ?\n      EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      }) :\n      null\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      active.classList.remove(CLASS_NAME_SHOW)\n      this._queueCallback(complete, element, true)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    let parent = element.parentNode\n    if (parent && parent.nodeName === 'LI') {\n      parent = parent.parentNode\n    }\n\n    if (parent && parent.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE, dropdownElement)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  const data = Data.get(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout':\n        this._hasMouseInteraction = isInteracting\n        break\n      case 'focusin':\n      case 'focusout':\n        this._hasKeyboardInteraction = isInteracting\n        break\n      default:\n        break\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n"], "names": ["NODE_TEXT", "SelectorEngine", "find", "selector", "element", "document", "documentElement", "concat", "Element", "prototype", "querySelectorAll", "call", "findOne", "querySelector", "children", "filter", "child", "matches", "parents", "ancestor", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "MAX_UID", "MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "undefined", "toString", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "getElementById", "getSelector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "getElementFromSelector", "getTransitionDurationFromElement", "transitionDuration", "transitionDelay", "window", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "j<PERSON>y", "getElement", "length", "emulateTransitionEnd", "duration", "called", "durationPadding", "emulatedDuration", "listener", "removeEventListener", "addEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "style", "elementStyle", "parentNodeStyle", "display", "visibility", "isDisabled", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "onDOMContentLoaded", "callback", "readyState", "isRTL", "dir", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "execute", "elementMap", "Map", "set", "key", "instance", "has", "instanceMap", "get", "size", "console", "error", "Array", "from", "remove", "delete", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "customEventsRegex", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "bootstrapHandler", "handler", "event", "<PERSON><PERSON><PERSON><PERSON>", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "dom<PERSON><PERSON>s", "target", "i", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "len", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "getTypeEvent", "isNative", "add<PERSON><PERSON><PERSON>", "wrapFn", "relatedTarget", "handlers", "previousFn", "replace", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "on", "one", "inNamespace", "isNamespace", "elementEvent", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "VERSION", "BaseComponent", "constructor", "_element", "Data", "DATA_KEY", "dispose", "EVENT_KEY", "getOwnPropertyNames", "propertyName", "_queueCallback", "isAnimated", "getInstance", "Error", "DATA_API_KEY", "SELECTOR_DISMISS", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "CLASS_NAME_ALERT", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "<PERSON><PERSON>", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "closest", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "each", "data", "handle<PERSON><PERSON><PERSON>", "alertInstance", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "setAttribute", "button", "normalizeData", "val", "normalizeDataKey", "chr", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_SLIDE", "CLASS_NAME_END", "CLASS_NAME_START", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_INDICATOR", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "POINTER_TYPE_TOUCH", "POINTER_TYPE_PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "order", "_handleSwipe", "absDeltax", "abs", "direction", "_keydown", "_addTouchEventListeners", "start", "pointerType", "clientX", "touches", "move", "end", "clearTimeout", "itemImg", "e", "add", "tagName", "indexOf", "_getItemByOrder", "activeElement", "isNext", "isPrev", "lastItemIndex", "isGoingToWrap", "delta", "itemIndex", "_triggerSlideEvent", "eventDirectionName", "targetIndex", "fromIndex", "_setActiveIndicatorElement", "activeIndicator", "indicators", "parseInt", "elementInterval", "defaultInterval", "directionOrOrder", "_directionToOrder", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "_orderToDirection", "slideEvent", "triggerSlidEvent", "completeCallBack", "carouselInterface", "action", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "Collapse", "_isTransitioning", "_triggerArray", "id", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "startEvent", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "complete", "capitalizedDimension", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "selectorElements", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_CLICK", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPEND", "CLASS_NAME_DROPSTART", "CLASS_NAME_NAVBAR", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "boundary", "reference", "popperConfig", "autoClose", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "isActive", "getParentFromElement", "showEvent", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "isDisplayStatic", "modifiers", "modifier", "enabled", "createPopper", "focus", "_completeHide", "destroy", "update", "hideEvent", "_getPlacement", "parentDropdown", "isEnd", "getPropertyValue", "_getOffset", "map", "popperData", "defaultBsPopperConfig", "placement", "options", "_selectMenuItem", "items", "dropdownInterface", "clearMenus", "toggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "clickEvent", "dataApiKeydownHandler", "stopPropagation", "getToggleButton", "click", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "getWidth", "documentWidth", "clientWidth", "innerWidth", "width", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "actualValue", "overflow", "styleProp", "scrollbarWidth", "reset", "_resetElementAttributes", "removeProperty", "clickCallback", "CLASS_NAME_BACKDROP", "EVENT_MOUSEDOWN", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "className", "append<PERSON><PERSON><PERSON>", "backdropTransitionDuration", "EVENT_HIDE_PREVENTED", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_DISMISS", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_isShown", "_ignoreBackdropClick", "_isAnimated", "scrollBarHide", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "_enforceFocus", "transitionComplete", "_triggerBackdropTransition", "_resetAdjustments", "scrollBarReset", "currentTarget", "isModalOverflowing", "scrollHeight", "clientHeight", "overflowY", "modalTransitionDuration", "getScrollBarWidth", "isBodyOverflowing", "paddingLeft", "paddingRight", "scroll", "OPEN_SELECTOR", "<PERSON><PERSON><PERSON>", "_enforceFocusOnElement", "blur", "completeCallback", "allReadyOpen", "el", "uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "nodeValue", "regExp", "attrRegex", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacements", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_MODAL", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "updateAttachment", "dataKey", "_getDelegateConfig", "phase", "_handlePopperPlacementChange", "onFirstUpdate", "triggers", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "tabClass", "token", "tClass", "state", "popper", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "method", "EVENT_ACTIVATE", "EVENT_SCROLL", "CLASS_NAME_DROPDOWN_ITEM", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "isActiveTarget", "queries", "link", "join", "listGroup", "navItem", "node", "spy", "CLASS_NAME_DROPDOWN_MENU", "SELECTOR_ACTIVE_UL", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "listElement", "itemSelector", "activeElements", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "EVENT_MOUSEOVER", "EVENT_MOUSEOUT", "EVENT_FOCUSOUT", "CLASS_NAME_HIDE", "CLASS_NAME_SHOWING", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "_onInteraction", "isInteracting"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAEA,MAAMA,SAAS,GAAG,CAAlB;AAEA,MAAMC,cAAc,GAAG;AACrBC,EAAAA,IAAI,CAACC,QAAD,EAAWC,OAAO,GAAGC,QAAQ,CAACC,eAA9B,EAA+C;AACjD,WAAO,GAAGC,MAAH,CAAU,GAAGC,OAAO,CAACC,SAAR,CAAkBC,gBAAlB,CAAmCC,IAAnC,CAAwCP,OAAxC,EAAiDD,QAAjD,CAAb,CAAP;AACD,GAHoB;;AAKrBS,EAAAA,OAAO,CAACT,QAAD,EAAWC,OAAO,GAAGC,QAAQ,CAACC,eAA9B,EAA+C;AACpD,WAAOE,OAAO,CAACC,SAAR,CAAkBI,aAAlB,CAAgCF,IAAhC,CAAqCP,OAArC,EAA8CD,QAA9C,CAAP;AACD,GAPoB;;AASrBW,EAAAA,QAAQ,CAACV,OAAD,EAAUD,QAAV,EAAoB;AAC1B,WAAO,GAAGI,MAAH,CAAU,GAAGH,OAAO,CAACU,QAArB,EACJC,MADI,CACGC,KAAK,IAAIA,KAAK,CAACC,OAAN,CAAcd,QAAd,CADZ,CAAP;AAED,GAZoB;;AAcrBe,EAAAA,OAAO,CAACd,OAAD,EAAUD,QAAV,EAAoB;AACzB,UAAMe,OAAO,GAAG,EAAhB;AAEA,QAAIC,QAAQ,GAAGf,OAAO,CAACgB,UAAvB;;AAEA,WAAOD,QAAQ,IAAIA,QAAQ,CAACE,QAAT,KAAsBC,IAAI,CAACC,YAAvC,IAAuDJ,QAAQ,CAACE,QAAT,KAAsBrB,SAApF,EAA+F;AAC7F,UAAImB,QAAQ,CAACF,OAAT,CAAiBd,QAAjB,CAAJ,EAAgC;AAC9Be,QAAAA,OAAO,CAACM,IAAR,CAAaL,QAAb;AACD;;AAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACC,UAApB;AACD;;AAED,WAAOF,OAAP;AACD,GA5BoB;;AA8BrBO,EAAAA,IAAI,CAACrB,OAAD,EAAUD,QAAV,EAAoB;AACtB,QAAIuB,QAAQ,GAAGtB,OAAO,CAACuB,sBAAvB;;AAEA,WAAOD,QAAP,EAAiB;AACf,UAAIA,QAAQ,CAACT,OAAT,CAAiBd,QAAjB,CAAJ,EAAgC;AAC9B,eAAO,CAACuB,QAAD,CAAP;AACD;;AAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACC,sBAApB;AACD;;AAED,WAAO,EAAP;AACD,GA1CoB;;AA4CrBC,EAAAA,IAAI,CAACxB,OAAD,EAAUD,QAAV,EAAoB;AACtB,QAAIyB,IAAI,GAAGxB,OAAO,CAACyB,kBAAnB;;AAEA,WAAOD,IAAP,EAAa;AACX,UAAIA,IAAI,CAACX,OAAL,CAAad,QAAb,CAAJ,EAA4B;AAC1B,eAAO,CAACyB,IAAD,CAAP;AACD;;AAEDA,MAAAA,IAAI,GAAGA,IAAI,CAACC,kBAAZ;AACD;;AAED,WAAO,EAAP;AACD;;AAxDoB,CAAvB;;ACbA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,OAAO,GAAG,OAAhB;AACA,MAAMC,uBAAuB,GAAG,IAAhC;AACA,MAAMC,cAAc,GAAG,eAAvB;;AAGA,MAAMC,MAAM,GAAGC,GAAG,IAAI;AACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;AACrC,WAAQ,GAAED,GAAI,EAAd;AACD;;AAED,SAAO,GAAGE,QAAH,CAAYzB,IAAZ,CAAiBuB,GAAjB,EAAsBG,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;AACD,CAND;AAQA;AACA;AACA;AACA;AACA;;;AAEA,MAAMC,MAAM,GAAGC,MAAM,IAAI;AACvB,KAAG;AACDA,IAAAA,MAAM,IAAIC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBb,OAA3B,CAAV;AACD,GAFD,QAESzB,QAAQ,CAACuC,cAAT,CAAwBJ,MAAxB,CAFT;;AAIA,SAAOA,MAAP;AACD,CAND;;AAQA,MAAMK,WAAW,GAAGzC,OAAO,IAAI;AAC7B,MAAID,QAAQ,GAAGC,OAAO,CAAC0C,YAAR,CAAqB,gBAArB,CAAf;;AAEA,MAAI,CAAC3C,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;AACjC,QAAI4C,QAAQ,GAAG3C,OAAO,CAAC0C,YAAR,CAAqB,MAArB,CAAf,CADiC;AAIjC;AACA;AACA;;AACA,QAAI,CAACC,QAAD,IAAc,CAACA,QAAQ,CAACC,QAAT,CAAkB,GAAlB,CAAD,IAA2B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA9C,EAAyE;AACvE,aAAO,IAAP;AACD,KATgC;;;AAYjC,QAAIF,QAAQ,CAACC,QAAT,CAAkB,GAAlB,KAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA/B,EAAyD;AACvDF,MAAAA,QAAQ,GAAI,IAAGA,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAuB,EAAtC;AACD;;AAED/C,IAAAA,QAAQ,GAAG4C,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACI,IAAT,EAA/B,GAAiD,IAA5D;AACD;;AAED,SAAOhD,QAAP;AACD,CAvBD;;AAyBA,MAAMiD,sBAAsB,GAAGhD,OAAO,IAAI;AACxC,QAAMD,QAAQ,GAAG0C,WAAW,CAACzC,OAAD,CAA5B;;AAEA,MAAID,QAAJ,EAAc;AACZ,WAAOE,QAAQ,CAACQ,aAAT,CAAuBV,QAAvB,IAAmCA,QAAnC,GAA8C,IAArD;AACD;;AAED,SAAO,IAAP;AACD,CARD;;AAUA,MAAMkD,sBAAsB,GAAGjD,OAAO,IAAI;AACxC,QAAMD,QAAQ,GAAG0C,WAAW,CAACzC,OAAD,CAA5B;AAEA,SAAOD,QAAQ,GAAGE,QAAQ,CAACQ,aAAT,CAAuBV,QAAvB,CAAH,GAAsC,IAArD;AACD,CAJD;;AAMA,MAAMmD,gCAAgC,GAAGlD,OAAO,IAAI;AAClD,MAAI,CAACA,OAAL,EAAc;AACZ,WAAO,CAAP;AACD,GAHiD;;;AAMlD,MAAI;AAAEmD,IAAAA,kBAAF;AAAsBC,IAAAA;AAAtB,MAA0CC,MAAM,CAACC,gBAAP,CAAwBtD,OAAxB,CAA9C;AAEA,QAAMuD,uBAAuB,GAAGC,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,CAAhC;AACA,QAAMO,oBAAoB,GAAGF,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAA7B,CATkD;;AAYlD,MAAI,CAACG,uBAAD,IAA4B,CAACG,oBAAjC,EAAuD;AACrD,WAAO,CAAP;AACD,GAdiD;;;AAiBlDP,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACL,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;AACAM,EAAAA,eAAe,GAAGA,eAAe,CAACN,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAlB;AAEA,SAAO,CAACU,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,IAAwCK,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAAzC,IAA+EzB,uBAAtF;AACD,CArBD;;AAuBA,MAAMgC,oBAAoB,GAAG3D,OAAO,IAAI;AACtCA,EAAAA,OAAO,CAAC4D,aAAR,CAAsB,IAAIC,KAAJ,CAAUjC,cAAV,CAAtB;AACD,CAFD;;AAIA,MAAMkC,SAAS,GAAGhC,GAAG,IAAI;AACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;AACnC,WAAO,KAAP;AACD;;AAED,MAAI,OAAOA,GAAG,CAACiC,MAAX,KAAsB,WAA1B,EAAuC;AACrCjC,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;AACD;;AAED,SAAO,OAAOA,GAAG,CAACb,QAAX,KAAwB,WAA/B;AACD,CAVD;;AAYA,MAAM+C,UAAU,GAAGlC,GAAG,IAAI;AACxB,MAAIgC,SAAS,CAAChC,GAAD,CAAb,EAAoB;AAAE;AACpB,WAAOA,GAAG,CAACiC,MAAJ,GAAajC,GAAG,CAAC,CAAD,CAAhB,GAAsBA,GAA7B;AACD;;AAED,MAAI,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,CAACmC,MAAJ,GAAa,CAA5C,EAA+C;AAC7C,WAAOpE,cAAc,CAACW,OAAf,CAAuBsB,GAAvB,CAAP;AACD;;AAED,SAAO,IAAP;AACD,CAVD;;AAYA,MAAMoC,oBAAoB,GAAG,CAAClE,OAAD,EAAUmE,QAAV,KAAuB;AAClD,MAAIC,MAAM,GAAG,KAAb;AACA,QAAMC,eAAe,GAAG,CAAxB;AACA,QAAMC,gBAAgB,GAAGH,QAAQ,GAAGE,eAApC;;AAEA,WAASE,QAAT,GAAoB;AAClBH,IAAAA,MAAM,GAAG,IAAT;AACApE,IAAAA,OAAO,CAACwE,mBAAR,CAA4B5C,cAA5B,EAA4C2C,QAA5C;AACD;;AAEDvE,EAAAA,OAAO,CAACyE,gBAAR,CAAyB7C,cAAzB,EAAyC2C,QAAzC;AACAG,EAAAA,UAAU,CAAC,MAAM;AACf,QAAI,CAACN,MAAL,EAAa;AACXT,MAAAA,oBAAoB,CAAC3D,OAAD,CAApB;AACD;AACF,GAJS,EAIPsE,gBAJO,CAAV;AAKD,CAhBD;;AAkBA,MAAMK,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;AAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;AAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;AACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;AACA,UAAMG,SAAS,GAAGD,KAAK,IAAItB,SAAS,CAACsB,KAAD,CAAlB,GAA4B,SAA5B,GAAwCvD,MAAM,CAACuD,KAAD,CAAhE;;AAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;AAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,aAAYP,QAAS,oBAAmBG,SAAU,wBAAuBF,aAAc,IADlH,CAAN;AAGD;AACF,GAVD;AAWD,CAZD;;AAcA,MAAMO,SAAS,GAAG1F,OAAO,IAAI;AAC3B,MAAI,CAACA,OAAL,EAAc;AACZ,WAAO,KAAP;AACD;;AAED,MAAIA,OAAO,CAAC2F,KAAR,IAAiB3F,OAAO,CAACgB,UAAzB,IAAuChB,OAAO,CAACgB,UAAR,CAAmB2E,KAA9D,EAAqE;AACnE,UAAMC,YAAY,GAAGtC,gBAAgB,CAACtD,OAAD,CAArC;AACA,UAAM6F,eAAe,GAAGvC,gBAAgB,CAACtD,OAAO,CAACgB,UAAT,CAAxC;AAEA,WAAO4E,YAAY,CAACE,OAAb,KAAyB,MAAzB,IACLD,eAAe,CAACC,OAAhB,KAA4B,MADvB,IAELF,YAAY,CAACG,UAAb,KAA4B,QAF9B;AAGD;;AAED,SAAO,KAAP;AACD,CAfD;;AAiBA,MAAMC,UAAU,GAAGhG,OAAO,IAAI;AAC5B,MAAI,CAACA,OAAD,IAAYA,OAAO,CAACiB,QAAR,KAAqBC,IAAI,CAACC,YAA1C,EAAwD;AACtD,WAAO,IAAP;AACD;;AAED,MAAInB,OAAO,CAACiG,SAAR,CAAkBC,QAAlB,CAA2B,UAA3B,CAAJ,EAA4C;AAC1C,WAAO,IAAP;AACD;;AAED,MAAI,OAAOlG,OAAO,CAACmG,QAAf,KAA4B,WAAhC,EAA6C;AAC3C,WAAOnG,OAAO,CAACmG,QAAf;AACD;;AAED,SAAOnG,OAAO,CAACoG,YAAR,CAAqB,UAArB,KAAoCpG,OAAO,CAAC0C,YAAR,CAAqB,UAArB,MAAqC,OAAhF;AACD,CAdD;;AAgBA,MAAM2D,cAAc,GAAGrG,OAAO,IAAI;AAChC,MAAI,CAACC,QAAQ,CAACC,eAAT,CAAyBoG,YAA9B,EAA4C;AAC1C,WAAO,IAAP;AACD,GAH+B;;;AAMhC,MAAI,OAAOtG,OAAO,CAACuG,WAAf,KAA+B,UAAnC,EAA+C;AAC7C,UAAMC,IAAI,GAAGxG,OAAO,CAACuG,WAAR,EAAb;AACA,WAAOC,IAAI,YAAYC,UAAhB,GAA6BD,IAA7B,GAAoC,IAA3C;AACD;;AAED,MAAIxG,OAAO,YAAYyG,UAAvB,EAAmC;AACjC,WAAOzG,OAAP;AACD,GAb+B;;;AAgBhC,MAAI,CAACA,OAAO,CAACgB,UAAb,EAAyB;AACvB,WAAO,IAAP;AACD;;AAED,SAAOqF,cAAc,CAACrG,OAAO,CAACgB,UAAT,CAArB;AACD,CArBD;;AAuBA,MAAM0F,IAAI,GAAG,MAAM,EAAnB;;AAEA,MAAMC,MAAM,GAAG3G,OAAO,IAAIA,OAAO,CAAC4G,YAAlC;;AAEA,MAAMC,SAAS,GAAG,MAAM;AACtB,QAAM;AAAEC,IAAAA;AAAF,MAAazD,MAAnB;;AAEA,MAAIyD,MAAM,IAAI,CAAC7G,QAAQ,CAAC8G,IAAT,CAAcX,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;AAC9D,WAAOU,MAAP;AACD;;AAED,SAAO,IAAP;AACD,CARD;;AAUA,MAAME,kBAAkB,GAAGC,QAAQ,IAAI;AACrC,MAAIhH,QAAQ,CAACiH,UAAT,KAAwB,SAA5B,EAAuC;AACrCjH,IAAAA,QAAQ,CAACwE,gBAAT,CAA0B,kBAA1B,EAA8CwC,QAA9C;AACD,GAFD,MAEO;AACLA,IAAAA,QAAQ;AACT;AACF,CAND;;AAQA,MAAME,KAAK,GAAG,MAAMlH,QAAQ,CAACC,eAAT,CAAyBkH,GAAzB,KAAiC,KAArD;;AAEA,MAAMC,kBAAkB,GAAGC,MAAM,IAAI;AACnCN,EAAAA,kBAAkB,CAAC,MAAM;AACvB,UAAMO,CAAC,GAAGV,SAAS,EAAnB;AACA;;AACA,QAAIU,CAAJ,EAAO;AACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;AACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;AACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;AACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;AACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;AAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;AACA,eAAOJ,MAAM,CAACM,eAAd;AACD,OAHD;AAID;AACF,GAbiB,CAAlB;AAcD,CAfD;;AAiBA,MAAMG,OAAO,GAAGd,QAAQ,IAAI;AAC1B,MAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;AAClCA,IAAAA,QAAQ;AACT;AACF,CAJD;;ACjQA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAEA,MAAMe,UAAU,GAAG,IAAIC,GAAJ,EAAnB;AAEA,WAAe;AACbC,EAAAA,GAAG,CAAClI,OAAD,EAAUmI,GAAV,EAAeC,QAAf,EAAyB;AAC1B,QAAI,CAACJ,UAAU,CAACK,GAAX,CAAerI,OAAf,CAAL,EAA8B;AAC5BgI,MAAAA,UAAU,CAACE,GAAX,CAAelI,OAAf,EAAwB,IAAIiI,GAAJ,EAAxB;AACD;;AAED,UAAMK,WAAW,GAAGN,UAAU,CAACO,GAAX,CAAevI,OAAf,CAApB,CAL0B;AAQ1B;;AACA,QAAI,CAACsI,WAAW,CAACD,GAAZ,CAAgBF,GAAhB,CAAD,IAAyBG,WAAW,CAACE,IAAZ,KAAqB,CAAlD,EAAqD;AACnD;AACAC,MAAAA,OAAO,CAACC,KAAR,CAAe,+EAA8EC,KAAK,CAACC,IAAN,CAAWN,WAAW,CAACtD,IAAZ,EAAX,EAA+B,CAA/B,CAAkC,GAA/H;AACA;AACD;;AAEDsD,IAAAA,WAAW,CAACJ,GAAZ,CAAgBC,GAAhB,EAAqBC,QAArB;AACD,GAjBY;;AAmBbG,EAAAA,GAAG,CAACvI,OAAD,EAAUmI,GAAV,EAAe;AAChB,QAAIH,UAAU,CAACK,GAAX,CAAerI,OAAf,CAAJ,EAA6B;AAC3B,aAAOgI,UAAU,CAACO,GAAX,CAAevI,OAAf,EAAwBuI,GAAxB,CAA4BJ,GAA5B,KAAoC,IAA3C;AACD;;AAED,WAAO,IAAP;AACD,GAzBY;;AA2BbU,EAAAA,MAAM,CAAC7I,OAAD,EAAUmI,GAAV,EAAe;AACnB,QAAI,CAACH,UAAU,CAACK,GAAX,CAAerI,OAAf,CAAL,EAA8B;AAC5B;AACD;;AAED,UAAMsI,WAAW,GAAGN,UAAU,CAACO,GAAX,CAAevI,OAAf,CAApB;AAEAsI,IAAAA,WAAW,CAACQ,MAAZ,CAAmBX,GAAnB,EAPmB;;AAUnB,QAAIG,WAAW,CAACE,IAAZ,KAAqB,CAAzB,EAA4B;AAC1BR,MAAAA,UAAU,CAACc,MAAX,CAAkB9I,OAAlB;AACD;AACF;;AAxCY,CAAf;;ACfA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;;AAEA,MAAM+I,cAAc,GAAG,oBAAvB;AACA,MAAMC,cAAc,GAAG,MAAvB;AACA,MAAMC,aAAa,GAAG,QAAtB;AACA,MAAMC,aAAa,GAAG,EAAtB;;AACA,IAAIC,QAAQ,GAAG,CAAf;AACA,MAAMC,YAAY,GAAG;AACnBC,EAAAA,UAAU,EAAE,WADO;AAEnBC,EAAAA,UAAU,EAAE;AAFO,CAArB;AAIA,MAAMC,iBAAiB,GAAG,2BAA1B;AACA,MAAMC,YAAY,GAAG,IAAIC,GAAJ,CAAQ,CAC3B,OAD2B,EAE3B,UAF2B,EAG3B,SAH2B,EAI3B,WAJ2B,EAK3B,aAL2B,EAM3B,YAN2B,EAO3B,gBAP2B,EAQ3B,WAR2B,EAS3B,UAT2B,EAU3B,WAV2B,EAW3B,aAX2B,EAY3B,WAZ2B,EAa3B,SAb2B,EAc3B,UAd2B,EAe3B,OAf2B,EAgB3B,mBAhB2B,EAiB3B,YAjB2B,EAkB3B,WAlB2B,EAmB3B,UAnB2B,EAoB3B,aApB2B,EAqB3B,aArB2B,EAsB3B,aAtB2B,EAuB3B,WAvB2B,EAwB3B,cAxB2B,EAyB3B,eAzB2B,EA0B3B,cA1B2B,EA2B3B,eA3B2B,EA4B3B,YA5B2B,EA6B3B,OA7B2B,EA8B3B,MA9B2B,EA+B3B,QA/B2B,EAgC3B,OAhC2B,EAiC3B,QAjC2B,EAkC3B,QAlC2B,EAmC3B,SAnC2B,EAoC3B,UApC2B,EAqC3B,MArC2B,EAsC3B,QAtC2B,EAuC3B,cAvC2B,EAwC3B,QAxC2B,EAyC3B,MAzC2B,EA0C3B,kBA1C2B,EA2C3B,kBA3C2B,EA4C3B,OA5C2B,EA6C3B,OA7C2B,EA8C3B,QA9C2B,CAAR,CAArB;AAiDA;AACA;AACA;AACA;AACA;;AAEA,SAASC,WAAT,CAAqB1J,OAArB,EAA8B2J,GAA9B,EAAmC;AACjC,SAAQA,GAAG,IAAK,GAAEA,GAAI,KAAIR,QAAQ,EAAG,EAA9B,IAAoCnJ,OAAO,CAACmJ,QAA5C,IAAwDA,QAAQ,EAAvE;AACD;;AAED,SAASS,QAAT,CAAkB5J,OAAlB,EAA2B;AACzB,QAAM2J,GAAG,GAAGD,WAAW,CAAC1J,OAAD,CAAvB;AAEAA,EAAAA,OAAO,CAACmJ,QAAR,GAAmBQ,GAAnB;AACAT,EAAAA,aAAa,CAACS,GAAD,CAAb,GAAqBT,aAAa,CAACS,GAAD,CAAb,IAAsB,EAA3C;AAEA,SAAOT,aAAa,CAACS,GAAD,CAApB;AACD;;AAED,SAASE,gBAAT,CAA0B7J,OAA1B,EAAmC2H,EAAnC,EAAuC;AACrC,SAAO,SAASmC,OAAT,CAAiBC,KAAjB,EAAwB;AAC7BA,IAAAA,KAAK,CAACC,cAAN,GAAuBhK,OAAvB;;AAEA,QAAI8J,OAAO,CAACG,MAAZ,EAAoB;AAClBC,MAAAA,YAAY,CAACC,GAAb,CAAiBnK,OAAjB,EAA0B+J,KAAK,CAACK,IAAhC,EAAsCzC,EAAtC;AACD;;AAED,WAAOA,EAAE,CAAC0C,KAAH,CAASrK,OAAT,EAAkB,CAAC+J,KAAD,CAAlB,CAAP;AACD,GARD;AASD;;AAED,SAASO,0BAAT,CAAoCtK,OAApC,EAA6CD,QAA7C,EAAuD4H,EAAvD,EAA2D;AACzD,SAAO,SAASmC,OAAT,CAAiBC,KAAjB,EAAwB;AAC7B,UAAMQ,WAAW,GAAGvK,OAAO,CAACM,gBAAR,CAAyBP,QAAzB,CAApB;;AAEA,SAAK,IAAI;AAAEyK,MAAAA;AAAF,QAAaT,KAAtB,EAA6BS,MAAM,IAAIA,MAAM,KAAK,IAAlD,EAAwDA,MAAM,GAAGA,MAAM,CAACxJ,UAAxE,EAAoF;AAClF,WAAK,IAAIyJ,CAAC,GAAGF,WAAW,CAACtG,MAAzB,EAAiCwG,CAAC,EAAlC,GAAuC;AACrC,YAAIF,WAAW,CAACE,CAAD,CAAX,KAAmBD,MAAvB,EAA+B;AAC7BT,UAAAA,KAAK,CAACC,cAAN,GAAuBQ,MAAvB;;AAEA,cAAIV,OAAO,CAACG,MAAZ,EAAoB;AAClB;AACAC,YAAAA,YAAY,CAACC,GAAb,CAAiBnK,OAAjB,EAA0B+J,KAAK,CAACK,IAAhC,EAAsCrK,QAAtC,EAAgD4H,EAAhD;AACD;;AAED,iBAAOA,EAAE,CAAC0C,KAAH,CAASG,MAAT,EAAiB,CAACT,KAAD,CAAjB,CAAP;AACD;AACF;AACF,KAhB4B;;;AAmB7B,WAAO,IAAP;AACD,GApBD;AAqBD;;AAED,SAASW,WAAT,CAAqBC,MAArB,EAA6Bb,OAA7B,EAAsCc,kBAAkB,GAAG,IAA3D,EAAiE;AAC/D,QAAMC,YAAY,GAAG9F,MAAM,CAACC,IAAP,CAAY2F,MAAZ,CAArB;;AAEA,OAAK,IAAIF,CAAC,GAAG,CAAR,EAAWK,GAAG,GAAGD,YAAY,CAAC5G,MAAnC,EAA2CwG,CAAC,GAAGK,GAA/C,EAAoDL,CAAC,EAArD,EAAyD;AACvD,UAAMV,KAAK,GAAGY,MAAM,CAACE,YAAY,CAACJ,CAAD,CAAb,CAApB;;AAEA,QAAIV,KAAK,CAACgB,eAAN,KAA0BjB,OAA1B,IAAqCC,KAAK,CAACa,kBAAN,KAA6BA,kBAAtE,EAA0F;AACxF,aAAOb,KAAP;AACD;AACF;;AAED,SAAO,IAAP;AACD;;AAED,SAASiB,eAAT,CAAyBC,iBAAzB,EAA4CnB,OAA5C,EAAqDoB,YAArD,EAAmE;AACjE,QAAMC,UAAU,GAAG,OAAOrB,OAAP,KAAmB,QAAtC;AACA,QAAMiB,eAAe,GAAGI,UAAU,GAAGD,YAAH,GAAkBpB,OAApD;AAEA,MAAIsB,SAAS,GAAGC,YAAY,CAACJ,iBAAD,CAA5B;AACA,QAAMK,QAAQ,GAAG9B,YAAY,CAACnB,GAAb,CAAiB+C,SAAjB,CAAjB;;AAEA,MAAI,CAACE,QAAL,EAAe;AACbF,IAAAA,SAAS,GAAGH,iBAAZ;AACD;;AAED,SAAO,CAACE,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,CAAP;AACD;;AAED,SAASG,UAAT,CAAoBvL,OAApB,EAA6BiL,iBAA7B,EAAgDnB,OAAhD,EAAyDoB,YAAzD,EAAuEjB,MAAvE,EAA+E;AAC7E,MAAI,OAAOgB,iBAAP,KAA6B,QAA7B,IAAyC,CAACjL,OAA9C,EAAuD;AACrD;AACD;;AAED,MAAI,CAAC8J,OAAL,EAAc;AACZA,IAAAA,OAAO,GAAGoB,YAAV;AACAA,IAAAA,YAAY,GAAG,IAAf;AACD,GAR4E;AAW7E;;;AACA,MAAI3B,iBAAiB,CAAChE,IAAlB,CAAuB0F,iBAAvB,CAAJ,EAA+C;AAC7C,UAAMO,MAAM,GAAG7D,EAAE,IAAI;AACnB,aAAO,UAAUoC,KAAV,EAAiB;AACtB,YAAI,CAACA,KAAK,CAAC0B,aAAP,IAAyB1B,KAAK,CAAC0B,aAAN,KAAwB1B,KAAK,CAACC,cAA9B,IAAgD,CAACD,KAAK,CAACC,cAAN,CAAqB9D,QAArB,CAA8B6D,KAAK,CAAC0B,aAApC,CAA9E,EAAmI;AACjI,iBAAO9D,EAAE,CAACpH,IAAH,CAAQ,IAAR,EAAcwJ,KAAd,CAAP;AACD;AACF,OAJD;AAKD,KAND;;AAQA,QAAImB,YAAJ,EAAkB;AAChBA,MAAAA,YAAY,GAAGM,MAAM,CAACN,YAAD,CAArB;AACD,KAFD,MAEO;AACLpB,MAAAA,OAAO,GAAG0B,MAAM,CAAC1B,OAAD,CAAhB;AACD;AACF;;AAED,QAAM,CAACqB,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,IAA2CJ,eAAe,CAACC,iBAAD,EAAoBnB,OAApB,EAA6BoB,YAA7B,CAAhE;AACA,QAAMP,MAAM,GAAGf,QAAQ,CAAC5J,OAAD,CAAvB;AACA,QAAM0L,QAAQ,GAAGf,MAAM,CAACS,SAAD,CAAN,KAAsBT,MAAM,CAACS,SAAD,CAAN,GAAoB,EAA1C,CAAjB;AACA,QAAMO,UAAU,GAAGjB,WAAW,CAACgB,QAAD,EAAWX,eAAX,EAA4BI,UAAU,GAAGrB,OAAH,GAAa,IAAnD,CAA9B;;AAEA,MAAI6B,UAAJ,EAAgB;AACdA,IAAAA,UAAU,CAAC1B,MAAX,GAAoB0B,UAAU,CAAC1B,MAAX,IAAqBA,MAAzC;AAEA;AACD;;AAED,QAAMN,GAAG,GAAGD,WAAW,CAACqB,eAAD,EAAkBE,iBAAiB,CAACW,OAAlB,CAA0B7C,cAA1B,EAA0C,EAA1C,CAAlB,CAAvB;AACA,QAAMpB,EAAE,GAAGwD,UAAU,GACnBb,0BAA0B,CAACtK,OAAD,EAAU8J,OAAV,EAAmBoB,YAAnB,CADP,GAEnBrB,gBAAgB,CAAC7J,OAAD,EAAU8J,OAAV,CAFlB;AAIAnC,EAAAA,EAAE,CAACiD,kBAAH,GAAwBO,UAAU,GAAGrB,OAAH,GAAa,IAA/C;AACAnC,EAAAA,EAAE,CAACoD,eAAH,GAAqBA,eAArB;AACApD,EAAAA,EAAE,CAACsC,MAAH,GAAYA,MAAZ;AACAtC,EAAAA,EAAE,CAACwB,QAAH,GAAcQ,GAAd;AACA+B,EAAAA,QAAQ,CAAC/B,GAAD,CAAR,GAAgBhC,EAAhB;AAEA3H,EAAAA,OAAO,CAACyE,gBAAR,CAAyB2G,SAAzB,EAAoCzD,EAApC,EAAwCwD,UAAxC;AACD;;AAED,SAASU,aAAT,CAAuB7L,OAAvB,EAAgC2K,MAAhC,EAAwCS,SAAxC,EAAmDtB,OAAnD,EAA4Dc,kBAA5D,EAAgF;AAC9E,QAAMjD,EAAE,GAAG+C,WAAW,CAACC,MAAM,CAACS,SAAD,CAAP,EAAoBtB,OAApB,EAA6Bc,kBAA7B,CAAtB;;AAEA,MAAI,CAACjD,EAAL,EAAS;AACP;AACD;;AAED3H,EAAAA,OAAO,CAACwE,mBAAR,CAA4B4G,SAA5B,EAAuCzD,EAAvC,EAA2CmE,OAAO,CAAClB,kBAAD,CAAlD;AACA,SAAOD,MAAM,CAACS,SAAD,CAAN,CAAkBzD,EAAE,CAACwB,QAArB,CAAP;AACD;;AAED,SAAS4C,wBAAT,CAAkC/L,OAAlC,EAA2C2K,MAA3C,EAAmDS,SAAnD,EAA8DY,SAA9D,EAAyE;AACvE,QAAMC,iBAAiB,GAAGtB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;AAEArG,EAAAA,MAAM,CAACC,IAAP,CAAYiH,iBAAZ,EAA+BhH,OAA/B,CAAuCiH,UAAU,IAAI;AACnD,QAAIA,UAAU,CAACtJ,QAAX,CAAoBoJ,SAApB,CAAJ,EAAoC;AAClC,YAAMjC,KAAK,GAAGkC,iBAAiB,CAACC,UAAD,CAA/B;AAEAL,MAAAA,aAAa,CAAC7L,OAAD,EAAU2K,MAAV,EAAkBS,SAAlB,EAA6BrB,KAAK,CAACgB,eAAnC,EAAoDhB,KAAK,CAACa,kBAA1D,CAAb;AACD;AACF,GAND;AAOD;;AAED,SAASS,YAAT,CAAsBtB,KAAtB,EAA6B;AAC3B;AACAA,EAAAA,KAAK,GAAGA,KAAK,CAAC6B,OAAN,CAAc5C,cAAd,EAA8B,EAA9B,CAAR;AACA,SAAOI,YAAY,CAACW,KAAD,CAAZ,IAAuBA,KAA9B;AACD;;AAED,MAAMG,YAAY,GAAG;AACnBiC,EAAAA,EAAE,CAACnM,OAAD,EAAU+J,KAAV,EAAiBD,OAAjB,EAA0BoB,YAA1B,EAAwC;AACxCK,IAAAA,UAAU,CAACvL,OAAD,EAAU+J,KAAV,EAAiBD,OAAjB,EAA0BoB,YAA1B,EAAwC,KAAxC,CAAV;AACD,GAHkB;;AAKnBkB,EAAAA,GAAG,CAACpM,OAAD,EAAU+J,KAAV,EAAiBD,OAAjB,EAA0BoB,YAA1B,EAAwC;AACzCK,IAAAA,UAAU,CAACvL,OAAD,EAAU+J,KAAV,EAAiBD,OAAjB,EAA0BoB,YAA1B,EAAwC,IAAxC,CAAV;AACD,GAPkB;;AASnBf,EAAAA,GAAG,CAACnK,OAAD,EAAUiL,iBAAV,EAA6BnB,OAA7B,EAAsCoB,YAAtC,EAAoD;AACrD,QAAI,OAAOD,iBAAP,KAA6B,QAA7B,IAAyC,CAACjL,OAA9C,EAAuD;AACrD;AACD;;AAED,UAAM,CAACmL,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,IAA2CJ,eAAe,CAACC,iBAAD,EAAoBnB,OAApB,EAA6BoB,YAA7B,CAAhE;AACA,UAAMmB,WAAW,GAAGjB,SAAS,KAAKH,iBAAlC;AACA,UAAMN,MAAM,GAAGf,QAAQ,CAAC5J,OAAD,CAAvB;AACA,UAAMsM,WAAW,GAAGrB,iBAAiB,CAACpI,UAAlB,CAA6B,GAA7B,CAApB;;AAEA,QAAI,OAAOkI,eAAP,KAA2B,WAA/B,EAA4C;AAC1C;AACA,UAAI,CAACJ,MAAD,IAAW,CAACA,MAAM,CAACS,SAAD,CAAtB,EAAmC;AACjC;AACD;;AAEDS,MAAAA,aAAa,CAAC7L,OAAD,EAAU2K,MAAV,EAAkBS,SAAlB,EAA6BL,eAA7B,EAA8CI,UAAU,GAAGrB,OAAH,GAAa,IAArE,CAAb;AACA;AACD;;AAED,QAAIwC,WAAJ,EAAiB;AACfvH,MAAAA,MAAM,CAACC,IAAP,CAAY2F,MAAZ,EAAoB1F,OAApB,CAA4BsH,YAAY,IAAI;AAC1CR,QAAAA,wBAAwB,CAAC/L,OAAD,EAAU2K,MAAV,EAAkB4B,YAAlB,EAAgCtB,iBAAiB,CAACuB,KAAlB,CAAwB,CAAxB,CAAhC,CAAxB;AACD,OAFD;AAGD;;AAED,UAAMP,iBAAiB,GAAGtB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;AACArG,IAAAA,MAAM,CAACC,IAAP,CAAYiH,iBAAZ,EAA+BhH,OAA/B,CAAuCwH,WAAW,IAAI;AACpD,YAAMP,UAAU,GAAGO,WAAW,CAACb,OAAZ,CAAoB3C,aAApB,EAAmC,EAAnC,CAAnB;;AAEA,UAAI,CAACoD,WAAD,IAAgBpB,iBAAiB,CAACrI,QAAlB,CAA2BsJ,UAA3B,CAApB,EAA4D;AAC1D,cAAMnC,KAAK,GAAGkC,iBAAiB,CAACQ,WAAD,CAA/B;AAEAZ,QAAAA,aAAa,CAAC7L,OAAD,EAAU2K,MAAV,EAAkBS,SAAlB,EAA6BrB,KAAK,CAACgB,eAAnC,EAAoDhB,KAAK,CAACa,kBAA1D,CAAb;AACD;AACF,KARD;AASD,GA7CkB;;AA+CnB8B,EAAAA,OAAO,CAAC1M,OAAD,EAAU+J,KAAV,EAAiB4C,IAAjB,EAAuB;AAC5B,QAAI,OAAO5C,KAAP,KAAiB,QAAjB,IAA6B,CAAC/J,OAAlC,EAA2C;AACzC,aAAO,IAAP;AACD;;AAED,UAAMuH,CAAC,GAAGV,SAAS,EAAnB;AACA,UAAMuE,SAAS,GAAGC,YAAY,CAACtB,KAAD,CAA9B;AACA,UAAMsC,WAAW,GAAGtC,KAAK,KAAKqB,SAA9B;AACA,UAAME,QAAQ,GAAG9B,YAAY,CAACnB,GAAb,CAAiB+C,SAAjB,CAAjB;AAEA,QAAIwB,WAAJ;AACA,QAAIC,OAAO,GAAG,IAAd;AACA,QAAIC,cAAc,GAAG,IAArB;AACA,QAAIC,gBAAgB,GAAG,KAAvB;AACA,QAAIC,GAAG,GAAG,IAAV;;AAEA,QAAIX,WAAW,IAAI9E,CAAnB,EAAsB;AACpBqF,MAAAA,WAAW,GAAGrF,CAAC,CAAC1D,KAAF,CAAQkG,KAAR,EAAe4C,IAAf,CAAd;AAEApF,MAAAA,CAAC,CAACvH,OAAD,CAAD,CAAW0M,OAAX,CAAmBE,WAAnB;AACAC,MAAAA,OAAO,GAAG,CAACD,WAAW,CAACK,oBAAZ,EAAX;AACAH,MAAAA,cAAc,GAAG,CAACF,WAAW,CAACM,6BAAZ,EAAlB;AACAH,MAAAA,gBAAgB,GAAGH,WAAW,CAACO,kBAAZ,EAAnB;AACD;;AAED,QAAI7B,QAAJ,EAAc;AACZ0B,MAAAA,GAAG,GAAG/M,QAAQ,CAACmN,WAAT,CAAqB,YAArB,CAAN;AACAJ,MAAAA,GAAG,CAACK,SAAJ,CAAcjC,SAAd,EAAyByB,OAAzB,EAAkC,IAAlC;AACD,KAHD,MAGO;AACLG,MAAAA,GAAG,GAAG,IAAIM,WAAJ,CAAgBvD,KAAhB,EAAuB;AAC3B8C,QAAAA,OAD2B;AAE3BU,QAAAA,UAAU,EAAE;AAFe,OAAvB,CAAN;AAID,KAjC2B;;;AAoC5B,QAAI,OAAOZ,IAAP,KAAgB,WAApB,EAAiC;AAC/B5H,MAAAA,MAAM,CAACC,IAAP,CAAY2H,IAAZ,EAAkB1H,OAAlB,CAA0BkD,GAAG,IAAI;AAC/BpD,QAAAA,MAAM,CAACyI,cAAP,CAAsBR,GAAtB,EAA2B7E,GAA3B,EAAgC;AAC9BI,UAAAA,GAAG,GAAG;AACJ,mBAAOoE,IAAI,CAACxE,GAAD,CAAX;AACD;;AAH6B,SAAhC;AAKD,OAND;AAOD;;AAED,QAAI4E,gBAAJ,EAAsB;AACpBC,MAAAA,GAAG,CAACS,cAAJ;AACD;;AAED,QAAIX,cAAJ,EAAoB;AAClB9M,MAAAA,OAAO,CAAC4D,aAAR,CAAsBoJ,GAAtB;AACD;;AAED,QAAIA,GAAG,CAACD,gBAAJ,IAAwB,OAAOH,WAAP,KAAuB,WAAnD,EAAgE;AAC9DA,MAAAA,WAAW,CAACa,cAAZ;AACD;;AAED,WAAOT,GAAP;AACD;;AA1GkB,CAArB;;AC/OA;AACA;AACA;AACA;AACA;AACA;AAWA;AACA;AACA;AACA;AACA;;AAEA,MAAMU,OAAO,GAAG,OAAhB;;AAEA,MAAMC,aAAN,CAAoB;AAClBC,EAAAA,WAAW,CAAC5N,OAAD,EAAU;AACnBA,IAAAA,OAAO,GAAGgE,UAAU,CAAChE,OAAD,CAApB;;AAEA,QAAI,CAACA,OAAL,EAAc;AACZ;AACD;;AAED,SAAK6N,QAAL,GAAgB7N,OAAhB;AACA8N,IAAAA,IAAI,CAAC5F,GAAL,CAAS,KAAK2F,QAAd,EAAwB,KAAKD,WAAL,CAAiBG,QAAzC,EAAmD,IAAnD;AACD;;AAEDC,EAAAA,OAAO,GAAG;AACRF,IAAAA,IAAI,CAACjF,MAAL,CAAY,KAAKgF,QAAjB,EAA2B,KAAKD,WAAL,CAAiBG,QAA5C;AACA7D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgC,KAAKD,WAAL,CAAiBK,SAAjD;AAEAlJ,IAAAA,MAAM,CAACmJ,mBAAP,CAA2B,IAA3B,EAAiCjJ,OAAjC,CAAyCkJ,YAAY,IAAI;AACvD,WAAKA,YAAL,IAAqB,IAArB;AACD,KAFD;AAGD;;AAEDC,EAAAA,cAAc,CAACnH,QAAD,EAAWjH,OAAX,EAAoBqO,UAAU,GAAG,IAAjC,EAAuC;AACnD,QAAI,CAACA,UAAL,EAAiB;AACftG,MAAAA,OAAO,CAACd,QAAD,CAAP;AACA;AACD;;AAED,UAAM9D,kBAAkB,GAAGD,gCAAgC,CAAClD,OAAD,CAA3D;AACAkK,IAAAA,YAAY,CAACkC,GAAb,CAAiBpM,OAAjB,EAA0B,eAA1B,EAA2C,MAAM+H,OAAO,CAACd,QAAD,CAAxD;AAEA/C,IAAAA,oBAAoB,CAAClE,OAAD,EAAUmD,kBAAV,CAApB;AACD;AAED;;;AAEkB,SAAXmL,WAAW,CAACtO,OAAD,EAAU;AAC1B,WAAO8N,IAAI,CAACvF,GAAL,CAASvI,OAAT,EAAkB,KAAK+N,QAAvB,CAAP;AACD;;AAEiB,aAAPL,OAAO,GAAG;AACnB,WAAOA,OAAP;AACD;;AAEc,aAAJjG,IAAI,GAAG;AAChB,UAAM,IAAI8G,KAAJ,CAAU,qEAAV,CAAN;AACD;;AAEkB,aAARR,QAAQ,GAAG;AACpB,WAAQ,MAAK,KAAKtG,IAAK,EAAvB;AACD;;AAEmB,aAATwG,SAAS,GAAG;AACrB,WAAQ,IAAG,KAAKF,QAAS,EAAzB;AACD;;AArDiB;;ACxBpB;AACA;AACA;AACA;AACA;AACA;AAUA;AACA;AACA;AACA;AACA;;AAEA,MAAMtG,MAAI,GAAG,OAAb;AACA,MAAMsG,UAAQ,GAAG,UAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMS,cAAY,GAAG,WAArB;AAEA,MAAMC,gBAAgB,GAAG,2BAAzB;AAEA,MAAMC,WAAW,GAAI,QAAOT,WAAU,EAAtC;AACA,MAAMU,YAAY,GAAI,SAAQV,WAAU,EAAxC;AACA,MAAMW,sBAAoB,GAAI,QAAOX,WAAU,GAAEO,cAAa,EAA9D;AAEA,MAAMK,gBAAgB,GAAG,OAAzB;AACA,MAAMC,iBAAe,GAAG,MAAxB;AACA,MAAMC,iBAAe,GAAG,MAAxB;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,KAAN,SAAoBrB,aAApB,CAAkC;AAChC;AAEe,aAAJlG,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GAL+B;;;AAShCwH,EAAAA,KAAK,CAACjP,OAAD,EAAU;AACb,UAAMkP,WAAW,GAAGlP,OAAO,GAAG,KAAKmP,eAAL,CAAqBnP,OAArB,CAAH,GAAmC,KAAK6N,QAAnE;;AACA,UAAMuB,WAAW,GAAG,KAAKC,kBAAL,CAAwBH,WAAxB,CAApB;;AAEA,QAAIE,WAAW,KAAK,IAAhB,IAAwBA,WAAW,CAACrC,gBAAxC,EAA0D;AACxD;AACD;;AAED,SAAKuC,cAAL,CAAoBJ,WAApB;AACD,GAlB+B;;;AAsBhCC,EAAAA,eAAe,CAACnP,OAAD,EAAU;AACvB,WAAOiD,sBAAsB,CAACjD,OAAD,CAAtB,IAAmCA,OAAO,CAACuP,OAAR,CAAiB,IAAGV,gBAAiB,EAArC,CAA1C;AACD;;AAEDQ,EAAAA,kBAAkB,CAACrP,OAAD,EAAU;AAC1B,WAAOkK,YAAY,CAACwC,OAAb,CAAqB1M,OAArB,EAA8B0O,WAA9B,CAAP;AACD;;AAEDY,EAAAA,cAAc,CAACtP,OAAD,EAAU;AACtBA,IAAAA,OAAO,CAACiG,SAAR,CAAkB4C,MAAlB,CAAyBkG,iBAAzB;AAEA,UAAMV,UAAU,GAAGrO,OAAO,CAACiG,SAAR,CAAkBC,QAAlB,CAA2B4I,iBAA3B,CAAnB;;AACA,SAAKV,cAAL,CAAoB,MAAM,KAAKoB,eAAL,CAAqBxP,OAArB,CAA1B,EAAyDA,OAAzD,EAAkEqO,UAAlE;AACD;;AAEDmB,EAAAA,eAAe,CAACxP,OAAD,EAAU;AACvB,QAAIA,OAAO,CAACgB,UAAZ,EAAwB;AACtBhB,MAAAA,OAAO,CAACgB,UAAR,CAAmByO,WAAnB,CAA+BzP,OAA/B;AACD;;AAEDkK,IAAAA,YAAY,CAACwC,OAAb,CAAqB1M,OAArB,EAA8B2O,YAA9B;AACD,GA3C+B;;;AA+CV,SAAf/G,eAAe,CAAC/C,MAAD,EAAS;AAC7B,WAAO,KAAK6K,IAAL,CAAU,YAAY;AAC3B,UAAIC,IAAI,GAAG7B,IAAI,CAACvF,GAAL,CAAS,IAAT,EAAewF,UAAf,CAAX;;AAEA,UAAI,CAAC4B,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIX,KAAJ,CAAU,IAAV,CAAP;AACD;;AAED,UAAInK,MAAM,KAAK,OAAf,EAAwB;AACtB8K,QAAAA,IAAI,CAAC9K,MAAD,CAAJ,CAAa,IAAb;AACD;AACF,KAVM,CAAP;AAWD;;AAEmB,SAAb+K,aAAa,CAACC,aAAD,EAAgB;AAClC,WAAO,UAAU9F,KAAV,EAAiB;AACtB,UAAIA,KAAJ,EAAW;AACTA,QAAAA,KAAK,CAAC0D,cAAN;AACD;;AAEDoC,MAAAA,aAAa,CAACZ,KAAd,CAAoB,IAApB;AACD,KAND;AAOD;;AArE+B;AAwElC;AACA;AACA;AACA;AACA;;;AAEA/E,YAAY,CAACiC,EAAb,CAAgBlM,QAAhB,EAA0B2O,sBAA1B,EAAgDH,gBAAhD,EAAkEO,KAAK,CAACY,aAAN,CAAoB,IAAIZ,KAAJ,EAApB,CAAlE;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA3H,kBAAkB,CAAC2H,KAAD,CAAlB;;ACjIA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;;AAEA,MAAMvH,MAAI,GAAG,QAAb;AACA,MAAMsG,UAAQ,GAAG,WAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMS,cAAY,GAAG,WAArB;AAEA,MAAMsB,mBAAiB,GAAG,QAA1B;AAEA,MAAMC,sBAAoB,GAAG,2BAA7B;AAEA,MAAMnB,sBAAoB,GAAI,QAAOX,WAAU,GAAEO,cAAa,EAA9D;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMwB,MAAN,SAAqBrC,aAArB,CAAmC;AACjC;AAEe,aAAJlG,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GALgC;;;AASjCwI,EAAAA,MAAM,GAAG;AACP;AACA,SAAKpC,QAAL,CAAcqC,YAAd,CAA2B,cAA3B,EAA2C,KAAKrC,QAAL,CAAc5H,SAAd,CAAwBgK,MAAxB,CAA+BH,mBAA/B,CAA3C;AACD,GAZgC;;;AAgBX,SAAflI,eAAe,CAAC/C,MAAD,EAAS;AAC7B,WAAO,KAAK6K,IAAL,CAAU,YAAY;AAC3B,UAAIC,IAAI,GAAG7B,IAAI,CAACvF,GAAL,CAAS,IAAT,EAAewF,UAAf,CAAX;;AAEA,UAAI,CAAC4B,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIK,MAAJ,CAAW,IAAX,CAAP;AACD;;AAED,UAAInL,MAAM,KAAK,QAAf,EAAyB;AACvB8K,QAAAA,IAAI,CAAC9K,MAAD,CAAJ;AACD;AACF,KAVM,CAAP;AAWD;;AA5BgC;AA+BnC;AACA;AACA;AACA;AACA;;;AAEAqF,YAAY,CAACiC,EAAb,CAAgBlM,QAAhB,EAA0B2O,sBAA1B,EAAgDmB,sBAAhD,EAAsEhG,KAAK,IAAI;AAC7EA,EAAAA,KAAK,CAAC0D,cAAN;AAEA,QAAM0C,MAAM,GAAGpG,KAAK,CAACS,MAAN,CAAa+E,OAAb,CAAqBQ,sBAArB,CAAf;AAEA,MAAIJ,IAAI,GAAG7B,IAAI,CAACvF,GAAL,CAAS4H,MAAT,EAAiBpC,UAAjB,CAAX;;AACA,MAAI,CAAC4B,IAAL,EAAW;AACTA,IAAAA,IAAI,GAAG,IAAIK,MAAJ,CAAWG,MAAX,CAAP;AACD;;AAEDR,EAAAA,IAAI,CAACM,MAAL;AACD,CAXD;AAaA;AACA;AACA;AACA;AACA;AACA;;AAEA5I,kBAAkB,CAAC2I,MAAD,CAAlB;;AC5FA;AACA;AACA;AACA;AACA;AACA;AAEA,SAASI,aAAT,CAAuBC,GAAvB,EAA4B;AAC1B,MAAIA,GAAG,KAAK,MAAZ,EAAoB;AAClB,WAAO,IAAP;AACD;;AAED,MAAIA,GAAG,KAAK,OAAZ,EAAqB;AACnB,WAAO,KAAP;AACD;;AAED,MAAIA,GAAG,KAAK7M,MAAM,CAAC6M,GAAD,CAAN,CAAYrO,QAAZ,EAAZ,EAAoC;AAClC,WAAOwB,MAAM,CAAC6M,GAAD,CAAb;AACD;;AAED,MAAIA,GAAG,KAAK,EAAR,IAAcA,GAAG,KAAK,MAA1B,EAAkC;AAChC,WAAO,IAAP;AACD;;AAED,SAAOA,GAAP;AACD;;AAED,SAASC,gBAAT,CAA0BnI,GAA1B,EAA+B;AAC7B,SAAOA,GAAG,CAACyD,OAAJ,CAAY,QAAZ,EAAsB2E,GAAG,IAAK,IAAGA,GAAG,CAACrO,WAAJ,EAAkB,EAAnD,CAAP;AACD;;AAED,MAAMsO,WAAW,GAAG;AAClBC,EAAAA,gBAAgB,CAACzQ,OAAD,EAAUmI,GAAV,EAAe/C,KAAf,EAAsB;AACpCpF,IAAAA,OAAO,CAACkQ,YAAR,CAAsB,WAAUI,gBAAgB,CAACnI,GAAD,CAAM,EAAtD,EAAyD/C,KAAzD;AACD,GAHiB;;AAKlBsL,EAAAA,mBAAmB,CAAC1Q,OAAD,EAAUmI,GAAV,EAAe;AAChCnI,IAAAA,OAAO,CAAC2Q,eAAR,CAAyB,WAAUL,gBAAgB,CAACnI,GAAD,CAAM,EAAzD;AACD,GAPiB;;AASlByI,EAAAA,iBAAiB,CAAC5Q,OAAD,EAAU;AACzB,QAAI,CAACA,OAAL,EAAc;AACZ,aAAO,EAAP;AACD;;AAED,UAAM6Q,UAAU,GAAG,EAAnB;AAEA9L,IAAAA,MAAM,CAACC,IAAP,CAAYhF,OAAO,CAAC8Q,OAApB,EACGnQ,MADH,CACUwH,GAAG,IAAIA,GAAG,CAACtF,UAAJ,CAAe,IAAf,CADjB,EAEGoC,OAFH,CAEWkD,GAAG,IAAI;AACd,UAAI4I,OAAO,GAAG5I,GAAG,CAACyD,OAAJ,CAAY,KAAZ,EAAmB,EAAnB,CAAd;AACAmF,MAAAA,OAAO,GAAGA,OAAO,CAACC,MAAR,CAAe,CAAf,EAAkB9O,WAAlB,KAAkC6O,OAAO,CAACvE,KAAR,CAAc,CAAd,EAAiBuE,OAAO,CAAC9M,MAAzB,CAA5C;AACA4M,MAAAA,UAAU,CAACE,OAAD,CAAV,GAAsBX,aAAa,CAACpQ,OAAO,CAAC8Q,OAAR,CAAgB3I,GAAhB,CAAD,CAAnC;AACD,KANH;AAQA,WAAO0I,UAAP;AACD,GAzBiB;;AA2BlBI,EAAAA,gBAAgB,CAACjR,OAAD,EAAUmI,GAAV,EAAe;AAC7B,WAAOiI,aAAa,CAACpQ,OAAO,CAAC0C,YAAR,CAAsB,WAAU4N,gBAAgB,CAACnI,GAAD,CAAM,EAAtD,CAAD,CAApB;AACD,GA7BiB;;AA+BlB+I,EAAAA,MAAM,CAAClR,OAAD,EAAU;AACd,UAAMmR,IAAI,GAAGnR,OAAO,CAACoR,qBAAR,EAAb;AAEA,WAAO;AACLC,MAAAA,GAAG,EAAEF,IAAI,CAACE,GAAL,GAAWpR,QAAQ,CAAC8G,IAAT,CAAcuK,SADzB;AAELC,MAAAA,IAAI,EAAEJ,IAAI,CAACI,IAAL,GAAYtR,QAAQ,CAAC8G,IAAT,CAAcyK;AAF3B,KAAP;AAID,GAtCiB;;AAwClBC,EAAAA,QAAQ,CAACzR,OAAD,EAAU;AAChB,WAAO;AACLqR,MAAAA,GAAG,EAAErR,OAAO,CAAC0R,SADR;AAELH,MAAAA,IAAI,EAAEvR,OAAO,CAAC2R;AAFT,KAAP;AAID;;AA7CiB,CAApB;;AC/BA;AACA;AACA;AACA;AACA;AACA;AAiBA;AACA;AACA;AACA;AACA;;AAEA,MAAMlK,MAAI,GAAG,UAAb;AACA,MAAMsG,UAAQ,GAAG,aAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMS,cAAY,GAAG,WAArB;AAEA,MAAMoD,cAAc,GAAG,WAAvB;AACA,MAAMC,eAAe,GAAG,YAAxB;AACA,MAAMC,sBAAsB,GAAG,GAA/B;;AACA,MAAMC,eAAe,GAAG,EAAxB;AAEA,MAAMC,SAAO,GAAG;AACdC,EAAAA,QAAQ,EAAE,IADI;AAEdC,EAAAA,QAAQ,EAAE,IAFI;AAGdC,EAAAA,KAAK,EAAE,KAHO;AAIdC,EAAAA,KAAK,EAAE,OAJO;AAKdC,EAAAA,IAAI,EAAE,IALQ;AAMdC,EAAAA,KAAK,EAAE;AANO,CAAhB;AASA,MAAMC,aAAW,GAAG;AAClBN,EAAAA,QAAQ,EAAE,kBADQ;AAElBC,EAAAA,QAAQ,EAAE,SAFQ;AAGlBC,EAAAA,KAAK,EAAE,kBAHW;AAIlBC,EAAAA,KAAK,EAAE,kBAJW;AAKlBC,EAAAA,IAAI,EAAE,SALY;AAMlBC,EAAAA,KAAK,EAAE;AANW,CAApB;AASA,MAAME,UAAU,GAAG,MAAnB;AACA,MAAMC,UAAU,GAAG,MAAnB;AACA,MAAMC,cAAc,GAAG,MAAvB;AACA,MAAMC,eAAe,GAAG,OAAxB;AAEA,MAAMC,WAAW,GAAI,QAAO3E,WAAU,EAAtC;AACA,MAAM4E,UAAU,GAAI,OAAM5E,WAAU,EAApC;AACA,MAAM6E,aAAa,GAAI,UAAS7E,WAAU,EAA1C;AACA,MAAM8E,gBAAgB,GAAI,aAAY9E,WAAU,EAAhD;AACA,MAAM+E,gBAAgB,GAAI,aAAY/E,WAAU,EAAhD;AACA,MAAMgF,gBAAgB,GAAI,aAAYhF,WAAU,EAAhD;AACA,MAAMiF,eAAe,GAAI,YAAWjF,WAAU,EAA9C;AACA,MAAMkF,cAAc,GAAI,WAAUlF,WAAU,EAA5C;AACA,MAAMmF,iBAAiB,GAAI,cAAanF,WAAU,EAAlD;AACA,MAAMoF,eAAe,GAAI,YAAWpF,WAAU,EAA9C;AACA,MAAMqF,gBAAgB,GAAI,YAAWrF,WAAU,EAA/C;AACA,MAAMsF,qBAAmB,GAAI,OAAMtF,WAAU,GAAEO,cAAa,EAA5D;AACA,MAAMI,sBAAoB,GAAI,QAAOX,WAAU,GAAEO,cAAa,EAA9D;AAEA,MAAMgF,mBAAmB,GAAG,UAA5B;AACA,MAAM1D,mBAAiB,GAAG,QAA1B;AACA,MAAM2D,gBAAgB,GAAG,OAAzB;AACA,MAAMC,cAAc,GAAG,mBAAvB;AACA,MAAMC,gBAAgB,GAAG,qBAAzB;AACA,MAAMC,eAAe,GAAG,oBAAxB;AACA,MAAMC,eAAe,GAAG,oBAAxB;AACA,MAAMC,wBAAwB,GAAG,eAAjC;AAEA,MAAMC,iBAAe,GAAG,SAAxB;AACA,MAAMC,oBAAoB,GAAG,uBAA7B;AACA,MAAMC,aAAa,GAAG,gBAAtB;AACA,MAAMC,iBAAiB,GAAG,oBAA1B;AACA,MAAMC,kBAAkB,GAAG,0CAA3B;AACA,MAAMC,mBAAmB,GAAG,sBAA5B;AACA,MAAMC,kBAAkB,GAAG,kBAA3B;AACA,MAAMC,mBAAmB,GAAG,qCAA5B;AACA,MAAMC,kBAAkB,GAAG,2BAA3B;AAEA,MAAMC,kBAAkB,GAAG,OAA3B;AACA,MAAMC,gBAAgB,GAAG,KAAzB;AAEA;AACA;AACA;AACA;AACA;;AACA,MAAMC,QAAN,SAAuB/G,aAAvB,CAAqC;AACnCC,EAAAA,WAAW,CAAC5N,OAAD,EAAU6E,MAAV,EAAkB;AAC3B,UAAM7E,OAAN;AAEA,SAAK2U,MAAL,GAAc,IAAd;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA,SAAKC,cAAL,GAAsB,IAAtB;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,UAAL,GAAkB,KAAlB;AACA,SAAKC,YAAL,GAAoB,IAApB;AACA,SAAKC,WAAL,GAAmB,CAAnB;AACA,SAAKC,WAAL,GAAmB,CAAnB;AAEA,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgBvQ,MAAhB,CAAf;AACA,SAAKwQ,kBAAL,GAA0BxV,cAAc,CAACW,OAAf,CAAuB4T,mBAAvB,EAA4C,KAAKvG,QAAjD,CAA1B;AACA,SAAKyH,eAAL,GAAuB,kBAAkBrV,QAAQ,CAACC,eAA3B,IAA8CqV,SAAS,CAACC,cAAV,GAA2B,CAAhG;AACA,SAAKC,aAAL,GAAqB3J,OAAO,CAACzI,MAAM,CAACqS,YAAR,CAA5B;;AAEA,SAAKC,kBAAL;AACD,GAnBkC;;;AAuBjB,aAAP3D,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD;;AAEc,aAAJvK,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GA7BkC;;;AAiCnCjG,EAAAA,IAAI,GAAG;AACL,QAAI,CAAC,KAAKuT,UAAV,EAAsB;AACpB,WAAKa,MAAL,CAAYpD,UAAZ;AACD;AACF;;AAEDqD,EAAAA,eAAe,GAAG;AAChB;AACA;AACA,QAAI,CAAC5V,QAAQ,CAAC6V,MAAV,IAAoBpQ,SAAS,CAAC,KAAKmI,QAAN,CAAjC,EAAkD;AAChD,WAAKrM,IAAL;AACD;AACF;;AAEDH,EAAAA,IAAI,GAAG;AACL,QAAI,CAAC,KAAK0T,UAAV,EAAsB;AACpB,WAAKa,MAAL,CAAYnD,UAAZ;AACD;AACF;;AAEDL,EAAAA,KAAK,CAACrI,KAAD,EAAQ;AACX,QAAI,CAACA,KAAL,EAAY;AACV,WAAK+K,SAAL,GAAiB,IAAjB;AACD;;AAED,QAAIjV,cAAc,CAACW,OAAf,CAAuB2T,kBAAvB,EAA2C,KAAKtG,QAAhD,CAAJ,EAA+D;AAC7DlK,MAAAA,oBAAoB,CAAC,KAAKkK,QAAN,CAApB;AACA,WAAKkI,KAAL,CAAW,IAAX;AACD;;AAEDC,IAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;AACA,SAAKA,SAAL,GAAiB,IAAjB;AACD;;AAEDmB,EAAAA,KAAK,CAAChM,KAAD,EAAQ;AACX,QAAI,CAACA,KAAL,EAAY;AACV,WAAK+K,SAAL,GAAiB,KAAjB;AACD;;AAED,QAAI,KAAKF,SAAT,EAAoB;AAClBoB,MAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;AACA,WAAKA,SAAL,GAAiB,IAAjB;AACD;;AAED,QAAI,KAAKO,OAAL,IAAgB,KAAKA,OAAL,CAAalD,QAA7B,IAAyC,CAAC,KAAK6C,SAAnD,EAA8D;AAC5D,WAAKmB,eAAL;;AAEA,WAAKrB,SAAL,GAAiBsB,WAAW,CAC1B,CAACjW,QAAQ,CAACkW,eAAT,GAA2B,KAAKN,eAAhC,GAAkD,KAAKrU,IAAxD,EAA8D4U,IAA9D,CAAmE,IAAnE,CAD0B,EAE1B,KAAKjB,OAAL,CAAalD,QAFa,CAA5B;AAID;AACF;;AAEDoE,EAAAA,EAAE,CAACC,KAAD,EAAQ;AACR,SAAKzB,cAAL,GAAsBhV,cAAc,CAACW,OAAf,CAAuBwT,oBAAvB,EAA6C,KAAKnG,QAAlD,CAAtB;;AACA,UAAM0I,WAAW,GAAG,KAAKC,aAAL,CAAmB,KAAK3B,cAAxB,CAApB;;AAEA,QAAIyB,KAAK,GAAG,KAAK3B,MAAL,CAAY1Q,MAAZ,GAAqB,CAA7B,IAAkCqS,KAAK,GAAG,CAA9C,EAAiD;AAC/C;AACD;;AAED,QAAI,KAAKvB,UAAT,EAAqB;AACnB7K,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgCgF,UAAhC,EAA4C,MAAM,KAAKwD,EAAL,CAAQC,KAAR,CAAlD;AACA;AACD;;AAED,QAAIC,WAAW,KAAKD,KAApB,EAA2B;AACzB,WAAKlE,KAAL;AACA,WAAK2D,KAAL;AACA;AACD;;AAED,UAAMU,KAAK,GAAGH,KAAK,GAAGC,WAAR,GACZ/D,UADY,GAEZC,UAFF;;AAIA,SAAKmD,MAAL,CAAYa,KAAZ,EAAmB,KAAK9B,MAAL,CAAY2B,KAAZ,CAAnB;AACD,GA/GkC;;;AAmHnClB,EAAAA,UAAU,CAACvQ,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmN,SADI;AAEP,SAAGnN;AAFI,KAAT;AAIAF,IAAAA,eAAe,CAAC8C,MAAD,EAAO5C,MAAP,EAAe0N,aAAf,CAAf;AACA,WAAO1N,MAAP;AACD;;AAED6R,EAAAA,YAAY,GAAG;AACb,UAAMC,SAAS,GAAGtU,IAAI,CAACuU,GAAL,CAAS,KAAK1B,WAAd,CAAlB;;AAEA,QAAIyB,SAAS,IAAI5E,eAAjB,EAAkC;AAChC;AACD;;AAED,UAAM8E,SAAS,GAAGF,SAAS,GAAG,KAAKzB,WAAnC;AAEA,SAAKA,WAAL,GAAmB,CAAnB;;AAEA,QAAI,CAAC2B,SAAL,EAAgB;AACd;AACD;;AAED,SAAKjB,MAAL,CAAYiB,SAAS,GAAG,CAAZ,GAAgBlE,eAAhB,GAAkCD,cAA9C;AACD;;AAEDiD,EAAAA,kBAAkB,GAAG;AACnB,QAAI,KAAKR,OAAL,CAAajD,QAAjB,EAA2B;AACzBhI,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BiF,aAA/B,EAA8C/I,KAAK,IAAI,KAAK+M,QAAL,CAAc/M,KAAd,CAAvD;AACD;;AAED,QAAI,KAAKoL,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;AAClClI,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BkF,gBAA/B,EAAiDhJ,KAAK,IAAI,KAAKqI,KAAL,CAAWrI,KAAX,CAA1D;AACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BmF,gBAA/B,EAAiDjJ,KAAK,IAAI,KAAKgM,KAAL,CAAWhM,KAAX,CAA1D;AACD;;AAED,QAAI,KAAKoL,OAAL,CAAa7C,KAAb,IAAsB,KAAKgD,eAA/B,EAAgD;AAC9C,WAAKyB,uBAAL;AACD;AACF;;AAEDA,EAAAA,uBAAuB,GAAG;AACxB,UAAMC,KAAK,GAAGjN,KAAK,IAAI;AACrB,UAAI,KAAK0L,aAAL,KAAuB1L,KAAK,CAACkN,WAAN,KAAsBxC,gBAAtB,IAA0C1K,KAAK,CAACkN,WAAN,KAAsBzC,kBAAvF,CAAJ,EAAgH;AAC9G,aAAKS,WAAL,GAAmBlL,KAAK,CAACmN,OAAzB;AACD,OAFD,MAEO,IAAI,CAAC,KAAKzB,aAAV,EAAyB;AAC9B,aAAKR,WAAL,GAAmBlL,KAAK,CAACoN,OAAN,CAAc,CAAd,EAAiBD,OAApC;AACD;AACF,KAND;;AAQA,UAAME,IAAI,GAAGrN,KAAK,IAAI;AACpB;AACA,WAAKmL,WAAL,GAAmBnL,KAAK,CAACoN,OAAN,IAAiBpN,KAAK,CAACoN,OAAN,CAAclT,MAAd,GAAuB,CAAxC,GACjB,CADiB,GAEjB8F,KAAK,CAACoN,OAAN,CAAc,CAAd,EAAiBD,OAAjB,GAA2B,KAAKjC,WAFlC;AAGD,KALD;;AAOA,UAAMoC,GAAG,GAAGtN,KAAK,IAAI;AACnB,UAAI,KAAK0L,aAAL,KAAuB1L,KAAK,CAACkN,WAAN,KAAsBxC,gBAAtB,IAA0C1K,KAAK,CAACkN,WAAN,KAAsBzC,kBAAvF,CAAJ,EAAgH;AAC9G,aAAKU,WAAL,GAAmBnL,KAAK,CAACmN,OAAN,GAAgB,KAAKjC,WAAxC;AACD;;AAED,WAAKyB,YAAL;;AACA,UAAI,KAAKvB,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,aAAKA,KAAL;;AACA,YAAI,KAAK4C,YAAT,EAAuB;AACrBsC,UAAAA,YAAY,CAAC,KAAKtC,YAAN,CAAZ;AACD;;AAED,aAAKA,YAAL,GAAoBtQ,UAAU,CAACqF,KAAK,IAAI,KAAKgM,KAAL,CAAWhM,KAAX,CAAV,EAA6B+H,sBAAsB,GAAG,KAAKqD,OAAL,CAAalD,QAAnE,CAA9B;AACD;AACF,KAtBD;;AAwBApS,IAAAA,cAAc,CAACC,IAAf,CAAoBoU,iBAApB,EAAuC,KAAKrG,QAA5C,EAAsD5I,OAAtD,CAA8DsS,OAAO,IAAI;AACvErN,MAAAA,YAAY,CAACiC,EAAb,CAAgBoL,OAAhB,EAAyBjE,gBAAzB,EAA2CkE,CAAC,IAAIA,CAAC,CAAC/J,cAAF,EAAhD;AACD,KAFD;;AAIA,QAAI,KAAKgI,aAAT,EAAwB;AACtBvL,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BuF,iBAA/B,EAAkDrJ,KAAK,IAAIiN,KAAK,CAACjN,KAAD,CAAhE;AACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BwF,eAA/B,EAAgDtJ,KAAK,IAAIsN,GAAG,CAACtN,KAAD,CAA5D;;AAEA,WAAK8D,QAAL,CAAc5H,SAAd,CAAwBwR,GAAxB,CAA4B3D,wBAA5B;AACD,KALD,MAKO;AACL5J,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BoF,gBAA/B,EAAiDlJ,KAAK,IAAIiN,KAAK,CAACjN,KAAD,CAA/D;AACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BqF,eAA/B,EAAgDnJ,KAAK,IAAIqN,IAAI,CAACrN,KAAD,CAA7D;AACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BsF,cAA/B,EAA+CpJ,KAAK,IAAIsN,GAAG,CAACtN,KAAD,CAA3D;AACD;AACF;;AAED+M,EAAAA,QAAQ,CAAC/M,KAAD,EAAQ;AACd,QAAI,kBAAkBxE,IAAlB,CAAuBwE,KAAK,CAACS,MAAN,CAAakN,OAApC,CAAJ,EAAkD;AAChD;AACD;;AAED,QAAI3N,KAAK,CAAC5B,GAAN,KAAcyJ,cAAlB,EAAkC;AAChC7H,MAAAA,KAAK,CAAC0D,cAAN;;AACA,WAAKmI,MAAL,CAAYjD,eAAZ;AACD,KAHD,MAGO,IAAI5I,KAAK,CAAC5B,GAAN,KAAc0J,eAAlB,EAAmC;AACxC9H,MAAAA,KAAK,CAAC0D,cAAN;;AACA,WAAKmI,MAAL,CAAYlD,cAAZ;AACD;AACF;;AAED8D,EAAAA,aAAa,CAACxW,OAAD,EAAU;AACrB,SAAK2U,MAAL,GAAc3U,OAAO,IAAIA,OAAO,CAACgB,UAAnB,GACZnB,cAAc,CAACC,IAAf,CAAoBmU,aAApB,EAAmCjU,OAAO,CAACgB,UAA3C,CADY,GAEZ,EAFF;AAIA,WAAO,KAAK2T,MAAL,CAAYgD,OAAZ,CAAoB3X,OAApB,CAAP;AACD;;AAED4X,EAAAA,eAAe,CAACnB,KAAD,EAAQoB,aAAR,EAAuB;AACpC,UAAMC,MAAM,GAAGrB,KAAK,KAAKjE,UAAzB;AACA,UAAMuF,MAAM,GAAGtB,KAAK,KAAKhE,UAAzB;;AACA,UAAM8D,WAAW,GAAG,KAAKC,aAAL,CAAmBqB,aAAnB,CAApB;;AACA,UAAMG,aAAa,GAAG,KAAKrD,MAAL,CAAY1Q,MAAZ,GAAqB,CAA3C;AACA,UAAMgU,aAAa,GAAIF,MAAM,IAAIxB,WAAW,KAAK,CAA3B,IAAkCuB,MAAM,IAAIvB,WAAW,KAAKyB,aAAlF;;AAEA,QAAIC,aAAa,IAAI,CAAC,KAAK9C,OAAL,CAAa9C,IAAnC,EAAyC;AACvC,aAAOwF,aAAP;AACD;;AAED,UAAMK,KAAK,GAAGH,MAAM,GAAG,CAAC,CAAJ,GAAQ,CAA5B;AACA,UAAMI,SAAS,GAAG,CAAC5B,WAAW,GAAG2B,KAAf,IAAwB,KAAKvD,MAAL,CAAY1Q,MAAtD;AAEA,WAAOkU,SAAS,KAAK,CAAC,CAAf,GACL,KAAKxD,MAAL,CAAY,KAAKA,MAAL,CAAY1Q,MAAZ,GAAqB,CAAjC,CADK,GAEL,KAAK0Q,MAAL,CAAYwD,SAAZ,CAFF;AAGD;;AAEDC,EAAAA,kBAAkB,CAAC3M,aAAD,EAAgB4M,kBAAhB,EAAoC;AACpD,UAAMC,WAAW,GAAG,KAAK9B,aAAL,CAAmB/K,aAAnB,CAApB;;AACA,UAAM8M,SAAS,GAAG,KAAK/B,aAAL,CAAmB3W,cAAc,CAACW,OAAf,CAAuBwT,oBAAvB,EAA6C,KAAKnG,QAAlD,CAAnB,CAAlB;;AAEA,WAAO3D,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC+E,WAApC,EAAiD;AACtDnH,MAAAA,aADsD;AAEtDoL,MAAAA,SAAS,EAAEwB,kBAF2C;AAGtDzP,MAAAA,IAAI,EAAE2P,SAHgD;AAItDlC,MAAAA,EAAE,EAAEiC;AAJkD,KAAjD,CAAP;AAMD;;AAEDE,EAAAA,0BAA0B,CAACxY,OAAD,EAAU;AAClC,QAAI,KAAKqV,kBAAT,EAA6B;AAC3B,YAAMoD,eAAe,GAAG5Y,cAAc,CAACW,OAAf,CAAuBuT,iBAAvB,EAAwC,KAAKsB,kBAA7C,CAAxB;AAEAoD,MAAAA,eAAe,CAACxS,SAAhB,CAA0B4C,MAA1B,CAAiCiH,mBAAjC;AACA2I,MAAAA,eAAe,CAAC9H,eAAhB,CAAgC,cAAhC;AAEA,YAAM+H,UAAU,GAAG7Y,cAAc,CAACC,IAAf,CAAoBuU,kBAApB,EAAwC,KAAKgB,kBAA7C,CAAnB;;AAEA,WAAK,IAAI5K,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiO,UAAU,CAACzU,MAA/B,EAAuCwG,CAAC,EAAxC,EAA4C;AAC1C,YAAIjH,MAAM,CAACmV,QAAP,CAAgBD,UAAU,CAACjO,CAAD,CAAV,CAAc/H,YAAd,CAA2B,kBAA3B,CAAhB,EAAgE,EAAhE,MAAwE,KAAK8T,aAAL,CAAmBxW,OAAnB,CAA5E,EAAyG;AACvG0Y,UAAAA,UAAU,CAACjO,CAAD,CAAV,CAAcxE,SAAd,CAAwBwR,GAAxB,CAA4B3H,mBAA5B;AACA4I,UAAAA,UAAU,CAACjO,CAAD,CAAV,CAAcyF,YAAd,CAA2B,cAA3B,EAA2C,MAA3C;AACA;AACD;AACF;AACF;AACF;;AAED+F,EAAAA,eAAe,GAAG;AAChB,UAAMjW,OAAO,GAAG,KAAK6U,cAAL,IAAuBhV,cAAc,CAACW,OAAf,CAAuBwT,oBAAvB,EAA6C,KAAKnG,QAAlD,CAAvC;;AAEA,QAAI,CAAC7N,OAAL,EAAc;AACZ;AACD;;AAED,UAAM4Y,eAAe,GAAGpV,MAAM,CAACmV,QAAP,CAAgB3Y,OAAO,CAAC0C,YAAR,CAAqB,kBAArB,CAAhB,EAA0D,EAA1D,CAAxB;;AAEA,QAAIkW,eAAJ,EAAqB;AACnB,WAAKzD,OAAL,CAAa0D,eAAb,GAA+B,KAAK1D,OAAL,CAAa0D,eAAb,IAAgC,KAAK1D,OAAL,CAAalD,QAA5E;AACA,WAAKkD,OAAL,CAAalD,QAAb,GAAwB2G,eAAxB;AACD,KAHD,MAGO;AACL,WAAKzD,OAAL,CAAalD,QAAb,GAAwB,KAAKkD,OAAL,CAAa0D,eAAb,IAAgC,KAAK1D,OAAL,CAAalD,QAArE;AACD;AACF;;AAED2D,EAAAA,MAAM,CAACkD,gBAAD,EAAmB9Y,OAAnB,EAA4B;AAChC,UAAMyW,KAAK,GAAG,KAAKsC,iBAAL,CAAuBD,gBAAvB,CAAd;;AACA,UAAMjB,aAAa,GAAGhY,cAAc,CAACW,OAAf,CAAuBwT,oBAAvB,EAA6C,KAAKnG,QAAlD,CAAtB;;AACA,UAAMmL,kBAAkB,GAAG,KAAKxC,aAAL,CAAmBqB,aAAnB,CAA3B;;AACA,UAAMoB,WAAW,GAAGjZ,OAAO,IAAI,KAAK4X,eAAL,CAAqBnB,KAArB,EAA4BoB,aAA5B,CAA/B;;AAEA,UAAMqB,gBAAgB,GAAG,KAAK1C,aAAL,CAAmByC,WAAnB,CAAzB;;AACA,UAAME,SAAS,GAAGrN,OAAO,CAAC,KAAK8I,SAAN,CAAzB;AAEA,UAAMkD,MAAM,GAAGrB,KAAK,KAAKjE,UAAzB;AACA,UAAM4G,oBAAoB,GAAGtB,MAAM,GAAGnE,gBAAH,GAAsBD,cAAzD;AACA,UAAM2F,cAAc,GAAGvB,MAAM,GAAGlE,eAAH,GAAqBC,eAAlD;;AACA,UAAMwE,kBAAkB,GAAG,KAAKiB,iBAAL,CAAuB7C,KAAvB,CAA3B;;AAEA,QAAIwC,WAAW,IAAIA,WAAW,CAAChT,SAAZ,CAAsBC,QAAtB,CAA+B4J,mBAA/B,CAAnB,EAAsE;AACpE,WAAKiF,UAAL,GAAkB,KAAlB;AACA;AACD;;AAED,UAAMwE,UAAU,GAAG,KAAKnB,kBAAL,CAAwBa,WAAxB,EAAqCZ,kBAArC,CAAnB;;AACA,QAAIkB,UAAU,CAACxM,gBAAf,EAAiC;AAC/B;AACD;;AAED,QAAI,CAAC8K,aAAD,IAAkB,CAACoB,WAAvB,EAAoC;AAClC;AACA;AACD;;AAED,SAAKlE,UAAL,GAAkB,IAAlB;;AAEA,QAAIoE,SAAJ,EAAe;AACb,WAAK/G,KAAL;AACD;;AAED,SAAKoG,0BAAL,CAAgCS,WAAhC;;AACA,SAAKpE,cAAL,GAAsBoE,WAAtB;;AAEA,UAAMO,gBAAgB,GAAG,MAAM;AAC7BtP,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCgF,UAApC,EAAgD;AAC9CpH,QAAAA,aAAa,EAAEwN,WAD+B;AAE9CpC,QAAAA,SAAS,EAAEwB,kBAFmC;AAG9CzP,QAAAA,IAAI,EAAEoQ,kBAHwC;AAI9C3C,QAAAA,EAAE,EAAE6C;AAJ0C,OAAhD;AAMD,KAPD;;AASA,QAAI,KAAKrL,QAAL,CAAc5H,SAAd,CAAwBC,QAAxB,CAAiCuN,gBAAjC,CAAJ,EAAwD;AACtDwF,MAAAA,WAAW,CAAChT,SAAZ,CAAsBwR,GAAtB,CAA0B4B,cAA1B;AAEA1S,MAAAA,MAAM,CAACsS,WAAD,CAAN;AAEApB,MAAAA,aAAa,CAAC5R,SAAd,CAAwBwR,GAAxB,CAA4B2B,oBAA5B;AACAH,MAAAA,WAAW,CAAChT,SAAZ,CAAsBwR,GAAtB,CAA0B2B,oBAA1B;;AAEA,YAAMK,gBAAgB,GAAG,MAAM;AAC7BR,QAAAA,WAAW,CAAChT,SAAZ,CAAsB4C,MAAtB,CAA6BuQ,oBAA7B,EAAmDC,cAAnD;AACAJ,QAAAA,WAAW,CAAChT,SAAZ,CAAsBwR,GAAtB,CAA0B3H,mBAA1B;AAEA+H,QAAAA,aAAa,CAAC5R,SAAd,CAAwB4C,MAAxB,CAA+BiH,mBAA/B,EAAkDuJ,cAAlD,EAAkED,oBAAlE;AAEA,aAAKrE,UAAL,GAAkB,KAAlB;AAEArQ,QAAAA,UAAU,CAAC8U,gBAAD,EAAmB,CAAnB,CAAV;AACD,OATD;;AAWA,WAAKpL,cAAL,CAAoBqL,gBAApB,EAAsC5B,aAAtC,EAAqD,IAArD;AACD,KApBD,MAoBO;AACLA,MAAAA,aAAa,CAAC5R,SAAd,CAAwB4C,MAAxB,CAA+BiH,mBAA/B;AACAmJ,MAAAA,WAAW,CAAChT,SAAZ,CAAsBwR,GAAtB,CAA0B3H,mBAA1B;AAEA,WAAKiF,UAAL,GAAkB,KAAlB;AACAyE,MAAAA,gBAAgB;AACjB;;AAED,QAAIL,SAAJ,EAAe;AACb,WAAKpD,KAAL;AACD;AACF;;AAEDgD,EAAAA,iBAAiB,CAAClC,SAAD,EAAY;AAC3B,QAAI,CAAC,CAAClE,eAAD,EAAkBD,cAAlB,EAAkC9P,QAAlC,CAA2CiU,SAA3C,CAAL,EAA4D;AAC1D,aAAOA,SAAP;AACD;;AAED,QAAI1P,KAAK,EAAT,EAAa;AACX,aAAO0P,SAAS,KAAKnE,cAAd,GAA+BD,UAA/B,GAA4CD,UAAnD;AACD;;AAED,WAAOqE,SAAS,KAAKnE,cAAd,GAA+BF,UAA/B,GAA4CC,UAAnD;AACD;;AAED6G,EAAAA,iBAAiB,CAAC7C,KAAD,EAAQ;AACvB,QAAI,CAAC,CAACjE,UAAD,EAAaC,UAAb,EAAyB7P,QAAzB,CAAkC6T,KAAlC,CAAL,EAA+C;AAC7C,aAAOA,KAAP;AACD;;AAED,QAAItP,KAAK,EAAT,EAAa;AACX,aAAOsP,KAAK,KAAKhE,UAAV,GAAuBC,cAAvB,GAAwCC,eAA/C;AACD;;AAED,WAAO8D,KAAK,KAAKhE,UAAV,GAAuBE,eAAvB,GAAyCD,cAAhD;AACD,GApZkC;;;AAwZX,SAAjBgH,iBAAiB,CAAC1Z,OAAD,EAAU6E,MAAV,EAAkB;AACxC,QAAI8K,IAAI,GAAG7B,IAAI,CAACvF,GAAL,CAASvI,OAAT,EAAkB+N,UAAlB,CAAX;AACA,QAAIoH,OAAO,GAAG,EACZ,GAAGnD,SADS;AAEZ,SAAGxB,WAAW,CAACI,iBAAZ,CAA8B5Q,OAA9B;AAFS,KAAd;;AAKA,QAAI,OAAO6E,MAAP,KAAkB,QAAtB,EAAgC;AAC9BsQ,MAAAA,OAAO,GAAG,EACR,GAAGA,OADK;AAER,WAAGtQ;AAFK,OAAV;AAID;;AAED,UAAM8U,MAAM,GAAG,OAAO9U,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsCsQ,OAAO,CAAChD,KAA7D;;AAEA,QAAI,CAACxC,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAI+E,QAAJ,CAAa1U,OAAb,EAAsBmV,OAAtB,CAAP;AACD;;AAED,QAAI,OAAOtQ,MAAP,KAAkB,QAAtB,EAAgC;AAC9B8K,MAAAA,IAAI,CAAC0G,EAAL,CAAQxR,MAAR;AACD,KAFD,MAEO,IAAI,OAAO8U,MAAP,KAAkB,QAAtB,EAAgC;AACrC,UAAI,OAAOhK,IAAI,CAACgK,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAInU,SAAJ,CAAe,oBAAmBmU,MAAO,GAAzC,CAAN;AACD;;AAEDhK,MAAAA,IAAI,CAACgK,MAAD,CAAJ;AACD,KANM,MAMA,IAAIxE,OAAO,CAAClD,QAAR,IAAoBkD,OAAO,CAACyE,IAAhC,EAAsC;AAC3CjK,MAAAA,IAAI,CAACyC,KAAL;AACAzC,MAAAA,IAAI,CAACoG,KAAL;AACD;AACF;;AAEqB,SAAfnO,eAAe,CAAC/C,MAAD,EAAS;AAC7B,WAAO,KAAK6K,IAAL,CAAU,YAAY;AAC3BgF,MAAAA,QAAQ,CAACgF,iBAAT,CAA2B,IAA3B,EAAiC7U,MAAjC;AACD,KAFM,CAAP;AAGD;;AAEyB,SAAnBgV,mBAAmB,CAAC9P,KAAD,EAAQ;AAChC,UAAMS,MAAM,GAAGvH,sBAAsB,CAAC,IAAD,CAArC;;AAEA,QAAI,CAACuH,MAAD,IAAW,CAACA,MAAM,CAACvE,SAAP,CAAiBC,QAAjB,CAA0BsN,mBAA1B,CAAhB,EAAgE;AAC9D;AACD;;AAED,UAAM3O,MAAM,GAAG,EACb,GAAG2L,WAAW,CAACI,iBAAZ,CAA8BpG,MAA9B,CADU;AAEb,SAAGgG,WAAW,CAACI,iBAAZ,CAA8B,IAA9B;AAFU,KAAf;AAIA,UAAMkJ,UAAU,GAAG,KAAKpX,YAAL,CAAkB,kBAAlB,CAAnB;;AAEA,QAAIoX,UAAJ,EAAgB;AACdjV,MAAAA,MAAM,CAACoN,QAAP,GAAkB,KAAlB;AACD;;AAEDyC,IAAAA,QAAQ,CAACgF,iBAAT,CAA2BlP,MAA3B,EAAmC3F,MAAnC;;AAEA,QAAIiV,UAAJ,EAAgB;AACdhM,MAAAA,IAAI,CAACvF,GAAL,CAASiC,MAAT,EAAiBuD,UAAjB,EAA2BsI,EAA3B,CAA8ByD,UAA9B;AACD;;AAED/P,IAAAA,KAAK,CAAC0D,cAAN;AACD;;AAxdkC;AA2drC;AACA;AACA;AACA;AACA;;;AAEAvD,YAAY,CAACiC,EAAb,CAAgBlM,QAAhB,EAA0B2O,sBAA1B,EAAgD0F,mBAAhD,EAAqEI,QAAQ,CAACmF,mBAA9E;AAEA3P,YAAY,CAACiC,EAAb,CAAgB9I,MAAhB,EAAwBkQ,qBAAxB,EAA6C,MAAM;AACjD,QAAMwG,SAAS,GAAGla,cAAc,CAACC,IAAf,CAAoByU,kBAApB,CAAlB;;AAEA,OAAK,IAAI9J,CAAC,GAAG,CAAR,EAAWK,GAAG,GAAGiP,SAAS,CAAC9V,MAAhC,EAAwCwG,CAAC,GAAGK,GAA5C,EAAiDL,CAAC,EAAlD,EAAsD;AACpDiK,IAAAA,QAAQ,CAACgF,iBAAT,CAA2BK,SAAS,CAACtP,CAAD,CAApC,EAAyCqD,IAAI,CAACvF,GAAL,CAASwR,SAAS,CAACtP,CAAD,CAAlB,EAAuBsD,UAAvB,CAAzC;AACD;AACF,CAND;AAQA;AACA;AACA;AACA;AACA;AACA;;AAEA1G,kBAAkB,CAACqN,QAAD,CAAlB;;ACxlBA;AACA;AACA;AACA;AACA;AACA;AAgBA;AACA;AACA;AACA;AACA;;AAEA,MAAMjN,MAAI,GAAG,UAAb;AACA,MAAMsG,UAAQ,GAAG,aAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMS,cAAY,GAAG,WAArB;AAEA,MAAMwD,SAAO,GAAG;AACd/B,EAAAA,MAAM,EAAE,IADM;AAEd+J,EAAAA,MAAM,EAAE;AAFM,CAAhB;AAKA,MAAMzH,aAAW,GAAG;AAClBtC,EAAAA,MAAM,EAAE,SADU;AAElB+J,EAAAA,MAAM,EAAE;AAFU,CAApB;AAKA,MAAMC,YAAU,GAAI,OAAMhM,WAAU,EAApC;AACA,MAAMiM,aAAW,GAAI,QAAOjM,WAAU,EAAtC;AACA,MAAMkM,YAAU,GAAI,OAAMlM,WAAU,EAApC;AACA,MAAMmM,cAAY,GAAI,SAAQnM,WAAU,EAAxC;AACA,MAAMW,sBAAoB,GAAI,QAAOX,WAAU,GAAEO,cAAa,EAA9D;AAEA,MAAMO,iBAAe,GAAG,MAAxB;AACA,MAAMsL,mBAAmB,GAAG,UAA5B;AACA,MAAMC,qBAAqB,GAAG,YAA9B;AACA,MAAMC,oBAAoB,GAAG,WAA7B;AAEA,MAAMC,KAAK,GAAG,OAAd;AACA,MAAMC,MAAM,GAAG,QAAf;AAEA,MAAMC,gBAAgB,GAAG,oBAAzB;AACA,MAAM3K,sBAAoB,GAAG,6BAA7B;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAM4K,QAAN,SAAuBhN,aAAvB,CAAqC;AACnCC,EAAAA,WAAW,CAAC5N,OAAD,EAAU6E,MAAV,EAAkB;AAC3B,UAAM7E,OAAN;AAEA,SAAK4a,gBAAL,GAAwB,KAAxB;AACA,SAAKzF,OAAL,GAAe,KAAKC,UAAL,CAAgBvQ,MAAhB,CAAf;AACA,SAAKgW,aAAL,GAAqBhb,cAAc,CAACC,IAAf,CAClB,GAAEiQ,sBAAqB,WAAU,KAAKlC,QAAL,CAAciN,EAAG,KAAnD,GACC,GAAE/K,sBAAqB,qBAAoB,KAAKlC,QAAL,CAAciN,EAAG,IAF1C,CAArB;AAKA,UAAMC,UAAU,GAAGlb,cAAc,CAACC,IAAf,CAAoBiQ,sBAApB,CAAnB;;AAEA,SAAK,IAAItF,CAAC,GAAG,CAAR,EAAWK,GAAG,GAAGiQ,UAAU,CAAC9W,MAAjC,EAAyCwG,CAAC,GAAGK,GAA7C,EAAkDL,CAAC,EAAnD,EAAuD;AACrD,YAAMuQ,IAAI,GAAGD,UAAU,CAACtQ,CAAD,CAAvB;AACA,YAAM1K,QAAQ,GAAGiD,sBAAsB,CAACgY,IAAD,CAAvC;AACA,YAAMC,aAAa,GAAGpb,cAAc,CAACC,IAAf,CAAoBC,QAApB,EACnBY,MADmB,CACZua,SAAS,IAAIA,SAAS,KAAK,KAAKrN,QADpB,CAAtB;;AAGA,UAAI9N,QAAQ,KAAK,IAAb,IAAqBkb,aAAa,CAAChX,MAAvC,EAA+C;AAC7C,aAAKkX,SAAL,GAAiBpb,QAAjB;;AACA,aAAK8a,aAAL,CAAmBzZ,IAAnB,CAAwB4Z,IAAxB;AACD;AACF;;AAED,SAAKI,OAAL,GAAe,KAAKjG,OAAL,CAAa6E,MAAb,GAAsB,KAAKqB,UAAL,EAAtB,GAA0C,IAAzD;;AAEA,QAAI,CAAC,KAAKlG,OAAL,CAAa6E,MAAlB,EAA0B;AACxB,WAAKsB,yBAAL,CAA+B,KAAKzN,QAApC,EAA8C,KAAKgN,aAAnD;AACD;;AAED,QAAI,KAAK1F,OAAL,CAAalF,MAAjB,EAAyB;AACvB,WAAKA,MAAL;AACD;AACF,GAlCkC;;;AAsCjB,aAAP+B,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD;;AAEc,aAAJvK,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GA5CkC;;;AAgDnCwI,EAAAA,MAAM,GAAG;AACP,QAAI,KAAKpC,QAAL,CAAc5H,SAAd,CAAwBC,QAAxB,CAAiC6I,iBAAjC,CAAJ,EAAuD;AACrD,WAAKwM,IAAL;AACD,KAFD,MAEO;AACL,WAAKC,IAAL;AACD;AACF;;AAEDA,EAAAA,IAAI,GAAG;AACL,QAAI,KAAKZ,gBAAL,IAAyB,KAAK/M,QAAL,CAAc5H,SAAd,CAAwBC,QAAxB,CAAiC6I,iBAAjC,CAA7B,EAAgF;AAC9E;AACD;;AAED,QAAI0M,OAAJ;AACA,QAAIC,WAAJ;;AAEA,QAAI,KAAKN,OAAT,EAAkB;AAChBK,MAAAA,OAAO,GAAG5b,cAAc,CAACC,IAAf,CAAoB4a,gBAApB,EAAsC,KAAKU,OAA3C,EACPza,MADO,CACAqa,IAAI,IAAI;AACd,YAAI,OAAO,KAAK7F,OAAL,CAAa6E,MAApB,KAA+B,QAAnC,EAA6C;AAC3C,iBAAOgB,IAAI,CAACtY,YAAL,CAAkB,gBAAlB,MAAwC,KAAKyS,OAAL,CAAa6E,MAA5D;AACD;;AAED,eAAOgB,IAAI,CAAC/U,SAAL,CAAeC,QAAf,CAAwBmU,mBAAxB,CAAP;AACD,OAPO,CAAV;;AASA,UAAIoB,OAAO,CAACxX,MAAR,KAAmB,CAAvB,EAA0B;AACxBwX,QAAAA,OAAO,GAAG,IAAV;AACD;AACF;;AAED,UAAME,SAAS,GAAG9b,cAAc,CAACW,OAAf,CAAuB,KAAK2a,SAA5B,CAAlB;;AACA,QAAIM,OAAJ,EAAa;AACX,YAAMG,cAAc,GAAGH,OAAO,CAAC3b,IAAR,CAAakb,IAAI,IAAIW,SAAS,KAAKX,IAAnC,CAAvB;AACAU,MAAAA,WAAW,GAAGE,cAAc,GAAG9N,IAAI,CAACvF,GAAL,CAASqT,cAAT,EAAyB7N,UAAzB,CAAH,GAAwC,IAApE;;AAEA,UAAI2N,WAAW,IAAIA,WAAW,CAACd,gBAA/B,EAAiD;AAC/C;AACD;AACF;;AAED,UAAMiB,UAAU,GAAG3R,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoM,YAApC,CAAnB;;AACA,QAAI4B,UAAU,CAAC9O,gBAAf,EAAiC;AAC/B;AACD;;AAED,QAAI0O,OAAJ,EAAa;AACXA,MAAAA,OAAO,CAACxW,OAAR,CAAgB6W,UAAU,IAAI;AAC5B,YAAIH,SAAS,KAAKG,UAAlB,EAA8B;AAC5BnB,UAAAA,QAAQ,CAACoB,iBAAT,CAA2BD,UAA3B,EAAuC,MAAvC;AACD;;AAED,YAAI,CAACJ,WAAL,EAAkB;AAChB5N,UAAAA,IAAI,CAAC5F,GAAL,CAAS4T,UAAT,EAAqB/N,UAArB,EAA+B,IAA/B;AACD;AACF,OARD;AASD;;AAED,UAAMiO,SAAS,GAAG,KAAKC,aAAL,EAAlB;;AAEA,SAAKpO,QAAL,CAAc5H,SAAd,CAAwB4C,MAAxB,CAA+BwR,mBAA/B;;AACA,SAAKxM,QAAL,CAAc5H,SAAd,CAAwBwR,GAAxB,CAA4B6C,qBAA5B;;AAEA,SAAKzM,QAAL,CAAclI,KAAd,CAAoBqW,SAApB,IAAiC,CAAjC;;AAEA,QAAI,KAAKnB,aAAL,CAAmB5W,MAAvB,EAA+B;AAC7B,WAAK4W,aAAL,CAAmB5V,OAAnB,CAA2BjF,OAAO,IAAI;AACpCA,QAAAA,OAAO,CAACiG,SAAR,CAAkB4C,MAAlB,CAAyB0R,oBAAzB;AACAva,QAAAA,OAAO,CAACkQ,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD,OAHD;AAID;;AAED,SAAKgM,gBAAL,CAAsB,IAAtB;;AAEA,UAAMC,QAAQ,GAAG,MAAM;AACrB,WAAKtO,QAAL,CAAc5H,SAAd,CAAwB4C,MAAxB,CAA+ByR,qBAA/B;;AACA,WAAKzM,QAAL,CAAc5H,SAAd,CAAwBwR,GAAxB,CAA4B4C,mBAA5B,EAAiDtL,iBAAjD;;AAEA,WAAKlB,QAAL,CAAclI,KAAd,CAAoBqW,SAApB,IAAiC,EAAjC;AAEA,WAAKE,gBAAL,CAAsB,KAAtB;AAEAhS,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqM,aAApC;AACD,KATD;;AAWA,UAAMkC,oBAAoB,GAAGJ,SAAS,CAAC,CAAD,CAAT,CAAavW,WAAb,KAA6BuW,SAAS,CAACxP,KAAV,CAAgB,CAAhB,CAA1D;AACA,UAAM6P,UAAU,GAAI,SAAQD,oBAAqB,EAAjD;;AAEA,SAAKhO,cAAL,CAAoB+N,QAApB,EAA8B,KAAKtO,QAAnC,EAA6C,IAA7C;;AACA,SAAKA,QAAL,CAAclI,KAAd,CAAoBqW,SAApB,IAAkC,GAAE,KAAKnO,QAAL,CAAcwO,UAAd,CAA0B,IAA9D;AACD;;AAEDd,EAAAA,IAAI,GAAG;AACL,QAAI,KAAKX,gBAAL,IAAyB,CAAC,KAAK/M,QAAL,CAAc5H,SAAd,CAAwBC,QAAxB,CAAiC6I,iBAAjC,CAA9B,EAAiF;AAC/E;AACD;;AAED,UAAM8M,UAAU,GAAG3R,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCsM,YAApC,CAAnB;;AACA,QAAI0B,UAAU,CAAC9O,gBAAf,EAAiC;AAC/B;AACD;;AAED,UAAMiP,SAAS,GAAG,KAAKC,aAAL,EAAlB;;AAEA,SAAKpO,QAAL,CAAclI,KAAd,CAAoBqW,SAApB,IAAkC,GAAE,KAAKnO,QAAL,CAAcuD,qBAAd,GAAsC4K,SAAtC,CAAiD,IAArF;AAEArV,IAAAA,MAAM,CAAC,KAAKkH,QAAN,CAAN;;AAEA,SAAKA,QAAL,CAAc5H,SAAd,CAAwBwR,GAAxB,CAA4B6C,qBAA5B;;AACA,SAAKzM,QAAL,CAAc5H,SAAd,CAAwB4C,MAAxB,CAA+BwR,mBAA/B,EAAoDtL,iBAApD;;AAEA,UAAMuN,kBAAkB,GAAG,KAAKzB,aAAL,CAAmB5W,MAA9C;;AACA,QAAIqY,kBAAkB,GAAG,CAAzB,EAA4B;AAC1B,WAAK,IAAI7R,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6R,kBAApB,EAAwC7R,CAAC,EAAzC,EAA6C;AAC3C,cAAMiC,OAAO,GAAG,KAAKmO,aAAL,CAAmBpQ,CAAnB,CAAhB;AACA,cAAMuQ,IAAI,GAAG/X,sBAAsB,CAACyJ,OAAD,CAAnC;;AAEA,YAAIsO,IAAI,IAAI,CAACA,IAAI,CAAC/U,SAAL,CAAeC,QAAf,CAAwB6I,iBAAxB,CAAb,EAAuD;AACrDrC,UAAAA,OAAO,CAACzG,SAAR,CAAkBwR,GAAlB,CAAsB8C,oBAAtB;AACA7N,UAAAA,OAAO,CAACwD,YAAR,CAAqB,eAArB,EAAsC,KAAtC;AACD;AACF;AACF;;AAED,SAAKgM,gBAAL,CAAsB,IAAtB;;AAEA,UAAMC,QAAQ,GAAG,MAAM;AACrB,WAAKD,gBAAL,CAAsB,KAAtB;;AACA,WAAKrO,QAAL,CAAc5H,SAAd,CAAwB4C,MAAxB,CAA+ByR,qBAA/B;;AACA,WAAKzM,QAAL,CAAc5H,SAAd,CAAwBwR,GAAxB,CAA4B4C,mBAA5B;;AACAnQ,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCuM,cAApC;AACD,KALD;;AAOA,SAAKvM,QAAL,CAAclI,KAAd,CAAoBqW,SAApB,IAAiC,EAAjC;;AAEA,SAAK5N,cAAL,CAAoB+N,QAApB,EAA8B,KAAKtO,QAAnC,EAA6C,IAA7C;AACD;;AAEDqO,EAAAA,gBAAgB,CAACK,eAAD,EAAkB;AAChC,SAAK3B,gBAAL,GAAwB2B,eAAxB;AACD,GA5LkC;;;AAgMnCnH,EAAAA,UAAU,CAACvQ,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmN,SADI;AAEP,SAAGnN;AAFI,KAAT;AAIAA,IAAAA,MAAM,CAACoL,MAAP,GAAgBnE,OAAO,CAACjH,MAAM,CAACoL,MAAR,CAAvB,CALiB;;AAMjBtL,IAAAA,eAAe,CAAC8C,MAAD,EAAO5C,MAAP,EAAe0N,aAAf,CAAf;AACA,WAAO1N,MAAP;AACD;;AAEDoX,EAAAA,aAAa,GAAG;AACd,WAAO,KAAKpO,QAAL,CAAc5H,SAAd,CAAwBC,QAAxB,CAAiCsU,KAAjC,IAA0CA,KAA1C,GAAkDC,MAAzD;AACD;;AAEDY,EAAAA,UAAU,GAAG;AACX,QAAI;AAAErB,MAAAA;AAAF,QAAa,KAAK7E,OAAtB;AAEA6E,IAAAA,MAAM,GAAGhW,UAAU,CAACgW,MAAD,CAAnB;AAEA,UAAMja,QAAQ,GAAI,GAAEgQ,sBAAqB,oBAAmBiK,MAAO,IAAnE;AAEAna,IAAAA,cAAc,CAACC,IAAf,CAAoBC,QAApB,EAA8Bia,MAA9B,EACG/U,OADH,CACWjF,OAAO,IAAI;AAClB,YAAMwc,QAAQ,GAAGvZ,sBAAsB,CAACjD,OAAD,CAAvC;;AAEA,WAAKsb,yBAAL,CACEkB,QADF,EAEE,CAACxc,OAAD,CAFF;AAID,KARH;AAUA,WAAOga,MAAP;AACD;;AAEDsB,EAAAA,yBAAyB,CAACtb,OAAD,EAAUyc,YAAV,EAAwB;AAC/C,QAAI,CAACzc,OAAD,IAAY,CAACyc,YAAY,CAACxY,MAA9B,EAAsC;AACpC;AACD;;AAED,UAAMyY,MAAM,GAAG1c,OAAO,CAACiG,SAAR,CAAkBC,QAAlB,CAA2B6I,iBAA3B,CAAf;AAEA0N,IAAAA,YAAY,CAACxX,OAAb,CAAqB+V,IAAI,IAAI;AAC3B,UAAI0B,MAAJ,EAAY;AACV1B,QAAAA,IAAI,CAAC/U,SAAL,CAAe4C,MAAf,CAAsB0R,oBAAtB;AACD,OAFD,MAEO;AACLS,QAAAA,IAAI,CAAC/U,SAAL,CAAewR,GAAf,CAAmB8C,oBAAnB;AACD;;AAEDS,MAAAA,IAAI,CAAC9K,YAAL,CAAkB,eAAlB,EAAmCwM,MAAnC;AACD,KARD;AASD,GAlPkC;;;AAsPX,SAAjBX,iBAAiB,CAAC/b,OAAD,EAAU6E,MAAV,EAAkB;AACxC,QAAI8K,IAAI,GAAG7B,IAAI,CAACvF,GAAL,CAASvI,OAAT,EAAkB+N,UAAlB,CAAX;AACA,UAAMoH,OAAO,GAAG,EACd,GAAGnD,SADW;AAEd,SAAGxB,WAAW,CAACI,iBAAZ,CAA8B5Q,OAA9B,CAFW;AAGd,UAAI,OAAO6E,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;AAHc,KAAhB;;AAMA,QAAI,CAAC8K,IAAD,IAASwF,OAAO,CAAClF,MAAjB,IAA2B,OAAOpL,MAAP,KAAkB,QAA7C,IAAyD,YAAYU,IAAZ,CAAiBV,MAAjB,CAA7D,EAAuF;AACrFsQ,MAAAA,OAAO,CAAClF,MAAR,GAAiB,KAAjB;AACD;;AAED,QAAI,CAACN,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAIgL,QAAJ,CAAa3a,OAAb,EAAsBmV,OAAtB,CAAP;AACD;;AAED,QAAI,OAAOtQ,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,UAAI,OAAO8K,IAAI,CAAC9K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED8K,MAAAA,IAAI,CAAC9K,MAAD,CAAJ;AACD;AACF;;AAEqB,SAAf+C,eAAe,CAAC/C,MAAD,EAAS;AAC7B,WAAO,KAAK6K,IAAL,CAAU,YAAY;AAC3BiL,MAAAA,QAAQ,CAACoB,iBAAT,CAA2B,IAA3B,EAAiClX,MAAjC;AACD,KAFM,CAAP;AAGD;;AAnRkC;AAsRrC;AACA;AACA;AACA;AACA;;;AAEAqF,YAAY,CAACiC,EAAb,CAAgBlM,QAAhB,EAA0B2O,sBAA1B,EAAgDmB,sBAAhD,EAAsE,UAAUhG,KAAV,EAAiB;AACrF;AACA,MAAIA,KAAK,CAACS,MAAN,CAAakN,OAAb,KAAyB,GAAzB,IAAiC3N,KAAK,CAACC,cAAN,IAAwBD,KAAK,CAACC,cAAN,CAAqB0N,OAArB,KAAiC,GAA9F,EAAoG;AAClG3N,IAAAA,KAAK,CAAC0D,cAAN;AACD;;AAED,QAAMkP,WAAW,GAAGnM,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAApB;AACA,QAAM7Q,QAAQ,GAAGiD,sBAAsB,CAAC,IAAD,CAAvC;AACA,QAAM4Z,gBAAgB,GAAG/c,cAAc,CAACC,IAAf,CAAoBC,QAApB,CAAzB;AAEA6c,EAAAA,gBAAgB,CAAC3X,OAAjB,CAAyBjF,OAAO,IAAI;AAClC,UAAM2P,IAAI,GAAG7B,IAAI,CAACvF,GAAL,CAASvI,OAAT,EAAkB+N,UAAlB,CAAb;AACA,QAAIlJ,MAAJ;;AACA,QAAI8K,IAAJ,EAAU;AACR;AACA,UAAIA,IAAI,CAACyL,OAAL,KAAiB,IAAjB,IAAyB,OAAOuB,WAAW,CAAC3C,MAAnB,KAA8B,QAA3D,EAAqE;AACnErK,QAAAA,IAAI,CAACwF,OAAL,CAAa6E,MAAb,GAAsB2C,WAAW,CAAC3C,MAAlC;AACArK,QAAAA,IAAI,CAACyL,OAAL,GAAezL,IAAI,CAAC0L,UAAL,EAAf;AACD;;AAEDxW,MAAAA,MAAM,GAAG,QAAT;AACD,KARD,MAQO;AACLA,MAAAA,MAAM,GAAG8X,WAAT;AACD;;AAEDhC,IAAAA,QAAQ,CAACoB,iBAAT,CAA2B/b,OAA3B,EAAoC6E,MAApC;AACD,GAhBD;AAiBD,CA3BD;AA6BA;AACA;AACA;AACA;AACA;AACA;;AAEAwC,kBAAkB,CAACsT,QAAD,CAAlB;;ACjYA;AACA;AACA;AACA;AACA;AACA;AAqBA;AACA;AACA;AACA;AACA;;AAEA,MAAMlT,MAAI,GAAG,UAAb;AACA,MAAMsG,UAAQ,GAAG,aAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMS,cAAY,GAAG,WAArB;AAEA,MAAMqO,YAAU,GAAG,QAAnB;AACA,MAAMC,SAAS,GAAG,OAAlB;AACA,MAAMC,OAAO,GAAG,KAAhB;AACA,MAAMC,YAAY,GAAG,SAArB;AACA,MAAMC,cAAc,GAAG,WAAvB;AACA,MAAMC,kBAAkB,GAAG,CAA3B;;AAEA,MAAMC,cAAc,GAAG,IAAI7X,MAAJ,CAAY,GAAE0X,YAAa,IAAGC,cAAe,IAAGJ,YAAW,EAA3D,CAAvB;AAEA,MAAM1C,YAAU,GAAI,OAAMlM,WAAU,EAApC;AACA,MAAMmM,cAAY,GAAI,SAAQnM,WAAU,EAAxC;AACA,MAAMgM,YAAU,GAAI,OAAMhM,WAAU,EAApC;AACA,MAAMiM,aAAW,GAAI,QAAOjM,WAAU,EAAtC;AACA,MAAMmP,WAAW,GAAI,QAAOnP,WAAU,EAAtC;AACA,MAAMW,sBAAoB,GAAI,QAAOX,WAAU,GAAEO,cAAa,EAA9D;AACA,MAAM6O,sBAAsB,GAAI,UAASpP,WAAU,GAAEO,cAAa,EAAlE;AACA,MAAM8O,oBAAoB,GAAI,QAAOrP,WAAU,GAAEO,cAAa,EAA9D;AAEA,MAAMO,iBAAe,GAAG,MAAxB;AACA,MAAMwO,iBAAiB,GAAG,QAA1B;AACA,MAAMC,kBAAkB,GAAG,SAA3B;AACA,MAAMC,oBAAoB,GAAG,WAA7B;AACA,MAAMC,iBAAiB,GAAG,QAA1B;AAEA,MAAM3N,sBAAoB,GAAG,6BAA7B;AACA,MAAM4N,aAAa,GAAG,gBAAtB;AACA,MAAMC,mBAAmB,GAAG,aAA5B;AACA,MAAMC,sBAAsB,GAAG,6DAA/B;AAEA,MAAMC,aAAa,GAAG3W,KAAK,KAAK,SAAL,GAAiB,WAA5C;AACA,MAAM4W,gBAAgB,GAAG5W,KAAK,KAAK,WAAL,GAAmB,SAAjD;AACA,MAAM6W,gBAAgB,GAAG7W,KAAK,KAAK,YAAL,GAAoB,cAAlD;AACA,MAAM8W,mBAAmB,GAAG9W,KAAK,KAAK,cAAL,GAAsB,YAAvD;AACA,MAAM+W,eAAe,GAAG/W,KAAK,KAAK,YAAL,GAAoB,aAAjD;AACA,MAAMgX,cAAc,GAAGhX,KAAK,KAAK,aAAL,GAAqB,YAAjD;AAEA,MAAM6K,SAAO,GAAG;AACdd,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CADM;AAEdkN,EAAAA,QAAQ,EAAE,iBAFI;AAGdC,EAAAA,SAAS,EAAE,QAHG;AAIdvY,EAAAA,OAAO,EAAE,SAJK;AAKdwY,EAAAA,YAAY,EAAE,IALA;AAMdC,EAAAA,SAAS,EAAE;AANG,CAAhB;AASA,MAAMhM,aAAW,GAAG;AAClBrB,EAAAA,MAAM,EAAE,yBADU;AAElBkN,EAAAA,QAAQ,EAAE,kBAFQ;AAGlBC,EAAAA,SAAS,EAAE,yBAHO;AAIlBvY,EAAAA,OAAO,EAAE,QAJS;AAKlBwY,EAAAA,YAAY,EAAE,wBALI;AAMlBC,EAAAA,SAAS,EAAE;AANO,CAApB;AASA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,QAAN,SAAuB7Q,aAAvB,CAAqC;AACnCC,EAAAA,WAAW,CAAC5N,OAAD,EAAU6E,MAAV,EAAkB;AAC3B,UAAM7E,OAAN;AAEA,SAAKye,OAAL,GAAe,IAAf;AACA,SAAKtJ,OAAL,GAAe,KAAKC,UAAL,CAAgBvQ,MAAhB,CAAf;AACA,SAAK6Z,KAAL,GAAa,KAAKC,eAAL,EAAb;AACA,SAAKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;AAEA,SAAKlJ,kBAAL;AACD,GAVkC;;;AAcjB,aAAP3D,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD;;AAEqB,aAAXO,WAAW,GAAG;AACvB,WAAOA,aAAP;AACD;;AAEc,aAAJ9K,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GAxBkC;;;AA4BnCwI,EAAAA,MAAM,GAAG;AACP,QAAIjK,UAAU,CAAC,KAAK6H,QAAN,CAAd,EAA+B;AAC7B;AACD;;AAED,UAAMiR,QAAQ,GAAG,KAAKjR,QAAL,CAAc5H,SAAd,CAAwBC,QAAxB,CAAiC6I,iBAAjC,CAAjB;;AAEA,QAAI+P,QAAJ,EAAc;AACZ,WAAKvD,IAAL;AACA;AACD;;AAED,SAAKC,IAAL;AACD;;AAEDA,EAAAA,IAAI,GAAG;AACL,QAAIxV,UAAU,CAAC,KAAK6H,QAAN,CAAV,IAA6B,KAAK6Q,KAAL,CAAWzY,SAAX,CAAqBC,QAArB,CAA8B6I,iBAA9B,CAAjC,EAAiF;AAC/E;AACD;;AAED,UAAMiL,MAAM,GAAGwE,QAAQ,CAACO,oBAAT,CAA8B,KAAKlR,QAAnC,CAAf;AACA,UAAMpC,aAAa,GAAG;AACpBA,MAAAA,aAAa,EAAE,KAAKoC;AADA,KAAtB;AAIA,UAAMmR,SAAS,GAAG9U,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoM,YAApC,EAAgDxO,aAAhD,CAAlB;;AAEA,QAAIuT,SAAS,CAACjS,gBAAd,EAAgC;AAC9B;AACD,KAdI;;;AAiBL,QAAI,KAAK6R,SAAT,EAAoB;AAClBpO,MAAAA,WAAW,CAACC,gBAAZ,CAA6B,KAAKiO,KAAlC,EAAyC,QAAzC,EAAmD,MAAnD;AACD,KAFD,MAEO;AACL,UAAI,OAAOO,MAAP,KAAkB,WAAtB,EAAmC;AACjC,cAAM,IAAIzZ,SAAJ,CAAc,+DAAd,CAAN;AACD;;AAED,UAAI0Z,gBAAgB,GAAG,KAAKrR,QAA5B;;AAEA,UAAI,KAAKsH,OAAL,CAAakJ,SAAb,KAA2B,QAA/B,EAAyC;AACvCa,QAAAA,gBAAgB,GAAGlF,MAAnB;AACD,OAFD,MAEO,IAAIlW,SAAS,CAAC,KAAKqR,OAAL,CAAakJ,SAAd,CAAb,EAAuC;AAC5Ca,QAAAA,gBAAgB,GAAGlb,UAAU,CAAC,KAAKmR,OAAL,CAAakJ,SAAd,CAA7B;AACD,OAFM,MAEA,IAAI,OAAO,KAAKlJ,OAAL,CAAakJ,SAApB,KAAkC,QAAtC,EAAgD;AACrDa,QAAAA,gBAAgB,GAAG,KAAK/J,OAAL,CAAakJ,SAAhC;AACD;;AAED,YAAMC,YAAY,GAAG,KAAKa,gBAAL,EAArB;;AACA,YAAMC,eAAe,GAAGd,YAAY,CAACe,SAAb,CAAuBvf,IAAvB,CAA4Bwf,QAAQ,IAAIA,QAAQ,CAAC9X,IAAT,KAAkB,aAAlB,IAAmC8X,QAAQ,CAACC,OAAT,KAAqB,KAAhG,CAAxB;AAEA,WAAKd,OAAL,GAAeQ,MAAM,CAACO,YAAP,CAAoBN,gBAApB,EAAsC,KAAKR,KAA3C,EAAkDJ,YAAlD,CAAf;;AAEA,UAAIc,eAAJ,EAAqB;AACnB5O,QAAAA,WAAW,CAACC,gBAAZ,CAA6B,KAAKiO,KAAlC,EAAyC,QAAzC,EAAmD,QAAnD;AACD;AACF,KA1CI;AA6CL;AACA;AACA;;;AACA,QAAI,kBAAkBze,QAAQ,CAACC,eAA3B,IACF,CAAC8Z,MAAM,CAACzK,OAAP,CAAeqO,mBAAf,CADH,EACwC;AACtC,SAAGzd,MAAH,CAAU,GAAGF,QAAQ,CAAC8G,IAAT,CAAcrG,QAA3B,EACGuE,OADH,CACW+V,IAAI,IAAI9Q,YAAY,CAACiC,EAAb,CAAgB6O,IAAhB,EAAsB,WAAtB,EAAmCtU,IAAnC,CADnB;AAED;;AAED,SAAKmH,QAAL,CAAc4R,KAAd;;AACA,SAAK5R,QAAL,CAAcqC,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;AAEA,SAAKwO,KAAL,CAAWzY,SAAX,CAAqBgK,MAArB,CAA4BlB,iBAA5B;;AACA,SAAKlB,QAAL,CAAc5H,SAAd,CAAwBgK,MAAxB,CAA+BlB,iBAA/B;;AACA7E,IAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqM,aAApC,EAAiDzO,aAAjD;AACD;;AAED8P,EAAAA,IAAI,GAAG;AACL,QAAIvV,UAAU,CAAC,KAAK6H,QAAN,CAAV,IAA6B,CAAC,KAAK6Q,KAAL,CAAWzY,SAAX,CAAqBC,QAArB,CAA8B6I,iBAA9B,CAAlC,EAAkF;AAChF;AACD;;AAED,UAAMtD,aAAa,GAAG;AACpBA,MAAAA,aAAa,EAAE,KAAKoC;AADA,KAAtB;;AAIA,SAAK6R,aAAL,CAAmBjU,aAAnB;AACD;;AAEDuC,EAAAA,OAAO,GAAG;AACR,QAAI,KAAKyQ,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAakB,OAAb;AACD;;AAED,UAAM3R,OAAN;AACD;;AAED4R,EAAAA,MAAM,GAAG;AACP,SAAKhB,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;AACA,QAAI,KAAKJ,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAamB,MAAb;AACD;AACF,GAlIkC;;;AAsInCjK,EAAAA,kBAAkB,GAAG;AACnBzL,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BuP,WAA/B,EAA4CrT,KAAK,IAAI;AACnDA,MAAAA,KAAK,CAAC0D,cAAN;AACA,WAAKwC,MAAL;AACD,KAHD;AAID;;AAEDyP,EAAAA,aAAa,CAACjU,aAAD,EAAgB;AAC3B,UAAMoU,SAAS,GAAG3V,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCsM,YAApC,EAAgD1O,aAAhD,CAAlB;;AACA,QAAIoU,SAAS,CAAC9S,gBAAd,EAAgC;AAC9B;AACD,KAJ0B;AAO3B;;;AACA,QAAI,kBAAkB9M,QAAQ,CAACC,eAA/B,EAAgD;AAC9C,SAAGC,MAAH,CAAU,GAAGF,QAAQ,CAAC8G,IAAT,CAAcrG,QAA3B,EACGuE,OADH,CACW+V,IAAI,IAAI9Q,YAAY,CAACC,GAAb,CAAiB6Q,IAAjB,EAAuB,WAAvB,EAAoCtU,IAApC,CADnB;AAED;;AAED,QAAI,KAAK+X,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAakB,OAAb;AACD;;AAED,SAAKjB,KAAL,CAAWzY,SAAX,CAAqB4C,MAArB,CAA4BkG,iBAA5B;;AACA,SAAKlB,QAAL,CAAc5H,SAAd,CAAwB4C,MAAxB,CAA+BkG,iBAA/B;;AACA,SAAKlB,QAAL,CAAcqC,YAAd,CAA2B,eAA3B,EAA4C,OAA5C;;AACAM,IAAAA,WAAW,CAACE,mBAAZ,CAAgC,KAAKgO,KAArC,EAA4C,QAA5C;AACAxU,IAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCuM,cAApC,EAAkD3O,aAAlD;AACD;;AAED2J,EAAAA,UAAU,CAACvQ,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAG,KAAK+I,WAAL,CAAiBoE,OADb;AAEP,SAAGxB,WAAW,CAACI,iBAAZ,CAA8B,KAAK/C,QAAnC,CAFI;AAGP,SAAGhJ;AAHI,KAAT;AAMAF,IAAAA,eAAe,CAAC8C,MAAD,EAAO5C,MAAP,EAAe,KAAK+I,WAAL,CAAiB2E,WAAhC,CAAf;;AAEA,QAAI,OAAO1N,MAAM,CAACwZ,SAAd,KAA4B,QAA5B,IAAwC,CAACva,SAAS,CAACe,MAAM,CAACwZ,SAAR,CAAlD,IACF,OAAOxZ,MAAM,CAACwZ,SAAP,CAAiBjN,qBAAxB,KAAkD,UADpD,EAEE;AACA;AACA,YAAM,IAAI5L,SAAJ,CAAe,GAAEiC,MAAI,CAAChC,WAAL,EAAmB,gGAApC,CAAN;AACD;;AAED,WAAOZ,MAAP;AACD;;AAED8Z,EAAAA,eAAe,GAAG;AAChB,WAAO9e,cAAc,CAAC2B,IAAf,CAAoB,KAAKqM,QAAzB,EAAmC8P,aAAnC,EAAkD,CAAlD,CAAP;AACD;;AAEDmC,EAAAA,aAAa,GAAG;AACd,UAAMC,cAAc,GAAG,KAAKlS,QAAL,CAAc7M,UAArC;;AAEA,QAAI+e,cAAc,CAAC9Z,SAAf,CAAyBC,QAAzB,CAAkCsX,kBAAlC,CAAJ,EAA2D;AACzD,aAAOU,eAAP;AACD;;AAED,QAAI6B,cAAc,CAAC9Z,SAAf,CAAyBC,QAAzB,CAAkCuX,oBAAlC,CAAJ,EAA6D;AAC3D,aAAOU,cAAP;AACD,KATa;;;AAYd,UAAM6B,KAAK,GAAG1c,gBAAgB,CAAC,KAAKob,KAAN,CAAhB,CAA6BuB,gBAA7B,CAA8C,eAA9C,EAA+Dld,IAA/D,OAA0E,KAAxF;;AAEA,QAAIgd,cAAc,CAAC9Z,SAAf,CAAyBC,QAAzB,CAAkCqX,iBAAlC,CAAJ,EAA0D;AACxD,aAAOyC,KAAK,GAAGjC,gBAAH,GAAsBD,aAAlC;AACD;;AAED,WAAOkC,KAAK,GAAG/B,mBAAH,GAAyBD,gBAArC;AACD;;AAEDa,EAAAA,aAAa,GAAG;AACd,WAAO,KAAKhR,QAAL,CAAc0B,OAAd,CAAuB,IAAGmO,iBAAkB,EAA5C,MAAmD,IAA1D;AACD;;AAEDwC,EAAAA,UAAU,GAAG;AACX,UAAM;AAAEhP,MAAAA;AAAF,QAAa,KAAKiE,OAAxB;;AAEA,QAAI,OAAOjE,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,aAAOA,MAAM,CAACpO,KAAP,CAAa,GAAb,EAAkBqd,GAAlB,CAAsB9P,GAAG,IAAI7M,MAAM,CAACmV,QAAP,CAAgBtI,GAAhB,EAAqB,EAArB,CAA7B,CAAP;AACD;;AAED,QAAI,OAAOa,MAAP,KAAkB,UAAtB,EAAkC;AAChC,aAAOkP,UAAU,IAAIlP,MAAM,CAACkP,UAAD,EAAa,KAAKvS,QAAlB,CAA3B;AACD;;AAED,WAAOqD,MAAP;AACD;;AAEDiO,EAAAA,gBAAgB,GAAG;AACjB,UAAMkB,qBAAqB,GAAG;AAC5BC,MAAAA,SAAS,EAAE,KAAKR,aAAL,EADiB;AAE5BT,MAAAA,SAAS,EAAE,CAAC;AACV7X,QAAAA,IAAI,EAAE,iBADI;AAEV+Y,QAAAA,OAAO,EAAE;AACPnC,UAAAA,QAAQ,EAAE,KAAKjJ,OAAL,CAAaiJ;AADhB;AAFC,OAAD,EAMX;AACE5W,QAAAA,IAAI,EAAE,QADR;AAEE+Y,QAAAA,OAAO,EAAE;AACPrP,UAAAA,MAAM,EAAE,KAAKgP,UAAL;AADD;AAFX,OANW;AAFiB,KAA9B,CADiB;;AAkBjB,QAAI,KAAK/K,OAAL,CAAarP,OAAb,KAAyB,QAA7B,EAAuC;AACrCua,MAAAA,qBAAqB,CAAChB,SAAtB,GAAkC,CAAC;AACjC7X,QAAAA,IAAI,EAAE,aAD2B;AAEjC+X,QAAAA,OAAO,EAAE;AAFwB,OAAD,CAAlC;AAID;;AAED,WAAO,EACL,GAAGc,qBADE;AAEL,UAAI,OAAO,KAAKlL,OAAL,CAAamJ,YAApB,KAAqC,UAArC,GAAkD,KAAKnJ,OAAL,CAAamJ,YAAb,CAA0B+B,qBAA1B,CAAlD,GAAqG,KAAKlL,OAAL,CAAamJ,YAAtH;AAFK,KAAP;AAID;;AAEDkC,EAAAA,eAAe,CAACzW,KAAD,EAAQ;AACrB,UAAM0W,KAAK,GAAG5gB,cAAc,CAACC,IAAf,CAAoB+d,sBAApB,EAA4C,KAAKa,KAAjD,EAAwD/d,MAAxD,CAA+D+E,SAA/D,CAAd;;AAEA,QAAI,CAAC+a,KAAK,CAACxc,MAAX,EAAmB;AACjB;AACD;;AAED,QAAIqS,KAAK,GAAGmK,KAAK,CAAC9I,OAAN,CAAc5N,KAAK,CAACS,MAApB,CAAZ,CAPqB;;AAUrB,QAAIT,KAAK,CAAC5B,GAAN,KAAc6U,YAAd,IAA8B1G,KAAK,GAAG,CAA1C,EAA6C;AAC3CA,MAAAA,KAAK;AACN,KAZoB;;;AAerB,QAAIvM,KAAK,CAAC5B,GAAN,KAAc8U,cAAd,IAAgC3G,KAAK,GAAGmK,KAAK,CAACxc,MAAN,GAAe,CAA3D,EAA8D;AAC5DqS,MAAAA,KAAK;AACN,KAjBoB;;;AAoBrBA,IAAAA,KAAK,GAAGA,KAAK,KAAK,CAAC,CAAX,GAAe,CAAf,GAAmBA,KAA3B;AAEAmK,IAAAA,KAAK,CAACnK,KAAD,CAAL,CAAamJ,KAAb;AACD,GAzRkC;;;AA6RX,SAAjBiB,iBAAiB,CAAC1gB,OAAD,EAAU6E,MAAV,EAAkB;AACxC,QAAI8K,IAAI,GAAG7B,IAAI,CAACvF,GAAL,CAASvI,OAAT,EAAkB+N,UAAlB,CAAX;;AACA,UAAMoH,OAAO,GAAG,OAAOtQ,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;AAEA,QAAI,CAAC8K,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAI6O,QAAJ,CAAaxe,OAAb,EAAsBmV,OAAtB,CAAP;AACD;;AAED,QAAI,OAAOtQ,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,UAAI,OAAO8K,IAAI,CAAC9K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED8K,MAAAA,IAAI,CAAC9K,MAAD,CAAJ;AACD;AACF;;AAEqB,SAAf+C,eAAe,CAAC/C,MAAD,EAAS;AAC7B,WAAO,KAAK6K,IAAL,CAAU,YAAY;AAC3B8O,MAAAA,QAAQ,CAACkC,iBAAT,CAA2B,IAA3B,EAAiC7b,MAAjC;AACD,KAFM,CAAP;AAGD;;AAEgB,SAAV8b,UAAU,CAAC5W,KAAD,EAAQ;AACvB,QAAIA,KAAK,KAAKA,KAAK,CAACoG,MAAN,KAAiB+M,kBAAjB,IAAwCnT,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAAC5B,GAAN,KAAc4U,OAArF,CAAT,EAAyG;AACvG;AACD;;AAED,UAAM6D,OAAO,GAAG/gB,cAAc,CAACC,IAAf,CAAoBiQ,sBAApB,CAAhB;;AAEA,SAAK,IAAItF,CAAC,GAAG,CAAR,EAAWK,GAAG,GAAG8V,OAAO,CAAC3c,MAA9B,EAAsCwG,CAAC,GAAGK,GAA1C,EAA+CL,CAAC,EAAhD,EAAoD;AAClD,YAAMoW,OAAO,GAAG/S,IAAI,CAACvF,GAAL,CAASqY,OAAO,CAACnW,CAAD,CAAhB,EAAqBsD,UAArB,CAAhB;;AACA,UAAI,CAAC8S,OAAD,IAAYA,OAAO,CAAC1L,OAAR,CAAgBoJ,SAAhB,KAA8B,KAA9C,EAAqD;AACnD;AACD;;AAED,UAAI,CAACsC,OAAO,CAAChT,QAAR,CAAiB5H,SAAjB,CAA2BC,QAA3B,CAAoC6I,iBAApC,CAAL,EAA2D;AACzD;AACD;;AAED,YAAMtD,aAAa,GAAG;AACpBA,QAAAA,aAAa,EAAEoV,OAAO,CAAChT;AADH,OAAtB;;AAIA,UAAI9D,KAAJ,EAAW;AACT,cAAM+W,YAAY,GAAG/W,KAAK,CAAC+W,YAAN,EAArB;AACA,cAAMC,YAAY,GAAGD,YAAY,CAACle,QAAb,CAAsBie,OAAO,CAACnC,KAA9B,CAArB;;AACA,YACEoC,YAAY,CAACle,QAAb,CAAsBie,OAAO,CAAChT,QAA9B,KACCgT,OAAO,CAAC1L,OAAR,CAAgBoJ,SAAhB,KAA8B,QAA9B,IAA0C,CAACwC,YAD5C,IAECF,OAAO,CAAC1L,OAAR,CAAgBoJ,SAAhB,KAA8B,SAA9B,IAA2CwC,YAH9C,EAIE;AACA;AACD,SATQ;;;AAYT,YAAIF,OAAO,CAACnC,KAAR,CAAcxY,QAAd,CAAuB6D,KAAK,CAACS,MAA7B,MAA0CT,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAAC5B,GAAN,KAAc4U,OAAzC,IAAqD,qCAAqCxX,IAArC,CAA0CwE,KAAK,CAACS,MAAN,CAAakN,OAAvD,CAA9F,CAAJ,EAAoK;AAClK;AACD;;AAED,YAAI3N,KAAK,CAACK,IAAN,KAAe,OAAnB,EAA4B;AAC1BqB,UAAAA,aAAa,CAACuV,UAAd,GAA2BjX,KAA3B;AACD;AACF;;AAED8W,MAAAA,OAAO,CAACnB,aAAR,CAAsBjU,aAAtB;AACD;AACF;;AAE0B,SAApBsT,oBAAoB,CAAC/e,OAAD,EAAU;AACnC,WAAOiD,sBAAsB,CAACjD,OAAD,CAAtB,IAAmCA,OAAO,CAACgB,UAAlD;AACD;;AAE2B,SAArBigB,qBAAqB,CAAClX,KAAD,EAAQ;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAI,kBAAkBxE,IAAlB,CAAuBwE,KAAK,CAACS,MAAN,CAAakN,OAApC,IACF3N,KAAK,CAAC5B,GAAN,KAAc2U,SAAd,IAA4B/S,KAAK,CAAC5B,GAAN,KAAc0U,YAAd,KAC1B9S,KAAK,CAAC5B,GAAN,KAAc8U,cAAd,IAAgClT,KAAK,CAAC5B,GAAN,KAAc6U,YAA/C,IACCjT,KAAK,CAACS,MAAN,CAAa+E,OAAb,CAAqBoO,aAArB,CAF0B,CAD1B,GAIF,CAACR,cAAc,CAAC5X,IAAf,CAAoBwE,KAAK,CAAC5B,GAA1B,CAJH,EAImC;AACjC;AACD;;AAED,UAAM2W,QAAQ,GAAG,KAAK7Y,SAAL,CAAeC,QAAf,CAAwB6I,iBAAxB,CAAjB;;AAEA,QAAI,CAAC+P,QAAD,IAAa/U,KAAK,CAAC5B,GAAN,KAAc0U,YAA/B,EAA2C;AACzC;AACD;;AAED9S,IAAAA,KAAK,CAAC0D,cAAN;AACA1D,IAAAA,KAAK,CAACmX,eAAN;;AAEA,QAAIlb,UAAU,CAAC,IAAD,CAAd,EAAsB;AACpB;AACD;;AAED,UAAMmb,eAAe,GAAG,MAAM,KAAKtgB,OAAL,CAAakP,sBAAb,IAAqC,IAArC,GAA4ClQ,cAAc,CAACwB,IAAf,CAAoB,IAApB,EAA0B0O,sBAA1B,EAAgD,CAAhD,CAA1E;;AAEA,QAAIhG,KAAK,CAAC5B,GAAN,KAAc0U,YAAlB,EAA8B;AAC5BsE,MAAAA,eAAe,GAAG1B,KAAlB;AACAjB,MAAAA,QAAQ,CAACmC,UAAT;AACA;AACD;;AAED,QAAI,CAAC7B,QAAD,KAAc/U,KAAK,CAAC5B,GAAN,KAAc6U,YAAd,IAA8BjT,KAAK,CAAC5B,GAAN,KAAc8U,cAA1D,CAAJ,EAA+E;AAC7EkE,MAAAA,eAAe,GAAGC,KAAlB;AACA;AACD;;AAED,QAAI,CAACtC,QAAD,IAAa/U,KAAK,CAAC5B,GAAN,KAAc2U,SAA/B,EAA0C;AACxC0B,MAAAA,QAAQ,CAACmC,UAAT;AACA;AACD;;AAEDnC,IAAAA,QAAQ,CAAClQ,WAAT,CAAqB6S,eAAe,EAApC,EAAwCX,eAAxC,CAAwDzW,KAAxD;AACD;;AAtZkC;AAyZrC;AACA;AACA;AACA;AACA;;;AAEAG,YAAY,CAACiC,EAAb,CAAgBlM,QAAhB,EAA0Bod,sBAA1B,EAAkDtN,sBAAlD,EAAwEyO,QAAQ,CAACyC,qBAAjF;AACA/W,YAAY,CAACiC,EAAb,CAAgBlM,QAAhB,EAA0Bod,sBAA1B,EAAkDM,aAAlD,EAAiEa,QAAQ,CAACyC,qBAA1E;AACA/W,YAAY,CAACiC,EAAb,CAAgBlM,QAAhB,EAA0B2O,sBAA1B,EAAgD4P,QAAQ,CAACmC,UAAzD;AACAzW,YAAY,CAACiC,EAAb,CAAgBlM,QAAhB,EAA0Bqd,oBAA1B,EAAgDkB,QAAQ,CAACmC,UAAzD;AACAzW,YAAY,CAACiC,EAAb,CAAgBlM,QAAhB,EAA0B2O,sBAA1B,EAAgDmB,sBAAhD,EAAsE,UAAUhG,KAAV,EAAiB;AACrFA,EAAAA,KAAK,CAAC0D,cAAN;AACA+Q,EAAAA,QAAQ,CAACkC,iBAAT,CAA2B,IAA3B;AACD,CAHD;AAKA;AACA;AACA;AACA;AACA;AACA;;AAEArZ,kBAAkB,CAACmX,QAAD,CAAlB;;AChhBA;AACA;AACA;AACA;AACA;AACA;AAKA,MAAM6C,sBAAsB,GAAG,mDAA/B;AACA,MAAMC,uBAAuB,GAAG,aAAhC;;AAEA,MAAMC,QAAQ,GAAG,MAAM;AACrB;AACA,QAAMC,aAAa,GAAGvhB,QAAQ,CAACC,eAAT,CAAyBuhB,WAA/C;AACA,SAAOpf,IAAI,CAACuU,GAAL,CAASvT,MAAM,CAACqe,UAAP,GAAoBF,aAA7B,CAAP;AACD,CAJD;;AAMA,MAAMjG,IAAI,GAAG,CAACoG,KAAK,GAAGJ,QAAQ,EAAjB,KAAwB;AACnCK,EAAAA,gBAAgB,GADmB;;;AAGnCC,EAAAA,qBAAqB,CAAC,MAAD,EAAS,cAAT,EAAyBC,eAAe,IAAIA,eAAe,GAAGH,KAA9D,CAArB,CAHmC;;;AAKnCE,EAAAA,qBAAqB,CAACR,sBAAD,EAAyB,cAAzB,EAAyCS,eAAe,IAAIA,eAAe,GAAGH,KAA9E,CAArB;;AACAE,EAAAA,qBAAqB,CAACP,uBAAD,EAA0B,aAA1B,EAAyCQ,eAAe,IAAIA,eAAe,GAAGH,KAA9E,CAArB;AACD,CAPD;;AASA,MAAMC,gBAAgB,GAAG,MAAM;AAC7B,QAAMG,WAAW,GAAG9hB,QAAQ,CAAC8G,IAAT,CAAcpB,KAAd,CAAoBqc,QAAxC;;AACA,MAAID,WAAJ,EAAiB;AACfvR,IAAAA,WAAW,CAACC,gBAAZ,CAA6BxQ,QAAQ,CAAC8G,IAAtC,EAA4C,UAA5C,EAAwDgb,WAAxD;AACD;;AAED9hB,EAAAA,QAAQ,CAAC8G,IAAT,CAAcpB,KAAd,CAAoBqc,QAApB,GAA+B,QAA/B;AACD,CAPD;;AASA,MAAMH,qBAAqB,GAAG,CAAC9hB,QAAD,EAAWkiB,SAAX,EAAsBhb,QAAtB,KAAmC;AAC/D,QAAMib,cAAc,GAAGX,QAAQ,EAA/B;AACA1hB,EAAAA,cAAc,CAACC,IAAf,CAAoBC,QAApB,EACGkF,OADH,CACWjF,OAAO,IAAI;AAClB,QAAIA,OAAO,KAAKC,QAAQ,CAAC8G,IAArB,IAA6B1D,MAAM,CAACqe,UAAP,GAAoB1hB,OAAO,CAACyhB,WAAR,GAAsBS,cAA3E,EAA2F;AACzF;AACD;;AAED,UAAMH,WAAW,GAAG/hB,OAAO,CAAC2F,KAAR,CAAcsc,SAAd,CAApB;AACA,UAAMH,eAAe,GAAGze,MAAM,CAACC,gBAAP,CAAwBtD,OAAxB,EAAiCiiB,SAAjC,CAAxB;AACAzR,IAAAA,WAAW,CAACC,gBAAZ,CAA6BzQ,OAA7B,EAAsCiiB,SAAtC,EAAiDF,WAAjD;AACA/hB,IAAAA,OAAO,CAAC2F,KAAR,CAAcsc,SAAd,IAA4B,GAAEhb,QAAQ,CAACzD,MAAM,CAACC,UAAP,CAAkBqe,eAAlB,CAAD,CAAqC,IAA3E;AACD,GAVH;AAWD,CAbD;;AAeA,MAAMK,KAAK,GAAG,MAAM;AAClBC,EAAAA,uBAAuB,CAAC,MAAD,EAAS,UAAT,CAAvB;;AACAA,EAAAA,uBAAuB,CAAC,MAAD,EAAS,cAAT,CAAvB;;AACAA,EAAAA,uBAAuB,CAACf,sBAAD,EAAyB,cAAzB,CAAvB;;AACAe,EAAAA,uBAAuB,CAACd,uBAAD,EAA0B,aAA1B,CAAvB;AACD,CALD;;AAOA,MAAMc,uBAAuB,GAAG,CAACriB,QAAD,EAAWkiB,SAAX,KAAyB;AACvDpiB,EAAAA,cAAc,CAACC,IAAf,CAAoBC,QAApB,EAA8BkF,OAA9B,CAAsCjF,OAAO,IAAI;AAC/C,UAAMoF,KAAK,GAAGoL,WAAW,CAACS,gBAAZ,CAA6BjR,OAA7B,EAAsCiiB,SAAtC,CAAd;;AACA,QAAI,OAAO7c,KAAP,KAAiB,WAArB,EAAkC;AAChCpF,MAAAA,OAAO,CAAC2F,KAAR,CAAc0c,cAAd,CAA6BJ,SAA7B;AACD,KAFD,MAEO;AACLzR,MAAAA,WAAW,CAACE,mBAAZ,CAAgC1Q,OAAhC,EAAyCiiB,SAAzC;AACAjiB,MAAAA,OAAO,CAAC2F,KAAR,CAAcsc,SAAd,IAA2B7c,KAA3B;AACD;AACF,GARD;AASD,CAVD;;AC3DA;AACA;AACA;AACA;AACA;AACA;AAKA,MAAM4M,SAAO,GAAG;AACdtM,EAAAA,SAAS,EAAE,IADG;AACG;AACjB2I,EAAAA,UAAU,EAAE,KAFE;AAGda,EAAAA,WAAW,EAAEjP,QAAQ,CAAC8G,IAHR;AAGc;AAC5Bub,EAAAA,aAAa,EAAE;AAJD,CAAhB;AAOA,MAAM/P,aAAW,GAAG;AAClB7M,EAAAA,SAAS,EAAE,SADO;AAElB2I,EAAAA,UAAU,EAAE,SAFM;AAGlBa,EAAAA,WAAW,EAAE,SAHK;AAIlBoT,EAAAA,aAAa,EAAE;AAJG,CAApB;AAMA,MAAM7a,MAAI,GAAG,UAAb;AACA,MAAM8a,mBAAmB,GAAG,gBAA5B;AACA,MAAMzT,iBAAe,GAAG,MAAxB;AACA,MAAMC,iBAAe,GAAG,MAAxB;AAEA,MAAMyT,eAAe,GAAI,gBAAe/a,MAAK,EAA7C;;AAEA,MAAMgb,QAAN,CAAe;AACb7U,EAAAA,WAAW,CAAC/I,MAAD,EAAS;AAClB,SAAKsQ,OAAL,GAAe,KAAKC,UAAL,CAAgBvQ,MAAhB,CAAf;AACA,SAAK6d,WAAL,GAAmB,KAAnB;AACA,SAAK7U,QAAL,GAAgB,IAAhB;AACD;;AAED2N,EAAAA,IAAI,CAACvU,QAAD,EAAW;AACb,QAAI,CAAC,KAAKkO,OAAL,CAAazP,SAAlB,EAA6B;AAC3BqC,MAAAA,OAAO,CAACd,QAAD,CAAP;AACA;AACD;;AAED,SAAK0b,OAAL;;AAEA,QAAI,KAAKxN,OAAL,CAAa9G,UAAjB,EAA6B;AAC3B1H,MAAAA,MAAM,CAAC,KAAKic,WAAL,EAAD,CAAN;AACD;;AAED,SAAKA,WAAL,GAAmB3c,SAAnB,CAA6BwR,GAA7B,CAAiC1I,iBAAjC;;AAEA,SAAK8T,iBAAL,CAAuB,MAAM;AAC3B9a,MAAAA,OAAO,CAACd,QAAD,CAAP;AACD,KAFD;AAGD;;AAEDsU,EAAAA,IAAI,CAACtU,QAAD,EAAW;AACb,QAAI,CAAC,KAAKkO,OAAL,CAAazP,SAAlB,EAA6B;AAC3BqC,MAAAA,OAAO,CAACd,QAAD,CAAP;AACA;AACD;;AAED,SAAK2b,WAAL,GAAmB3c,SAAnB,CAA6B4C,MAA7B,CAAoCkG,iBAApC;;AAEA,SAAK8T,iBAAL,CAAuB,MAAM;AAC3B,WAAK7U,OAAL;AACAjG,MAAAA,OAAO,CAACd,QAAD,CAAP;AACD,KAHD;AAID,GAtCY;;;AA0Cb2b,EAAAA,WAAW,GAAG;AACZ,QAAI,CAAC,KAAK/U,QAAV,EAAoB;AAClB,YAAMiV,QAAQ,GAAG7iB,QAAQ,CAAC8iB,aAAT,CAAuB,KAAvB,CAAjB;AACAD,MAAAA,QAAQ,CAACE,SAAT,GAAqBT,mBAArB;;AACA,UAAI,KAAKpN,OAAL,CAAa9G,UAAjB,EAA6B;AAC3ByU,QAAAA,QAAQ,CAAC7c,SAAT,CAAmBwR,GAAnB,CAAuB3I,iBAAvB;AACD;;AAED,WAAKjB,QAAL,GAAgBiV,QAAhB;AACD;;AAED,WAAO,KAAKjV,QAAZ;AACD;;AAEDuH,EAAAA,UAAU,CAACvQ,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmN,SADI;AAEP,UAAI,OAAOnN,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;AAFO,KAAT;AAKAA,IAAAA,MAAM,CAACqK,WAAP,GAAqBrK,MAAM,CAACqK,WAAP,IAAsBjP,QAAQ,CAAC8G,IAApD;AACApC,IAAAA,eAAe,CAAC8C,MAAD,EAAO5C,MAAP,EAAe0N,aAAf,CAAf;AACA,WAAO1N,MAAP;AACD;;AAED8d,EAAAA,OAAO,GAAG;AACR,QAAI,KAAKD,WAAT,EAAsB;AACpB;AACD;;AAED,SAAKvN,OAAL,CAAajG,WAAb,CAAyB+T,WAAzB,CAAqC,KAAKL,WAAL,EAArC;;AAEA1Y,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKyW,WAAL,EAAhB,EAAoCJ,eAApC,EAAqD,MAAM;AACzDza,MAAAA,OAAO,CAAC,KAAKoN,OAAL,CAAamN,aAAd,CAAP;AACD,KAFD;AAIA,SAAKI,WAAL,GAAmB,IAAnB;AACD;;AAED1U,EAAAA,OAAO,GAAG;AACR,QAAI,CAAC,KAAK0U,WAAV,EAAuB;AACrB;AACD;;AAEDxY,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgC2U,eAAhC;;AAEA,SAAKI,WAAL,GAAmB5hB,UAAnB,CAA8ByO,WAA9B,CAA0C,KAAK5B,QAA/C;;AACA,SAAK6U,WAAL,GAAmB,KAAnB;AACD;;AAEDG,EAAAA,iBAAiB,CAAC5b,QAAD,EAAW;AAC1B,QAAI,CAAC,KAAKkO,OAAL,CAAa9G,UAAlB,EAA8B;AAC5BtG,MAAAA,OAAO,CAACd,QAAD,CAAP;AACA;AACD;;AAED,UAAMic,0BAA0B,GAAGhgB,gCAAgC,CAAC,KAAK0f,WAAL,EAAD,CAAnE;AACA1Y,IAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKwW,WAAL,EAAjB,EAAqC,eAArC,EAAsD,MAAM7a,OAAO,CAACd,QAAD,CAAnE;AACA/C,IAAAA,oBAAoB,CAAC,KAAK0e,WAAL,EAAD,EAAqBM,0BAArB,CAApB;AACD;;AArGY;;AC9Bf;AACA;AACA;AACA;AACA;AACA;AAmBA;AACA;AACA;AACA;AACA;;AAEA,MAAMzb,MAAI,GAAG,OAAb;AACA,MAAMsG,UAAQ,GAAG,UAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMS,cAAY,GAAG,WAArB;AACA,MAAMqO,YAAU,GAAG,QAAnB;AAEA,MAAM7K,SAAO,GAAG;AACd8Q,EAAAA,QAAQ,EAAE,IADI;AAEd5Q,EAAAA,QAAQ,EAAE,IAFI;AAGduN,EAAAA,KAAK,EAAE;AAHO,CAAhB;AAMA,MAAMlN,aAAW,GAAG;AAClBuQ,EAAAA,QAAQ,EAAE,kBADQ;AAElB5Q,EAAAA,QAAQ,EAAE,SAFQ;AAGlBuN,EAAAA,KAAK,EAAE;AAHW,CAApB;AAMA,MAAMtF,YAAU,GAAI,OAAMlM,WAAU,EAApC;AACA,MAAMkV,oBAAoB,GAAI,gBAAelV,WAAU,EAAvD;AACA,MAAMmM,cAAY,GAAI,SAAQnM,WAAU,EAAxC;AACA,MAAMgM,YAAU,GAAI,OAAMhM,WAAU,EAApC;AACA,MAAMiM,aAAW,GAAI,QAAOjM,WAAU,EAAtC;AACA,MAAMmV,eAAa,GAAI,UAASnV,WAAU,EAA1C;AACA,MAAMoV,YAAY,GAAI,SAAQpV,WAAU,EAAxC;AACA,MAAMqV,qBAAmB,GAAI,gBAAerV,WAAU,EAAtD;AACA,MAAMsV,uBAAqB,GAAI,kBAAiBtV,WAAU,EAA1D;AACA,MAAMuV,qBAAqB,GAAI,kBAAiBvV,WAAU,EAA1D;AACA,MAAMwV,uBAAuB,GAAI,oBAAmBxV,WAAU,EAA9D;AACA,MAAMW,sBAAoB,GAAI,QAAOX,WAAU,GAAEO,cAAa,EAA9D;AAEA,MAAMkV,eAAe,GAAG,YAAxB;AACA,MAAM5U,iBAAe,GAAG,MAAxB;AACA,MAAMC,iBAAe,GAAG,MAAxB;AACA,MAAM4U,iBAAiB,GAAG,cAA1B;AAEA,MAAMC,eAAe,GAAG,eAAxB;AACA,MAAMC,mBAAmB,GAAG,aAA5B;AACA,MAAM9T,sBAAoB,GAAG,0BAA7B;AACA,MAAM+T,uBAAqB,GAAG,2BAA9B;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,KAAN,SAAoBpW,aAApB,CAAkC;AAChCC,EAAAA,WAAW,CAAC5N,OAAD,EAAU6E,MAAV,EAAkB;AAC3B,UAAM7E,OAAN;AAEA,SAAKmV,OAAL,GAAe,KAAKC,UAAL,CAAgBvQ,MAAhB,CAAf;AACA,SAAKmf,OAAL,GAAenkB,cAAc,CAACW,OAAf,CAAuBojB,eAAvB,EAAwC,KAAK/V,QAA7C,CAAf;AACA,SAAKoW,SAAL,GAAiB,KAAKC,mBAAL,EAAjB;AACA,SAAKC,QAAL,GAAgB,KAAhB;AACA,SAAKC,oBAAL,GAA4B,KAA5B;AACA,SAAKxJ,gBAAL,GAAwB,KAAxB;AACD,GAV+B;;;AAcd,aAAP5I,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD;;AAEc,aAAJvK,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GApB+B;;;AAwBhCwI,EAAAA,MAAM,CAACxE,aAAD,EAAgB;AACpB,WAAO,KAAK0Y,QAAL,GAAgB,KAAK5I,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAU/P,aAAV,CAArC;AACD;;AAED+P,EAAAA,IAAI,CAAC/P,aAAD,EAAgB;AAClB,QAAI,KAAK0Y,QAAL,IAAiB,KAAKvJ,gBAA1B,EAA4C;AAC1C;AACD;;AAED,QAAI,KAAKyJ,WAAL,EAAJ,EAAwB;AACtB,WAAKzJ,gBAAL,GAAwB,IAAxB;AACD;;AAED,UAAMoE,SAAS,GAAG9U,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoM,YAApC,EAAgD;AAChExO,MAAAA;AADgE,KAAhD,CAAlB;;AAIA,QAAI,KAAK0Y,QAAL,IAAiBnF,SAAS,CAACjS,gBAA/B,EAAiD;AAC/C;AACD;;AAED,SAAKoX,QAAL,GAAgB,IAAhB;AAEAG,IAAAA,IAAa;AAEbrkB,IAAAA,QAAQ,CAAC8G,IAAT,CAAcd,SAAd,CAAwBwR,GAAxB,CAA4BiM,eAA5B;;AAEA,SAAKa,aAAL;;AAEA,SAAKC,eAAL;;AACA,SAAKC,eAAL;;AAEAva,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+ByV,qBAA/B,EAAoDQ,uBAApD,EAA2E/Z,KAAK,IAAI,KAAKwR,IAAL,CAAUxR,KAAV,CAApF;AAEAG,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK6X,OAArB,EAA8BP,uBAA9B,EAAuD,MAAM;AAC3DvZ,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC2V,qBAAhC,EAAuDzZ,KAAK,IAAI;AAC9D,YAAIA,KAAK,CAACS,MAAN,KAAiB,KAAKqD,QAA1B,EAAoC;AAClC,eAAKuW,oBAAL,GAA4B,IAA5B;AACD;AACF,OAJD;AAKD,KAND;;AAQA,SAAKM,aAAL,CAAmB,MAAM,KAAKC,YAAL,CAAkBlZ,aAAlB,CAAzB;AACD;;AAED8P,EAAAA,IAAI,CAACxR,KAAD,EAAQ;AACV,QAAIA,KAAJ,EAAW;AACTA,MAAAA,KAAK,CAAC0D,cAAN;AACD;;AAED,QAAI,CAAC,KAAK0W,QAAN,IAAkB,KAAKvJ,gBAA3B,EAA6C;AAC3C;AACD;;AAED,UAAMiF,SAAS,GAAG3V,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCsM,YAApC,CAAlB;;AAEA,QAAI0F,SAAS,CAAC9S,gBAAd,EAAgC;AAC9B;AACD;;AAED,SAAKoX,QAAL,GAAgB,KAAhB;;AACA,UAAM9V,UAAU,GAAG,KAAKgW,WAAL,EAAnB;;AAEA,QAAIhW,UAAJ,EAAgB;AACd,WAAKuM,gBAAL,GAAwB,IAAxB;AACD;;AAED,SAAK4J,eAAL;;AACA,SAAKC,eAAL;;AAEAva,IAAAA,YAAY,CAACC,GAAb,CAAiBlK,QAAjB,EAA2BmjB,eAA3B;;AAEA,SAAKvV,QAAL,CAAc5H,SAAd,CAAwB4C,MAAxB,CAA+BkG,iBAA/B;;AAEA7E,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgCyV,qBAAhC;AACApZ,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK6Z,OAAtB,EAA+BP,uBAA/B;;AAEA,SAAKrV,cAAL,CAAoB,MAAM,KAAKwW,UAAL,EAA1B,EAA6C,KAAK/W,QAAlD,EAA4DQ,UAA5D;AACD;;AAEDL,EAAAA,OAAO,GAAG;AACR,KAAC3K,MAAD,EAAS,KAAK2gB,OAAd,EACG/e,OADH,CACW4f,WAAW,IAAI3a,YAAY,CAACC,GAAb,CAAiB0a,WAAjB,EAA8B5W,WAA9B,CAD1B;;AAGA,SAAKgW,SAAL,CAAejW,OAAf;;AACA,UAAMA,OAAN;AAEA;AACJ;AACA;AACA;AACA;;AACI9D,IAAAA,YAAY,CAACC,GAAb,CAAiBlK,QAAjB,EAA2BmjB,eAA3B;AACD;;AAED0B,EAAAA,YAAY,GAAG;AACb,SAAKP,aAAL;AACD,GAzH+B;;;AA6HhCL,EAAAA,mBAAmB,GAAG;AACpB,WAAO,IAAIzB,QAAJ,CAAa;AAClB/c,MAAAA,SAAS,EAAEoG,OAAO,CAAC,KAAKqJ,OAAL,CAAa2N,QAAd,CADA;AACyB;AAC3CzU,MAAAA,UAAU,EAAE,KAAKgW,WAAL;AAFM,KAAb,CAAP;AAID;;AAEDjP,EAAAA,UAAU,CAACvQ,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmN,SADI;AAEP,SAAGxB,WAAW,CAACI,iBAAZ,CAA8B,KAAK/C,QAAnC,CAFI;AAGP,SAAGhJ;AAHI,KAAT;AAKAF,IAAAA,eAAe,CAAC8C,MAAD,EAAO5C,MAAP,EAAe0N,aAAf,CAAf;AACA,WAAO1N,MAAP;AACD;;AAED8f,EAAAA,YAAY,CAAClZ,aAAD,EAAgB;AAC1B,UAAM4C,UAAU,GAAG,KAAKgW,WAAL,EAAnB;;AACA,UAAMU,SAAS,GAAGllB,cAAc,CAACW,OAAf,CAAuBqjB,mBAAvB,EAA4C,KAAKG,OAAjD,CAAlB;;AAEA,QAAI,CAAC,KAAKnW,QAAL,CAAc7M,UAAf,IAA6B,KAAK6M,QAAL,CAAc7M,UAAd,CAAyBC,QAAzB,KAAsCC,IAAI,CAACC,YAA5E,EAA0F;AACxF;AACAlB,MAAAA,QAAQ,CAAC8G,IAAT,CAAckc,WAAd,CAA0B,KAAKpV,QAA/B;AACD;;AAED,SAAKA,QAAL,CAAclI,KAAd,CAAoBG,OAApB,GAA8B,OAA9B;;AACA,SAAK+H,QAAL,CAAc8C,eAAd,CAA8B,aAA9B;;AACA,SAAK9C,QAAL,CAAcqC,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;AACA,SAAKrC,QAAL,CAAcqC,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;AACA,SAAKrC,QAAL,CAAcyD,SAAd,GAA0B,CAA1B;;AAEA,QAAIyT,SAAJ,EAAe;AACbA,MAAAA,SAAS,CAACzT,SAAV,GAAsB,CAAtB;AACD;;AAED,QAAIjD,UAAJ,EAAgB;AACd1H,MAAAA,MAAM,CAAC,KAAKkH,QAAN,CAAN;AACD;;AAED,SAAKA,QAAL,CAAc5H,SAAd,CAAwBwR,GAAxB,CAA4B1I,iBAA5B;;AAEA,QAAI,KAAKoG,OAAL,CAAasK,KAAjB,EAAwB;AACtB,WAAKuF,aAAL;AACD;;AAED,UAAMC,kBAAkB,GAAG,MAAM;AAC/B,UAAI,KAAK9P,OAAL,CAAasK,KAAjB,EAAwB;AACtB,aAAK5R,QAAL,CAAc4R,KAAd;AACD;;AAED,WAAK7E,gBAAL,GAAwB,KAAxB;AACA1Q,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqM,aAApC,EAAiD;AAC/CzO,QAAAA;AAD+C,OAAjD;AAGD,KATD;;AAWA,SAAK2C,cAAL,CAAoB6W,kBAApB,EAAwC,KAAKjB,OAA7C,EAAsD3V,UAAtD;AACD;;AAED2W,EAAAA,aAAa,GAAG;AACd9a,IAAAA,YAAY,CAACC,GAAb,CAAiBlK,QAAjB,EAA2BmjB,eAA3B,EADc;;AAEdlZ,IAAAA,YAAY,CAACiC,EAAb,CAAgBlM,QAAhB,EAA0BmjB,eAA1B,EAAyCrZ,KAAK,IAAI;AAChD,UAAI9J,QAAQ,KAAK8J,KAAK,CAACS,MAAnB,IACA,KAAKqD,QAAL,KAAkB9D,KAAK,CAACS,MADxB,IAEA,CAAC,KAAKqD,QAAL,CAAc3H,QAAd,CAAuB6D,KAAK,CAACS,MAA7B,CAFL,EAE2C;AACzC,aAAKqD,QAAL,CAAc4R,KAAd;AACD;AACF,KAND;AAOD;;AAED+E,EAAAA,eAAe,GAAG;AAChB,QAAI,KAAKL,QAAT,EAAmB;AACjBja,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+B0V,uBAA/B,EAAsDxZ,KAAK,IAAI;AAC7D,YAAI,KAAKoL,OAAL,CAAajD,QAAb,IAAyBnI,KAAK,CAAC5B,GAAN,KAAc0U,YAA3C,EAAuD;AACrD9S,UAAAA,KAAK,CAAC0D,cAAN;AACA,eAAK8N,IAAL;AACD,SAHD,MAGO,IAAI,CAAC,KAAKpG,OAAL,CAAajD,QAAd,IAA0BnI,KAAK,CAAC5B,GAAN,KAAc0U,YAA5C,EAAwD;AAC7D,eAAKqI,0BAAL;AACD;AACF,OAPD;AAQD,KATD,MASO;AACLhb,MAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgC0V,uBAAhC;AACD;AACF;;AAEDkB,EAAAA,eAAe,GAAG;AAChB,QAAI,KAAKN,QAAT,EAAmB;AACjBja,MAAAA,YAAY,CAACiC,EAAb,CAAgB9I,MAAhB,EAAwBggB,YAAxB,EAAsC,MAAM,KAAKkB,aAAL,EAA5C;AACD,KAFD,MAEO;AACLra,MAAAA,YAAY,CAACC,GAAb,CAAiB9G,MAAjB,EAAyBggB,YAAzB;AACD;AACF;;AAEDuB,EAAAA,UAAU,GAAG;AACX,SAAK/W,QAAL,CAAclI,KAAd,CAAoBG,OAApB,GAA8B,MAA9B;;AACA,SAAK+H,QAAL,CAAcqC,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;AACA,SAAKrC,QAAL,CAAc8C,eAAd,CAA8B,YAA9B;;AACA,SAAK9C,QAAL,CAAc8C,eAAd,CAA8B,MAA9B;;AACA,SAAKiK,gBAAL,GAAwB,KAAxB;;AACA,SAAKqJ,SAAL,CAAe1I,IAAf,CAAoB,MAAM;AACxBtb,MAAAA,QAAQ,CAAC8G,IAAT,CAAcd,SAAd,CAAwB4C,MAAxB,CAA+B6a,eAA/B;;AACA,WAAKyB,iBAAL;;AACAC,MAAAA,KAAc;AACdlb,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCuM,cAApC;AACD,KALD;AAMD;;AAEDsK,EAAAA,aAAa,CAACzd,QAAD,EAAW;AACtBiD,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+ByV,qBAA/B,EAAoDvZ,KAAK,IAAI;AAC3D,UAAI,KAAKqa,oBAAT,EAA+B;AAC7B,aAAKA,oBAAL,GAA4B,KAA5B;AACA;AACD;;AAED,UAAIra,KAAK,CAACS,MAAN,KAAiBT,KAAK,CAACsb,aAA3B,EAA0C;AACxC;AACD;;AAED,UAAI,KAAKlQ,OAAL,CAAa2N,QAAb,KAA0B,IAA9B,EAAoC;AAClC,aAAKvH,IAAL;AACD,OAFD,MAEO,IAAI,KAAKpG,OAAL,CAAa2N,QAAb,KAA0B,QAA9B,EAAwC;AAC7C,aAAKoC,0BAAL;AACD;AACF,KAfD;;AAiBA,SAAKjB,SAAL,CAAezI,IAAf,CAAoBvU,QAApB;AACD;;AAEDod,EAAAA,WAAW,GAAG;AACZ,WAAO,KAAKxW,QAAL,CAAc5H,SAAd,CAAwBC,QAAxB,CAAiC4I,iBAAjC,CAAP;AACD;;AAEDoW,EAAAA,0BAA0B,GAAG;AAC3B,UAAMrF,SAAS,GAAG3V,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCsV,oBAApC,CAAlB;;AACA,QAAItD,SAAS,CAAC9S,gBAAd,EAAgC;AAC9B;AACD;;AAED,UAAMuY,kBAAkB,GAAG,KAAKzX,QAAL,CAAc0X,YAAd,GAA6BtlB,QAAQ,CAACC,eAAT,CAAyBslB,YAAjF;;AAEA,QAAI,CAACF,kBAAL,EAAyB;AACvB,WAAKzX,QAAL,CAAclI,KAAd,CAAoB8f,SAApB,GAAgC,QAAhC;AACD;;AAED,SAAK5X,QAAL,CAAc5H,SAAd,CAAwBwR,GAAxB,CAA4BkM,iBAA5B;;AACA,UAAM+B,uBAAuB,GAAGxiB,gCAAgC,CAAC,KAAK8gB,OAAN,CAAhE;AACA9Z,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgC,eAAhC;AACA3D,IAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiD,MAAM;AACrD,WAAKA,QAAL,CAAc5H,SAAd,CAAwB4C,MAAxB,CAA+B8a,iBAA/B;;AACA,UAAI,CAAC2B,kBAAL,EAAyB;AACvBpb,QAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiD,MAAM;AACrD,eAAKA,QAAL,CAAclI,KAAd,CAAoB8f,SAApB,GAAgC,EAAhC;AACD,SAFD;AAGAvhB,QAAAA,oBAAoB,CAAC,KAAK2J,QAAN,EAAgB6X,uBAAhB,CAApB;AACD;AACF,KARD;AASAxhB,IAAAA,oBAAoB,CAAC,KAAK2J,QAAN,EAAgB6X,uBAAhB,CAApB;;AACA,SAAK7X,QAAL,CAAc4R,KAAd;AACD,GA5R+B;AA+RhC;AACA;;;AAEA8E,EAAAA,aAAa,GAAG;AACd,UAAMe,kBAAkB,GAAG,KAAKzX,QAAL,CAAc0X,YAAd,GAA6BtlB,QAAQ,CAACC,eAAT,CAAyBslB,YAAjF;AACA,UAAMtD,cAAc,GAAGyD,QAAiB,EAAxC;AACA,UAAMC,iBAAiB,GAAG1D,cAAc,GAAG,CAA3C;;AAEA,QAAK,CAAC0D,iBAAD,IAAsBN,kBAAtB,IAA4C,CAACne,KAAK,EAAnD,IAA2Dye,iBAAiB,IAAI,CAACN,kBAAtB,IAA4Cne,KAAK,EAAhH,EAAqH;AACnH,WAAK0G,QAAL,CAAclI,KAAd,CAAoBkgB,WAApB,GAAmC,GAAE3D,cAAe,IAApD;AACD;;AAED,QAAK0D,iBAAiB,IAAI,CAACN,kBAAtB,IAA4C,CAACne,KAAK,EAAnD,IAA2D,CAACye,iBAAD,IAAsBN,kBAAtB,IAA4Cne,KAAK,EAAhH,EAAqH;AACnH,WAAK0G,QAAL,CAAclI,KAAd,CAAoBmgB,YAApB,GAAoC,GAAE5D,cAAe,IAArD;AACD;AACF;;AAEDiD,EAAAA,iBAAiB,GAAG;AAClB,SAAKtX,QAAL,CAAclI,KAAd,CAAoBkgB,WAApB,GAAkC,EAAlC;AACA,SAAKhY,QAAL,CAAclI,KAAd,CAAoBmgB,YAApB,GAAmC,EAAnC;AACD,GAnT+B;;;AAuTV,SAAfle,eAAe,CAAC/C,MAAD,EAAS4G,aAAT,EAAwB;AAC5C,WAAO,KAAKiE,IAAL,CAAU,YAAY;AAC3B,YAAMC,IAAI,GAAGoU,KAAK,CAACzV,WAAN,CAAkB,IAAlB,KAA2B,IAAIyV,KAAJ,CAAU,IAAV,EAAgB,OAAOlf,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAAtD,CAAxC;;AAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B;AACD;;AAED,UAAI,OAAO8K,IAAI,CAAC9K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED8K,MAAAA,IAAI,CAAC9K,MAAD,CAAJ,CAAa4G,aAAb;AACD,KAZM,CAAP;AAaD;;AArU+B;AAwUlC;AACA;AACA;AACA;AACA;;;AAEAvB,YAAY,CAACiC,EAAb,CAAgBlM,QAAhB,EAA0B2O,sBAA1B,EAAgDmB,sBAAhD,EAAsE,UAAUhG,KAAV,EAAiB;AACrF,QAAMS,MAAM,GAAGvH,sBAAsB,CAAC,IAAD,CAArC;;AAEA,MAAI,CAAC,GAAD,EAAM,MAAN,EAAcL,QAAd,CAAuB,KAAK8U,OAA5B,CAAJ,EAA0C;AACxC3N,IAAAA,KAAK,CAAC0D,cAAN;AACD;;AAEDvD,EAAAA,YAAY,CAACkC,GAAb,CAAiB5B,MAAjB,EAAyByP,YAAzB,EAAqC+E,SAAS,IAAI;AAChD,QAAIA,SAAS,CAACjS,gBAAd,EAAgC;AAC9B;AACA;AACD;;AAED7C,IAAAA,YAAY,CAACkC,GAAb,CAAiB5B,MAAjB,EAAyB4P,cAAzB,EAAuC,MAAM;AAC3C,UAAI1U,SAAS,CAAC,IAAD,CAAb,EAAqB;AACnB,aAAK+Z,KAAL;AACD;AACF,KAJD;AAKD,GAXD;AAaA,QAAM9P,IAAI,GAAGoU,KAAK,CAACzV,WAAN,CAAkB9D,MAAlB,KAA6B,IAAIuZ,KAAJ,CAAUvZ,MAAV,CAA1C;AAEAmF,EAAAA,IAAI,CAACM,MAAL,CAAY,IAAZ;AACD,CAvBD;AAyBA;AACA;AACA;AACA;AACA;AACA;;AAEA5I,kBAAkB,CAAC0c,KAAD,CAAlB;;AC3bA;AACA;AACA;AACA;AACA;AACA;AAiBA;AACA;AACA;AACA;AACA;;AAEA,MAAMtc,MAAI,GAAG,WAAb;AACA,MAAMsG,UAAQ,GAAG,cAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMS,cAAY,GAAG,WAArB;AACA,MAAM+E,qBAAmB,GAAI,OAAMtF,WAAU,GAAEO,cAAa,EAA5D;AACA,MAAMqO,UAAU,GAAG,QAAnB;AAEA,MAAM7K,SAAO,GAAG;AACd8Q,EAAAA,QAAQ,EAAE,IADI;AAEd5Q,EAAAA,QAAQ,EAAE,IAFI;AAGd6T,EAAAA,MAAM,EAAE;AAHM,CAAhB;AAMA,MAAMxT,aAAW,GAAG;AAClBuQ,EAAAA,QAAQ,EAAE,SADQ;AAElB5Q,EAAAA,QAAQ,EAAE,SAFQ;AAGlB6T,EAAAA,MAAM,EAAE;AAHU,CAApB;AAMA,MAAMhX,iBAAe,GAAG,MAAxB;AACA,MAAMiX,aAAa,GAAG,iBAAtB;AAEA,MAAM/L,YAAU,GAAI,OAAMhM,WAAU,EAApC;AACA,MAAMiM,aAAW,GAAI,QAAOjM,WAAU,EAAtC;AACA,MAAMkM,YAAU,GAAI,OAAMlM,WAAU,EAApC;AACA,MAAMmM,cAAY,GAAI,SAAQnM,WAAU,EAAxC;AACA,MAAMmV,eAAa,GAAI,UAASnV,WAAU,EAA1C;AACA,MAAMW,sBAAoB,GAAI,QAAOX,WAAU,GAAEO,cAAa,EAA9D;AACA,MAAM8U,qBAAmB,GAAI,gBAAerV,WAAU,EAAtD;AACA,MAAMsV,qBAAqB,GAAI,kBAAiBtV,WAAU,EAA1D;AAEA,MAAM6V,uBAAqB,GAAG,+BAA9B;AACA,MAAM/T,sBAAoB,GAAG,8BAA7B;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMkW,SAAN,SAAwBtY,aAAxB,CAAsC;AACpCC,EAAAA,WAAW,CAAC5N,OAAD,EAAU6E,MAAV,EAAkB;AAC3B,UAAM7E,OAAN;AAEA,SAAKmV,OAAL,GAAe,KAAKC,UAAL,CAAgBvQ,MAAhB,CAAf;AACA,SAAKsf,QAAL,GAAgB,KAAhB;AACA,SAAKF,SAAL,GAAiB,KAAKC,mBAAL,EAAjB;;AACA,SAAKvO,kBAAL;AACD,GARmC;;;AAYrB,aAAJlO,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD;;AAEiB,aAAPuK,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD,GAlBmC;;;AAsBpC/B,EAAAA,MAAM,CAACxE,aAAD,EAAgB;AACpB,WAAO,KAAK0Y,QAAL,GAAgB,KAAK5I,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAU/P,aAAV,CAArC;AACD;;AAED+P,EAAAA,IAAI,CAAC/P,aAAD,EAAgB;AAClB,QAAI,KAAK0Y,QAAT,EAAmB;AACjB;AACD;;AAED,UAAMnF,SAAS,GAAG9U,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoM,YAApC,EAAgD;AAAExO,MAAAA;AAAF,KAAhD,CAAlB;;AAEA,QAAIuT,SAAS,CAACjS,gBAAd,EAAgC;AAC9B;AACD;;AAED,SAAKoX,QAAL,GAAgB,IAAhB;AACA,SAAKtW,QAAL,CAAclI,KAAd,CAAoBI,UAApB,GAAiC,SAAjC;;AAEA,SAAKke,SAAL,CAAezI,IAAf;;AAEA,QAAI,CAAC,KAAKrG,OAAL,CAAa4Q,MAAlB,EAA0B;AACxBzB,MAAAA,IAAa;;AACb,WAAK4B,sBAAL,CAA4B,KAAKrY,QAAjC;AACD;;AAED,SAAKA,QAAL,CAAc8C,eAAd,CAA8B,aAA9B;;AACA,SAAK9C,QAAL,CAAcqC,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;AACA,SAAKrC,QAAL,CAAcqC,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;AACA,SAAKrC,QAAL,CAAc5H,SAAd,CAAwBwR,GAAxB,CAA4B1I,iBAA5B;;AAEA,UAAM0K,gBAAgB,GAAG,MAAM;AAC7BvP,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqM,aAApC,EAAiD;AAAEzO,QAAAA;AAAF,OAAjD;AACD,KAFD;;AAIA,SAAK2C,cAAL,CAAoBqL,gBAApB,EAAsC,KAAK5L,QAA3C,EAAqD,IAArD;AACD;;AAED0N,EAAAA,IAAI,GAAG;AACL,QAAI,CAAC,KAAK4I,QAAV,EAAoB;AAClB;AACD;;AAED,UAAMtE,SAAS,GAAG3V,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCsM,YAApC,CAAlB;;AAEA,QAAI0F,SAAS,CAAC9S,gBAAd,EAAgC;AAC9B;AACD;;AAED7C,IAAAA,YAAY,CAACC,GAAb,CAAiBlK,QAAjB,EAA2BmjB,eAA3B;;AACA,SAAKvV,QAAL,CAAcsY,IAAd;;AACA,SAAKhC,QAAL,GAAgB,KAAhB;;AACA,SAAKtW,QAAL,CAAc5H,SAAd,CAAwB4C,MAAxB,CAA+BkG,iBAA/B;;AACA,SAAKkV,SAAL,CAAe1I,IAAf;;AAEA,UAAM6K,gBAAgB,GAAG,MAAM;AAC7B,WAAKvY,QAAL,CAAcqC,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;AACA,WAAKrC,QAAL,CAAc8C,eAAd,CAA8B,YAA9B;;AACA,WAAK9C,QAAL,CAAc8C,eAAd,CAA8B,MAA9B;;AACA,WAAK9C,QAAL,CAAclI,KAAd,CAAoBI,UAApB,GAAiC,QAAjC;;AAEA,UAAI,CAAC,KAAKoP,OAAL,CAAa4Q,MAAlB,EAA0B;AACxBX,QAAAA,KAAc;AACf;;AAEDlb,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCuM,cAApC;AACD,KAXD;;AAaA,SAAKhM,cAAL,CAAoBgY,gBAApB,EAAsC,KAAKvY,QAA3C,EAAqD,IAArD;AACD;;AAEDG,EAAAA,OAAO,GAAG;AACR,SAAKiW,SAAL,CAAejW,OAAf;;AACA,UAAMA,OAAN;AACA9D,IAAAA,YAAY,CAACC,GAAb,CAAiBlK,QAAjB,EAA2BmjB,eAA3B;AACD,GAhGmC;;;AAoGpChO,EAAAA,UAAU,CAACvQ,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmN,SADI;AAEP,SAAGxB,WAAW,CAACI,iBAAZ,CAA8B,KAAK/C,QAAnC,CAFI;AAGP,UAAI,OAAOhJ,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;AAHO,KAAT;AAKAF,IAAAA,eAAe,CAAC8C,MAAD,EAAO5C,MAAP,EAAe0N,aAAf,CAAf;AACA,WAAO1N,MAAP;AACD;;AAEDqf,EAAAA,mBAAmB,GAAG;AACpB,WAAO,IAAIzB,QAAJ,CAAa;AAClB/c,MAAAA,SAAS,EAAE,KAAKyP,OAAL,CAAa2N,QADN;AAElBzU,MAAAA,UAAU,EAAE,IAFM;AAGlBa,MAAAA,WAAW,EAAE,KAAKrB,QAAL,CAAc7M,UAHT;AAIlBshB,MAAAA,aAAa,EAAE,MAAM,KAAK/G,IAAL;AAJH,KAAb,CAAP;AAMD;;AAED2K,EAAAA,sBAAsB,CAAClmB,OAAD,EAAU;AAC9BkK,IAAAA,YAAY,CAACC,GAAb,CAAiBlK,QAAjB,EAA2BmjB,eAA3B,EAD8B;;AAE9BlZ,IAAAA,YAAY,CAACiC,EAAb,CAAgBlM,QAAhB,EAA0BmjB,eAA1B,EAAyCrZ,KAAK,IAAI;AAChD,UAAI9J,QAAQ,KAAK8J,KAAK,CAACS,MAAnB,IACFxK,OAAO,KAAK+J,KAAK,CAACS,MADhB,IAEF,CAACxK,OAAO,CAACkG,QAAR,CAAiB6D,KAAK,CAACS,MAAvB,CAFH,EAEmC;AACjCxK,QAAAA,OAAO,CAACyf,KAAR;AACD;AACF,KAND;AAOAzf,IAAAA,OAAO,CAACyf,KAAR;AACD;;AAED9J,EAAAA,kBAAkB,GAAG;AACnBzL,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+ByV,qBAA/B,EAAoDQ,uBAApD,EAA2E,MAAM,KAAKvI,IAAL,EAAjF;AAEArR,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+B0V,qBAA/B,EAAsDxZ,KAAK,IAAI;AAC7D,UAAI,KAAKoL,OAAL,CAAajD,QAAb,IAAyBnI,KAAK,CAAC5B,GAAN,KAAc0U,UAA3C,EAAuD;AACrD,aAAKtB,IAAL;AACD;AACF,KAJD;AAKD,GA3ImC;;;AA+Id,SAAf3T,eAAe,CAAC/C,MAAD,EAAS;AAC7B,WAAO,KAAK6K,IAAL,CAAU,YAAY;AAC3B,YAAMC,IAAI,GAAG7B,IAAI,CAACvF,GAAL,CAAS,IAAT,EAAewF,UAAf,KAA4B,IAAIkY,SAAJ,CAAc,IAAd,EAAoB,OAAOphB,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1D,CAAzC;;AAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B;AACD;;AAED,UAAI8K,IAAI,CAAC9K,MAAD,CAAJ,KAAiB9C,SAAjB,IAA8B8C,MAAM,CAAChC,UAAP,CAAkB,GAAlB,CAA9B,IAAwDgC,MAAM,KAAK,aAAvE,EAAsF;AACpF,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED8K,MAAAA,IAAI,CAAC9K,MAAD,CAAJ,CAAa,IAAb;AACD,KAZM,CAAP;AAaD;;AA7JmC;AAgKtC;AACA;AACA;AACA;AACA;;;AAEAqF,YAAY,CAACiC,EAAb,CAAgBlM,QAAhB,EAA0B2O,sBAA1B,EAAgDmB,sBAAhD,EAAsE,UAAUhG,KAAV,EAAiB;AACrF,QAAMS,MAAM,GAAGvH,sBAAsB,CAAC,IAAD,CAArC;;AAEA,MAAI,CAAC,GAAD,EAAM,MAAN,EAAcL,QAAd,CAAuB,KAAK8U,OAA5B,CAAJ,EAA0C;AACxC3N,IAAAA,KAAK,CAAC0D,cAAN;AACD;;AAED,MAAIzH,UAAU,CAAC,IAAD,CAAd,EAAsB;AACpB;AACD;;AAEDkE,EAAAA,YAAY,CAACkC,GAAb,CAAiB5B,MAAjB,EAAyB4P,cAAzB,EAAuC,MAAM;AAC3C;AACA,QAAI1U,SAAS,CAAC,IAAD,CAAb,EAAqB;AACnB,WAAK+Z,KAAL;AACD;AACF,GALD,EAXqF;;AAmBrF,QAAM4G,YAAY,GAAGxmB,cAAc,CAACW,OAAf,CAAuBwlB,aAAvB,CAArB;;AACA,MAAIK,YAAY,IAAIA,YAAY,KAAK7b,MAArC,EAA6C;AAC3Cyb,IAAAA,SAAS,CAAC3X,WAAV,CAAsB+X,YAAtB,EAAoC9K,IAApC;AACD;;AAED,QAAM5L,IAAI,GAAG7B,IAAI,CAACvF,GAAL,CAASiC,MAAT,EAAiBuD,UAAjB,KAA8B,IAAIkY,SAAJ,CAAczb,MAAd,CAA3C;AAEAmF,EAAAA,IAAI,CAACM,MAAL,CAAY,IAAZ;AACD,CA3BD;AA6BA/F,YAAY,CAACiC,EAAb,CAAgB9I,MAAhB,EAAwBkQ,qBAAxB,EAA6C,MAAM;AACjD1T,EAAAA,cAAc,CAACC,IAAf,CAAoBkmB,aAApB,EAAmC/gB,OAAnC,CAA2CqhB,EAAE,IAAI,CAACxY,IAAI,CAACvF,GAAL,CAAS+d,EAAT,EAAavY,UAAb,KAA0B,IAAIkY,SAAJ,CAAcK,EAAd,CAA3B,EAA8C9K,IAA9C,EAAjD;AACD,CAFD;AAIA;AACA;AACA;AACA;AACA;;AAEAnU,kBAAkB,CAAC4e,SAAD,CAAlB;;ACjRA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAMM,QAAQ,GAAG,IAAI9c,GAAJ,CAAQ,CACvB,YADuB,EAEvB,MAFuB,EAGvB,MAHuB,EAIvB,UAJuB,EAKvB,UALuB,EAMvB,QANuB,EAOvB,KAPuB,EAQvB,YARuB,CAAR,CAAjB;AAWA,MAAM+c,sBAAsB,GAAG,gBAA/B;AAEA;AACA;AACA;AACA;AACA;;AACA,MAAMC,gBAAgB,GAAG,4DAAzB;AAEA;AACA;AACA;AACA;AACA;;AACA,MAAMC,gBAAgB,GAAG,oIAAzB;;AAEA,MAAMC,gBAAgB,GAAG,CAACC,IAAD,EAAOC,oBAAP,KAAgC;AACvD,QAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAc7kB,WAAd,EAAjB;;AAEA,MAAI2kB,oBAAoB,CAACjkB,QAArB,CAA8BkkB,QAA9B,CAAJ,EAA6C;AAC3C,QAAIP,QAAQ,CAACle,GAAT,CAAaye,QAAb,CAAJ,EAA4B;AAC1B,aAAOhb,OAAO,CAAC2a,gBAAgB,CAAClhB,IAAjB,CAAsBqhB,IAAI,CAACI,SAA3B,KAAyCN,gBAAgB,CAACnhB,IAAjB,CAAsBqhB,IAAI,CAACI,SAA3B,CAA1C,CAAd;AACD;;AAED,WAAO,IAAP;AACD;;AAED,QAAMC,MAAM,GAAGJ,oBAAoB,CAAClmB,MAArB,CAA4BumB,SAAS,IAAIA,SAAS,YAAY5hB,MAA9D,CAAf,CAXuD;;AAcvD,OAAK,IAAImF,CAAC,GAAG,CAAR,EAAWK,GAAG,GAAGmc,MAAM,CAAChjB,MAA7B,EAAqCwG,CAAC,GAAGK,GAAzC,EAA8CL,CAAC,EAA/C,EAAmD;AACjD,QAAIwc,MAAM,CAACxc,CAAD,CAAN,CAAUlF,IAAV,CAAeuhB,QAAf,CAAJ,EAA8B;AAC5B,aAAO,IAAP;AACD;AACF;;AAED,SAAO,KAAP;AACD,CArBD;;AAuBO,MAAMK,gBAAgB,GAAG;AAC9B;AACA,OAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCX,sBAAvC,CAFyB;AAG9BY,EAAAA,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;AAI9BC,EAAAA,IAAI,EAAE,EAJwB;AAK9BC,EAAAA,CAAC,EAAE,EAL2B;AAM9BC,EAAAA,EAAE,EAAE,EAN0B;AAO9BC,EAAAA,GAAG,EAAE,EAPyB;AAQ9BC,EAAAA,IAAI,EAAE,EARwB;AAS9BC,EAAAA,GAAG,EAAE,EATyB;AAU9BC,EAAAA,EAAE,EAAE,EAV0B;AAW9BC,EAAAA,EAAE,EAAE,EAX0B;AAY9BC,EAAAA,EAAE,EAAE,EAZ0B;AAa9BC,EAAAA,EAAE,EAAE,EAb0B;AAc9BC,EAAAA,EAAE,EAAE,EAd0B;AAe9BC,EAAAA,EAAE,EAAE,EAf0B;AAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;AAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;AAkB9Bzd,EAAAA,CAAC,EAAE,EAlB2B;AAmB9B0d,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;AAoB9BC,EAAAA,EAAE,EAAE,EApB0B;AAqB9BC,EAAAA,EAAE,EAAE,EArB0B;AAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;AAuB9BC,EAAAA,GAAG,EAAE,EAvByB;AAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;AAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;AA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;AA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;AA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;AA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;AA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;AA+B9BC,EAAAA,EAAE,EAAE;AA/B0B,CAAzB;AAkCA,SAASC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyD;AAC9D,MAAI,CAACF,UAAU,CAAChlB,MAAhB,EAAwB;AACtB,WAAOglB,UAAP;AACD;;AAED,MAAIE,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;AAClD,WAAOA,UAAU,CAACF,UAAD,CAAjB;AACD;;AAED,QAAMG,SAAS,GAAG,IAAI/lB,MAAM,CAACgmB,SAAX,EAAlB;AACA,QAAMC,eAAe,GAAGF,SAAS,CAACG,eAAV,CAA0BN,UAA1B,EAAsC,WAAtC,CAAxB;AACA,QAAMO,aAAa,GAAGzkB,MAAM,CAACC,IAAP,CAAYkkB,SAAZ,CAAtB;AACA,QAAMO,QAAQ,GAAG,GAAGtpB,MAAH,CAAU,GAAGmpB,eAAe,CAACviB,IAAhB,CAAqBzG,gBAArB,CAAsC,GAAtC,CAAb,CAAjB;;AAEA,OAAK,IAAImK,CAAC,GAAG,CAAR,EAAWK,GAAG,GAAG2e,QAAQ,CAACxlB,MAA/B,EAAuCwG,CAAC,GAAGK,GAA3C,EAAgDL,CAAC,EAAjD,EAAqD;AACnD,UAAM6b,EAAE,GAAGmD,QAAQ,CAAChf,CAAD,CAAnB;AACA,UAAMif,MAAM,GAAGpD,EAAE,CAACS,QAAH,CAAY7kB,WAAZ,EAAf;;AAEA,QAAI,CAACsnB,aAAa,CAAC5mB,QAAd,CAAuB8mB,MAAvB,CAAL,EAAqC;AACnCpD,MAAAA,EAAE,CAACtlB,UAAH,CAAcyO,WAAd,CAA0B6W,EAA1B;AAEA;AACD;;AAED,UAAMqD,aAAa,GAAG,GAAGxpB,MAAH,CAAU,GAAGmmB,EAAE,CAACzV,UAAhB,CAAtB;AACA,UAAM+Y,iBAAiB,GAAG,GAAGzpB,MAAH,CAAU+oB,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACQ,MAAD,CAAT,IAAqB,EAArD,CAA1B;AAEAC,IAAAA,aAAa,CAAC1kB,OAAd,CAAsB2hB,IAAI,IAAI;AAC5B,UAAI,CAACD,gBAAgB,CAACC,IAAD,EAAOgD,iBAAP,CAArB,EAAgD;AAC9CtD,QAAAA,EAAE,CAAC3V,eAAH,CAAmBiW,IAAI,CAACG,QAAxB;AACD;AACF,KAJD;AAKD;;AAED,SAAOuC,eAAe,CAACviB,IAAhB,CAAqB8iB,SAA5B;AACD;;AC9HD;AACA;AACA;AACA;AACA;AACA;AAwBA;AACA;AACA;AACA;AACA;;AAEA,MAAMpiB,MAAI,GAAG,SAAb;AACA,MAAMsG,UAAQ,GAAG,YAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAM+b,cAAY,GAAG,YAArB;AACA,MAAMC,oBAAkB,GAAG,IAAIzkB,MAAJ,CAAY,UAASwkB,cAAa,MAAlC,EAAyC,GAAzC,CAA3B;AACA,MAAME,qBAAqB,GAAG,IAAIvgB,GAAJ,CAAQ,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAAR,CAA9B;AAEA,MAAM8I,aAAW,GAAG;AAClB0X,EAAAA,SAAS,EAAE,SADO;AAElBC,EAAAA,QAAQ,EAAE,QAFQ;AAGlBC,EAAAA,KAAK,EAAE,2BAHW;AAIlBzd,EAAAA,OAAO,EAAE,QAJS;AAKlB0d,EAAAA,KAAK,EAAE,iBALW;AAMlBC,EAAAA,IAAI,EAAE,SANY;AAOlBtqB,EAAAA,QAAQ,EAAE,kBAPQ;AAQlBugB,EAAAA,SAAS,EAAE,mBARO;AASlBpP,EAAAA,MAAM,EAAE,yBATU;AAUlByK,EAAAA,SAAS,EAAE,0BAVO;AAWlB2O,EAAAA,kBAAkB,EAAE,OAXF;AAYlBlM,EAAAA,QAAQ,EAAE,kBAZQ;AAalBmM,EAAAA,WAAW,EAAE,mBAbK;AAclBC,EAAAA,QAAQ,EAAE,SAdQ;AAelBrB,EAAAA,UAAU,EAAE,iBAfM;AAgBlBD,EAAAA,SAAS,EAAE,QAhBO;AAiBlB5K,EAAAA,YAAY,EAAE;AAjBI,CAApB;AAoBA,MAAMmM,aAAa,GAAG;AACpBC,EAAAA,IAAI,EAAE,MADc;AAEpBC,EAAAA,GAAG,EAAE,KAFe;AAGpBC,EAAAA,KAAK,EAAEzjB,KAAK,KAAK,MAAL,GAAc,OAHN;AAIpB0jB,EAAAA,MAAM,EAAE,QAJY;AAKpBC,EAAAA,IAAI,EAAE3jB,KAAK,KAAK,OAAL,GAAe;AALN,CAAtB;AAQA,MAAM6K,SAAO,GAAG;AACdiY,EAAAA,SAAS,EAAE,IADG;AAEdC,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEE,mCAFF,GAGA,QALI;AAMdxd,EAAAA,OAAO,EAAE,aANK;AAOdyd,EAAAA,KAAK,EAAE,EAPO;AAQdC,EAAAA,KAAK,EAAE,CARO;AASdC,EAAAA,IAAI,EAAE,KATQ;AAUdtqB,EAAAA,QAAQ,EAAE,KAVI;AAWdugB,EAAAA,SAAS,EAAE,KAXG;AAYdpP,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAZM;AAadyK,EAAAA,SAAS,EAAE,KAbG;AAcd2O,EAAAA,kBAAkB,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,CAdN;AAedlM,EAAAA,QAAQ,EAAE,iBAfI;AAgBdmM,EAAAA,WAAW,EAAE,EAhBC;AAiBdC,EAAAA,QAAQ,EAAE,IAjBI;AAkBdrB,EAAAA,UAAU,EAAE,IAlBE;AAmBdD,EAAAA,SAAS,EAAE/B,gBAnBG;AAoBd7I,EAAAA,YAAY,EAAE;AApBA,CAAhB;AAuBA,MAAMza,OAAK,GAAG;AACZknB,EAAAA,IAAI,EAAG,OAAM9c,WAAU,EADX;AAEZ+c,EAAAA,MAAM,EAAG,SAAQ/c,WAAU,EAFf;AAGZgd,EAAAA,IAAI,EAAG,OAAMhd,WAAU,EAHX;AAIZid,EAAAA,KAAK,EAAG,QAAOjd,WAAU,EAJb;AAKZkd,EAAAA,QAAQ,EAAG,WAAUld,WAAU,EALnB;AAMZmd,EAAAA,KAAK,EAAG,QAAOnd,WAAU,EANb;AAOZod,EAAAA,OAAO,EAAG,UAASpd,WAAU,EAPjB;AAQZqd,EAAAA,QAAQ,EAAG,WAAUrd,WAAU,EARnB;AASZsd,EAAAA,UAAU,EAAG,aAAYtd,WAAU,EATvB;AAUZud,EAAAA,UAAU,EAAG,aAAYvd,WAAU;AAVvB,CAAd;AAaA,MAAMa,iBAAe,GAAG,MAAxB;AACA,MAAM2c,gBAAgB,GAAG,OAAzB;AACA,MAAM1c,iBAAe,GAAG,MAAxB;AAEA,MAAM2c,gBAAgB,GAAG,MAAzB;AACA,MAAMC,eAAe,GAAG,KAAxB;AAEA,MAAMC,sBAAsB,GAAG,gBAA/B;AAEA,MAAMC,aAAa,GAAG,OAAtB;AACA,MAAMC,aAAa,GAAG,OAAtB;AACA,MAAMC,aAAa,GAAG,OAAtB;AACA,MAAMC,cAAc,GAAG,QAAvB;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,OAAN,SAAsBte,aAAtB,CAAoC;AAClCC,EAAAA,WAAW,CAAC5N,OAAD,EAAU6E,MAAV,EAAkB;AAC3B,QAAI,OAAOoa,MAAP,KAAkB,WAAtB,EAAmC;AACjC,YAAM,IAAIzZ,SAAJ,CAAc,8DAAd,CAAN;AACD;;AAED,UAAMxF,OAAN,EAL2B;;AAQ3B,SAAKksB,UAAL,GAAkB,IAAlB;AACA,SAAKC,QAAL,GAAgB,CAAhB;AACA,SAAKC,WAAL,GAAmB,EAAnB;AACA,SAAKC,cAAL,GAAsB,EAAtB;AACA,SAAK5N,OAAL,GAAe,IAAf,CAZ2B;;AAe3B,SAAKtJ,OAAL,GAAe,KAAKC,UAAL,CAAgBvQ,MAAhB,CAAf;AACA,SAAKynB,GAAL,GAAW,IAAX;;AAEA,SAAKC,aAAL;AACD,GApBiC;;;AAwBhB,aAAPva,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD;;AAEc,aAAJvK,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD;;AAEe,aAAL5D,KAAK,GAAG;AACjB,WAAOA,OAAP;AACD;;AAEqB,aAAX0O,WAAW,GAAG;AACvB,WAAOA,aAAP;AACD,GAtCiC;;;AA0ClCia,EAAAA,MAAM,GAAG;AACP,SAAKN,UAAL,GAAkB,IAAlB;AACD;;AAEDO,EAAAA,OAAO,GAAG;AACR,SAAKP,UAAL,GAAkB,KAAlB;AACD;;AAEDQ,EAAAA,aAAa,GAAG;AACd,SAAKR,UAAL,GAAkB,CAAC,KAAKA,UAAxB;AACD;;AAEDjc,EAAAA,MAAM,CAAClG,KAAD,EAAQ;AACZ,QAAI,CAAC,KAAKmiB,UAAV,EAAsB;AACpB;AACD;;AAED,QAAIniB,KAAJ,EAAW;AACT,YAAM8W,OAAO,GAAG,KAAK8L,4BAAL,CAAkC5iB,KAAlC,CAAhB;;AAEA8W,MAAAA,OAAO,CAACwL,cAAR,CAAuBjL,KAAvB,GAA+B,CAACP,OAAO,CAACwL,cAAR,CAAuBjL,KAAvD;;AAEA,UAAIP,OAAO,CAAC+L,oBAAR,EAAJ,EAAoC;AAClC/L,QAAAA,OAAO,CAACgM,MAAR,CAAe,IAAf,EAAqBhM,OAArB;AACD,OAFD,MAEO;AACLA,QAAAA,OAAO,CAACiM,MAAR,CAAe,IAAf,EAAqBjM,OAArB;AACD;AACF,KAVD,MAUO;AACL,UAAI,KAAKkM,aAAL,GAAqB9mB,SAArB,CAA+BC,QAA/B,CAAwC6I,iBAAxC,CAAJ,EAA8D;AAC5D,aAAK+d,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;AACA;AACD;;AAED,WAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;AACD;AACF;;AAED7e,EAAAA,OAAO,GAAG;AACRsJ,IAAAA,YAAY,CAAC,KAAK6U,QAAN,CAAZ;AAEAjiB,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAL,CAAc0B,OAAd,CAAuB,IAAGkc,gBAAiB,EAA3C,CAAjB,EAAgE,eAAhE,EAAiF,KAAKuB,iBAAtF;;AAEA,QAAI,KAAKV,GAAL,IAAY,KAAKA,GAAL,CAAStrB,UAAzB,EAAqC;AACnC,WAAKsrB,GAAL,CAAStrB,UAAT,CAAoByO,WAApB,CAAgC,KAAK6c,GAArC;AACD;;AAED,QAAI,KAAK7N,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAakB,OAAb;AACD;;AAED,UAAM3R,OAAN;AACD;;AAEDwN,EAAAA,IAAI,GAAG;AACL,QAAI,KAAK3N,QAAL,CAAclI,KAAd,CAAoBG,OAApB,KAAgC,MAApC,EAA4C;AAC1C,YAAM,IAAIyI,KAAJ,CAAU,qCAAV,CAAN;AACD;;AAED,QAAI,EAAE,KAAK0e,aAAL,MAAwB,KAAKf,UAA/B,CAAJ,EAAgD;AAC9C;AACD;;AAED,UAAMlN,SAAS,GAAG9U,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKD,WAAL,CAAiB/J,KAAjB,CAAuBonB,IAA3D,CAAlB;AACA,UAAMiC,UAAU,GAAG7mB,cAAc,CAAC,KAAKwH,QAAN,CAAjC;AACA,UAAMsf,UAAU,GAAGD,UAAU,KAAK,IAAf,GACjB,KAAKrf,QAAL,CAAcuf,aAAd,CAA4BltB,eAA5B,CAA4CgG,QAA5C,CAAqD,KAAK2H,QAA1D,CADiB,GAEjBqf,UAAU,CAAChnB,QAAX,CAAoB,KAAK2H,QAAzB,CAFF;;AAIA,QAAImR,SAAS,CAACjS,gBAAV,IAA8B,CAACogB,UAAnC,EAA+C;AAC7C;AACD;;AAED,UAAMb,GAAG,GAAG,KAAKS,aAAL,EAAZ;AACA,UAAMM,KAAK,GAAGlrB,MAAM,CAAC,KAAKyL,WAAL,CAAiBnG,IAAlB,CAApB;AAEA6kB,IAAAA,GAAG,CAACpc,YAAJ,CAAiB,IAAjB,EAAuBmd,KAAvB;;AACA,SAAKxf,QAAL,CAAcqC,YAAd,CAA2B,kBAA3B,EAA+Cmd,KAA/C;;AAEA,SAAKC,UAAL;;AAEA,QAAI,KAAKnY,OAAL,CAAa8U,SAAjB,EAA4B;AAC1BqC,MAAAA,GAAG,CAACrmB,SAAJ,CAAcwR,GAAd,CAAkB3I,iBAAlB;AACD;;AAED,UAAMwR,SAAS,GAAG,OAAO,KAAKnL,OAAL,CAAamL,SAApB,KAAkC,UAAlC,GAChB,KAAKnL,OAAL,CAAamL,SAAb,CAAuB/f,IAAvB,CAA4B,IAA5B,EAAkC+rB,GAAlC,EAAuC,KAAKze,QAA5C,CADgB,GAEhB,KAAKsH,OAAL,CAAamL,SAFf;;AAIA,UAAMiN,UAAU,GAAG,KAAKC,cAAL,CAAoBlN,SAApB,CAAnB;;AACA,SAAKmN,mBAAL,CAAyBF,UAAzB;;AAEA,UAAM;AAAE5R,MAAAA;AAAF,QAAgB,KAAKxG,OAA3B;AACArH,IAAAA,IAAI,CAAC5F,GAAL,CAASokB,GAAT,EAAc,KAAK1e,WAAL,CAAiBG,QAA/B,EAAyC,IAAzC;;AAEA,QAAI,CAAC,KAAKF,QAAL,CAAcuf,aAAd,CAA4BltB,eAA5B,CAA4CgG,QAA5C,CAAqD,KAAKomB,GAA1D,CAAL,EAAqE;AACnE3Q,MAAAA,SAAS,CAACsH,WAAV,CAAsBqJ,GAAtB;AACApiB,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKD,WAAL,CAAiB/J,KAAjB,CAAuBsnB,QAA3D;AACD;;AAED,QAAI,KAAK1M,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAamB,MAAb;AACD,KAFD,MAEO;AACL,WAAKnB,OAAL,GAAeQ,MAAM,CAACO,YAAP,CAAoB,KAAK3R,QAAzB,EAAmCye,GAAnC,EAAwC,KAAKnN,gBAAL,CAAsBoO,UAAtB,CAAxC,CAAf;AACD;;AAEDjB,IAAAA,GAAG,CAACrmB,SAAJ,CAAcwR,GAAd,CAAkB1I,iBAAlB;AAEA,UAAMwb,WAAW,GAAG,OAAO,KAAKpV,OAAL,CAAaoV,WAApB,KAAoC,UAApC,GAAiD,KAAKpV,OAAL,CAAaoV,WAAb,EAAjD,GAA8E,KAAKpV,OAAL,CAAaoV,WAA/G;;AACA,QAAIA,WAAJ,EAAiB;AACf+B,MAAAA,GAAG,CAACrmB,SAAJ,CAAcwR,GAAd,CAAkB,GAAG8S,WAAW,CAACznB,KAAZ,CAAkB,GAAlB,CAArB;AACD,KAzDI;AA4DL;AACA;AACA;;;AACA,QAAI,kBAAkB7C,QAAQ,CAACC,eAA/B,EAAgD;AAC9C,SAAGC,MAAH,CAAU,GAAGF,QAAQ,CAAC8G,IAAT,CAAcrG,QAA3B,EAAqCuE,OAArC,CAA6CjF,OAAO,IAAI;AACtDkK,QAAAA,YAAY,CAACiC,EAAb,CAAgBnM,OAAhB,EAAyB,WAAzB,EAAsC0G,IAAtC;AACD,OAFD;AAGD;;AAED,UAAMyV,QAAQ,GAAG,MAAM;AACrB,YAAMuR,cAAc,GAAG,KAAKtB,WAA5B;AAEA,WAAKA,WAAL,GAAmB,IAAnB;AACAliB,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKD,WAAL,CAAiB/J,KAAjB,CAAuBqnB,KAA3D;;AAEA,UAAIwC,cAAc,KAAK/B,eAAvB,EAAwC;AACtC,aAAKmB,MAAL,CAAY,IAAZ,EAAkB,IAAlB;AACD;AACF,KATD;;AAWA,UAAMze,UAAU,GAAG,KAAKie,GAAL,CAASrmB,SAAT,CAAmBC,QAAnB,CAA4B4I,iBAA5B,CAAnB;;AACA,SAAKV,cAAL,CAAoB+N,QAApB,EAA8B,KAAKmQ,GAAnC,EAAwCje,UAAxC;AACD;;AAEDkN,EAAAA,IAAI,GAAG;AACL,QAAI,CAAC,KAAKkD,OAAV,EAAmB;AACjB;AACD;;AAED,UAAM6N,GAAG,GAAG,KAAKS,aAAL,EAAZ;;AACA,UAAM5Q,QAAQ,GAAG,MAAM;AACrB,UAAI,KAAKyQ,oBAAL,EAAJ,EAAiC;AAC/B;AACD;;AAED,UAAI,KAAKR,WAAL,KAAqBV,gBAArB,IAAyCY,GAAG,CAACtrB,UAAjD,EAA6D;AAC3DsrB,QAAAA,GAAG,CAACtrB,UAAJ,CAAeyO,WAAf,CAA2B6c,GAA3B;AACD;;AAED,WAAKqB,cAAL;;AACA,WAAK9f,QAAL,CAAc8C,eAAd,CAA8B,kBAA9B;;AACAzG,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKD,WAAL,CAAiB/J,KAAjB,CAAuBmnB,MAA3D;;AAEA,UAAI,KAAKvM,OAAT,EAAkB;AAChB,aAAKA,OAAL,CAAakB,OAAb;;AACA,aAAKlB,OAAL,GAAe,IAAf;AACD;AACF,KAjBD;;AAmBA,UAAMoB,SAAS,GAAG3V,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKD,WAAL,CAAiB/J,KAAjB,CAAuBknB,IAA3D,CAAlB;;AACA,QAAIlL,SAAS,CAAC9S,gBAAd,EAAgC;AAC9B;AACD;;AAEDuf,IAAAA,GAAG,CAACrmB,SAAJ,CAAc4C,MAAd,CAAqBkG,iBAArB,EA9BK;AAiCL;;AACA,QAAI,kBAAkB9O,QAAQ,CAACC,eAA/B,EAAgD;AAC9C,SAAGC,MAAH,CAAU,GAAGF,QAAQ,CAAC8G,IAAT,CAAcrG,QAA3B,EACGuE,OADH,CACWjF,OAAO,IAAIkK,YAAY,CAACC,GAAb,CAAiBnK,OAAjB,EAA0B,WAA1B,EAAuC0G,IAAvC,CADtB;AAED;;AAED,SAAK2lB,cAAL,CAAoBN,aAApB,IAAqC,KAArC;AACA,SAAKM,cAAL,CAAoBP,aAApB,IAAqC,KAArC;AACA,SAAKO,cAAL,CAAoBR,aAApB,IAAqC,KAArC;AAEA,UAAMxd,UAAU,GAAG,KAAKie,GAAL,CAASrmB,SAAT,CAAmBC,QAAnB,CAA4B4I,iBAA5B,CAAnB;;AACA,SAAKV,cAAL,CAAoB+N,QAApB,EAA8B,KAAKmQ,GAAnC,EAAwCje,UAAxC;;AACA,SAAK+d,WAAL,GAAmB,EAAnB;AACD;;AAEDxM,EAAAA,MAAM,GAAG;AACP,QAAI,KAAKnB,OAAL,KAAiB,IAArB,EAA2B;AACzB,WAAKA,OAAL,CAAamB,MAAb;AACD;AACF,GAvOiC;;;AA2OlCqN,EAAAA,aAAa,GAAG;AACd,WAAOnhB,OAAO,CAAC,KAAK8hB,QAAL,EAAD,CAAd;AACD;;AAEDb,EAAAA,aAAa,GAAG;AACd,QAAI,KAAKT,GAAT,EAAc;AACZ,aAAO,KAAKA,GAAZ;AACD;;AAED,UAAMtsB,OAAO,GAAGC,QAAQ,CAAC8iB,aAAT,CAAuB,KAAvB,CAAhB;AACA/iB,IAAAA,OAAO,CAAC6pB,SAAR,GAAoB,KAAK1U,OAAL,CAAa+U,QAAjC;AAEA,SAAKoC,GAAL,GAAWtsB,OAAO,CAACU,QAAR,CAAiB,CAAjB,CAAX;AACA,WAAO,KAAK4rB,GAAZ;AACD;;AAEDgB,EAAAA,UAAU,GAAG;AACX,UAAMhB,GAAG,GAAG,KAAKS,aAAL,EAAZ;AACA,SAAKc,iBAAL,CAAuBhuB,cAAc,CAACW,OAAf,CAAuBorB,sBAAvB,EAA+CU,GAA/C,CAAvB,EAA4E,KAAKsB,QAAL,EAA5E;AACAtB,IAAAA,GAAG,CAACrmB,SAAJ,CAAc4C,MAAd,CAAqBiG,iBAArB,EAAsCC,iBAAtC;AACD;;AAED8e,EAAAA,iBAAiB,CAAC7tB,OAAD,EAAU8tB,OAAV,EAAmB;AAClC,QAAI9tB,OAAO,KAAK,IAAhB,EAAsB;AACpB;AACD;;AAED,QAAI8D,SAAS,CAACgqB,OAAD,CAAb,EAAwB;AACtBA,MAAAA,OAAO,GAAG9pB,UAAU,CAAC8pB,OAAD,CAApB,CADsB;;AAItB,UAAI,KAAK3Y,OAAL,CAAakV,IAAjB,EAAuB;AACrB,YAAIyD,OAAO,CAAC9sB,UAAR,KAAuBhB,OAA3B,EAAoC;AAClCA,UAAAA,OAAO,CAAC6pB,SAAR,GAAoB,EAApB;AACA7pB,UAAAA,OAAO,CAACijB,WAAR,CAAoB6K,OAApB;AACD;AACF,OALD,MAKO;AACL9tB,QAAAA,OAAO,CAAC+tB,WAAR,GAAsBD,OAAO,CAACC,WAA9B;AACD;;AAED;AACD;;AAED,QAAI,KAAK5Y,OAAL,CAAakV,IAAjB,EAAuB;AACrB,UAAI,KAAKlV,OAAL,CAAaqV,QAAjB,EAA2B;AACzBsD,QAAAA,OAAO,GAAG9E,YAAY,CAAC8E,OAAD,EAAU,KAAK3Y,OAAL,CAAa+T,SAAvB,EAAkC,KAAK/T,OAAL,CAAagU,UAA/C,CAAtB;AACD;;AAEDnpB,MAAAA,OAAO,CAAC6pB,SAAR,GAAoBiE,OAApB;AACD,KAND,MAMO;AACL9tB,MAAAA,OAAO,CAAC+tB,WAAR,GAAsBD,OAAtB;AACD;AACF;;AAEDF,EAAAA,QAAQ,GAAG;AACT,QAAIzD,KAAK,GAAG,KAAKtc,QAAL,CAAcnL,YAAd,CAA2B,wBAA3B,CAAZ;;AAEA,QAAI,CAACynB,KAAL,EAAY;AACVA,MAAAA,KAAK,GAAG,OAAO,KAAKhV,OAAL,CAAagV,KAApB,KAA8B,UAA9B,GACN,KAAKhV,OAAL,CAAagV,KAAb,CAAmB5pB,IAAnB,CAAwB,KAAKsN,QAA7B,CADM,GAEN,KAAKsH,OAAL,CAAagV,KAFf;AAGD;;AAED,WAAOA,KAAP;AACD;;AAED6D,EAAAA,gBAAgB,CAACT,UAAD,EAAa;AAC3B,QAAIA,UAAU,KAAK,OAAnB,EAA4B;AAC1B,aAAO,KAAP;AACD;;AAED,QAAIA,UAAU,KAAK,MAAnB,EAA2B;AACzB,aAAO,OAAP;AACD;;AAED,WAAOA,UAAP;AACD,GAvTiC;;;AA2TlCZ,EAAAA,4BAA4B,CAAC5iB,KAAD,EAAQ8W,OAAR,EAAiB;AAC3C,UAAMoN,OAAO,GAAG,KAAKrgB,WAAL,CAAiBG,QAAjC;AACA8S,IAAAA,OAAO,GAAGA,OAAO,IAAI/S,IAAI,CAACvF,GAAL,CAASwB,KAAK,CAACC,cAAf,EAA+BikB,OAA/B,CAArB;;AAEA,QAAI,CAACpN,OAAL,EAAc;AACZA,MAAAA,OAAO,GAAG,IAAI,KAAKjT,WAAT,CAAqB7D,KAAK,CAACC,cAA3B,EAA2C,KAAKkkB,kBAAL,EAA3C,CAAV;AACApgB,MAAAA,IAAI,CAAC5F,GAAL,CAAS6B,KAAK,CAACC,cAAf,EAA+BikB,OAA/B,EAAwCpN,OAAxC;AACD;;AAED,WAAOA,OAAP;AACD;;AAEDX,EAAAA,UAAU,GAAG;AACX,UAAM;AAAEhP,MAAAA;AAAF,QAAa,KAAKiE,OAAxB;;AAEA,QAAI,OAAOjE,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,aAAOA,MAAM,CAACpO,KAAP,CAAa,GAAb,EAAkBqd,GAAlB,CAAsB9P,GAAG,IAAI7M,MAAM,CAACmV,QAAP,CAAgBtI,GAAhB,EAAqB,EAArB,CAA7B,CAAP;AACD;;AAED,QAAI,OAAOa,MAAP,KAAkB,UAAtB,EAAkC;AAChC,aAAOkP,UAAU,IAAIlP,MAAM,CAACkP,UAAD,EAAa,KAAKvS,QAAlB,CAA3B;AACD;;AAED,WAAOqD,MAAP;AACD;;AAEDiO,EAAAA,gBAAgB,CAACoO,UAAD,EAAa;AAC3B,UAAMlN,qBAAqB,GAAG;AAC5BC,MAAAA,SAAS,EAAEiN,UADiB;AAE5BlO,MAAAA,SAAS,EAAE,CACT;AACE7X,QAAAA,IAAI,EAAE,MADR;AAEE+Y,QAAAA,OAAO,EAAE;AACP+J,UAAAA,kBAAkB,EAAE,KAAKnV,OAAL,CAAamV;AAD1B;AAFX,OADS,EAOT;AACE9iB,QAAAA,IAAI,EAAE,QADR;AAEE+Y,QAAAA,OAAO,EAAE;AACPrP,UAAAA,MAAM,EAAE,KAAKgP,UAAL;AADD;AAFX,OAPS,EAaT;AACE1Y,QAAAA,IAAI,EAAE,iBADR;AAEE+Y,QAAAA,OAAO,EAAE;AACPnC,UAAAA,QAAQ,EAAE,KAAKjJ,OAAL,CAAaiJ;AADhB;AAFX,OAbS,EAmBT;AACE5W,QAAAA,IAAI,EAAE,OADR;AAEE+Y,QAAAA,OAAO,EAAE;AACPvgB,UAAAA,OAAO,EAAG,IAAG,KAAK4N,WAAL,CAAiBnG,IAAK;AAD5B;AAFX,OAnBS,EAyBT;AACED,QAAAA,IAAI,EAAE,UADR;AAEE+X,QAAAA,OAAO,EAAE,IAFX;AAGE4O,QAAAA,KAAK,EAAE,YAHT;AAIExmB,QAAAA,EAAE,EAAEgI,IAAI,IAAI,KAAKye,4BAAL,CAAkCze,IAAlC;AAJd,OAzBS,CAFiB;AAkC5B0e,MAAAA,aAAa,EAAE1e,IAAI,IAAI;AACrB,YAAIA,IAAI,CAAC4Q,OAAL,CAAaD,SAAb,KAA2B3Q,IAAI,CAAC2Q,SAApC,EAA+C;AAC7C,eAAK8N,4BAAL,CAAkCze,IAAlC;AACD;AACF;AAtC2B,KAA9B;AAyCA,WAAO,EACL,GAAG0Q,qBADE;AAEL,UAAI,OAAO,KAAKlL,OAAL,CAAamJ,YAApB,KAAqC,UAArC,GAAkD,KAAKnJ,OAAL,CAAamJ,YAAb,CAA0B+B,qBAA1B,CAAlD,GAAqG,KAAKlL,OAAL,CAAamJ,YAAtH;AAFK,KAAP;AAID;;AAEDmP,EAAAA,mBAAmB,CAACF,UAAD,EAAa;AAC9B,SAAKR,aAAL,GAAqB9mB,SAArB,CAA+BwR,GAA/B,CAAoC,GAAEqS,cAAa,IAAG,KAAKkE,gBAAL,CAAsBT,UAAtB,CAAkC,EAAxF;AACD;;AAEDC,EAAAA,cAAc,CAAClN,SAAD,EAAY;AACxB,WAAOmK,aAAa,CAACnK,SAAS,CAAC7a,WAAV,EAAD,CAApB;AACD;;AAED8mB,EAAAA,aAAa,GAAG;AACd,UAAM+B,QAAQ,GAAG,KAAKnZ,OAAL,CAAazI,OAAb,CAAqB5J,KAArB,CAA2B,GAA3B,CAAjB;;AAEAwrB,IAAAA,QAAQ,CAACrpB,OAAT,CAAiByH,OAAO,IAAI;AAC1B,UAAIA,OAAO,KAAK,OAAhB,EAAyB;AACvBxC,QAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+B,KAAKD,WAAL,CAAiB/J,KAAjB,CAAuBunB,KAAtD,EAA6D,KAAKjW,OAAL,CAAapV,QAA1E,EAAoFgK,KAAK,IAAI,KAAKkG,MAAL,CAAYlG,KAAZ,CAA7F;AACD,OAFD,MAEO,IAAI2C,OAAO,KAAKsf,cAAhB,EAAgC;AACrC,cAAMuC,OAAO,GAAG7hB,OAAO,KAAKmf,aAAZ,GACd,KAAKje,WAAL,CAAiB/J,KAAjB,CAAuB0nB,UADT,GAEd,KAAK3d,WAAL,CAAiB/J,KAAjB,CAAuBwnB,OAFzB;AAGA,cAAMmD,QAAQ,GAAG9hB,OAAO,KAAKmf,aAAZ,GACf,KAAKje,WAAL,CAAiB/J,KAAjB,CAAuB2nB,UADR,GAEf,KAAK5d,WAAL,CAAiB/J,KAAjB,CAAuBynB,QAFzB;AAIAphB,QAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+B0gB,OAA/B,EAAwC,KAAKpZ,OAAL,CAAapV,QAArD,EAA+DgK,KAAK,IAAI,KAAK8iB,MAAL,CAAY9iB,KAAZ,CAAxE;AACAG,QAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+B2gB,QAA/B,EAAyC,KAAKrZ,OAAL,CAAapV,QAAtD,EAAgEgK,KAAK,IAAI,KAAK+iB,MAAL,CAAY/iB,KAAZ,CAAzE;AACD;AACF,KAdD;;AAgBA,SAAKijB,iBAAL,GAAyB,MAAM;AAC7B,UAAI,KAAKnf,QAAT,EAAmB;AACjB,aAAK0N,IAAL;AACD;AACF,KAJD;;AAMArR,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAAL,CAAc0B,OAAd,CAAuB,IAAGkc,gBAAiB,EAA3C,CAAhB,EAA+D,eAA/D,EAAgF,KAAKuB,iBAArF;;AAEA,QAAI,KAAK7X,OAAL,CAAapV,QAAjB,EAA2B;AACzB,WAAKoV,OAAL,GAAe,EACb,GAAG,KAAKA,OADK;AAEbzI,QAAAA,OAAO,EAAE,QAFI;AAGb3M,QAAAA,QAAQ,EAAE;AAHG,OAAf;AAKD,KAND,MAMO;AACL,WAAK0uB,SAAL;AACD;AACF;;AAEDA,EAAAA,SAAS,GAAG;AACV,UAAMtE,KAAK,GAAG,KAAKtc,QAAL,CAAcnL,YAAd,CAA2B,OAA3B,CAAd;;AACA,UAAMgsB,iBAAiB,GAAG,OAAO,KAAK7gB,QAAL,CAAcnL,YAAd,CAA2B,wBAA3B,CAAjC;;AAEA,QAAIynB,KAAK,IAAIuE,iBAAiB,KAAK,QAAnC,EAA6C;AAC3C,WAAK7gB,QAAL,CAAcqC,YAAd,CAA2B,wBAA3B,EAAqDia,KAAK,IAAI,EAA9D;;AACA,UAAIA,KAAK,IAAI,CAAC,KAAKtc,QAAL,CAAcnL,YAAd,CAA2B,YAA3B,CAAV,IAAsD,CAAC,KAAKmL,QAAL,CAAckgB,WAAzE,EAAsF;AACpF,aAAKlgB,QAAL,CAAcqC,YAAd,CAA2B,YAA3B,EAAyCia,KAAzC;AACD;;AAED,WAAKtc,QAAL,CAAcqC,YAAd,CAA2B,OAA3B,EAAoC,EAApC;AACD;AACF;;AAED2c,EAAAA,MAAM,CAAC9iB,KAAD,EAAQ8W,OAAR,EAAiB;AACrBA,IAAAA,OAAO,GAAG,KAAK8L,4BAAL,CAAkC5iB,KAAlC,EAAyC8W,OAAzC,CAAV;;AAEA,QAAI9W,KAAJ,EAAW;AACT8W,MAAAA,OAAO,CAACwL,cAAR,CACEtiB,KAAK,CAACK,IAAN,KAAe,SAAf,GAA2B0hB,aAA3B,GAA2CD,aAD7C,IAEI,IAFJ;AAGD;;AAED,QAAIhL,OAAO,CAACkM,aAAR,GAAwB9mB,SAAxB,CAAkCC,QAAlC,CAA2C6I,iBAA3C,KAA+D8R,OAAO,CAACuL,WAAR,KAAwBV,gBAA3F,EAA6G;AAC3G7K,MAAAA,OAAO,CAACuL,WAAR,GAAsBV,gBAAtB;AACA;AACD;;AAEDpU,IAAAA,YAAY,CAACuJ,OAAO,CAACsL,QAAT,CAAZ;AAEAtL,IAAAA,OAAO,CAACuL,WAAR,GAAsBV,gBAAtB;;AAEA,QAAI,CAAC7K,OAAO,CAAC1L,OAAR,CAAgBiV,KAAjB,IAA0B,CAACvJ,OAAO,CAAC1L,OAAR,CAAgBiV,KAAhB,CAAsB5O,IAArD,EAA2D;AACzDqF,MAAAA,OAAO,CAACrF,IAAR;AACA;AACD;;AAEDqF,IAAAA,OAAO,CAACsL,QAAR,GAAmBznB,UAAU,CAAC,MAAM;AAClC,UAAImc,OAAO,CAACuL,WAAR,KAAwBV,gBAA5B,EAA8C;AAC5C7K,QAAAA,OAAO,CAACrF,IAAR;AACD;AACF,KAJ4B,EAI1BqF,OAAO,CAAC1L,OAAR,CAAgBiV,KAAhB,CAAsB5O,IAJI,CAA7B;AAKD;;AAEDsR,EAAAA,MAAM,CAAC/iB,KAAD,EAAQ8W,OAAR,EAAiB;AACrBA,IAAAA,OAAO,GAAG,KAAK8L,4BAAL,CAAkC5iB,KAAlC,EAAyC8W,OAAzC,CAAV;;AAEA,QAAI9W,KAAJ,EAAW;AACT8W,MAAAA,OAAO,CAACwL,cAAR,CACEtiB,KAAK,CAACK,IAAN,KAAe,UAAf,GAA4B0hB,aAA5B,GAA4CD,aAD9C,IAEIhL,OAAO,CAAChT,QAAR,CAAiB3H,QAAjB,CAA0B6D,KAAK,CAAC0B,aAAhC,CAFJ;AAGD;;AAED,QAAIoV,OAAO,CAAC+L,oBAAR,EAAJ,EAAoC;AAClC;AACD;;AAEDtV,IAAAA,YAAY,CAACuJ,OAAO,CAACsL,QAAT,CAAZ;AAEAtL,IAAAA,OAAO,CAACuL,WAAR,GAAsBT,eAAtB;;AAEA,QAAI,CAAC9K,OAAO,CAAC1L,OAAR,CAAgBiV,KAAjB,IAA0B,CAACvJ,OAAO,CAAC1L,OAAR,CAAgBiV,KAAhB,CAAsB7O,IAArD,EAA2D;AACzDsF,MAAAA,OAAO,CAACtF,IAAR;AACA;AACD;;AAEDsF,IAAAA,OAAO,CAACsL,QAAR,GAAmBznB,UAAU,CAAC,MAAM;AAClC,UAAImc,OAAO,CAACuL,WAAR,KAAwBT,eAA5B,EAA6C;AAC3C9K,QAAAA,OAAO,CAACtF,IAAR;AACD;AACF,KAJ4B,EAI1BsF,OAAO,CAAC1L,OAAR,CAAgBiV,KAAhB,CAAsB7O,IAJI,CAA7B;AAKD;;AAEDqR,EAAAA,oBAAoB,GAAG;AACrB,SAAK,MAAMlgB,OAAX,IAAsB,KAAK2f,cAA3B,EAA2C;AACzC,UAAI,KAAKA,cAAL,CAAoB3f,OAApB,CAAJ,EAAkC;AAChC,eAAO,IAAP;AACD;AACF;;AAED,WAAO,KAAP;AACD;;AAED0I,EAAAA,UAAU,CAACvQ,MAAD,EAAS;AACjB,UAAM8pB,cAAc,GAAGne,WAAW,CAACI,iBAAZ,CAA8B,KAAK/C,QAAnC,CAAvB;AAEA9I,IAAAA,MAAM,CAACC,IAAP,CAAY2pB,cAAZ,EAA4B1pB,OAA5B,CAAoC2pB,QAAQ,IAAI;AAC9C,UAAI5E,qBAAqB,CAAC3hB,GAAtB,CAA0BumB,QAA1B,CAAJ,EAAyC;AACvC,eAAOD,cAAc,CAACC,QAAD,CAArB;AACD;AACF,KAJD;AAMA/pB,IAAAA,MAAM,GAAG,EACP,GAAG,KAAK+I,WAAL,CAAiBoE,OADb;AAEP,SAAG2c,cAFI;AAGP,UAAI,OAAO9pB,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;AAHO,KAAT;AAMAA,IAAAA,MAAM,CAAC8W,SAAP,GAAmB9W,MAAM,CAAC8W,SAAP,KAAqB,KAArB,GAA6B1b,QAAQ,CAAC8G,IAAtC,GAA6C/C,UAAU,CAACa,MAAM,CAAC8W,SAAR,CAA1E;;AAEA,QAAI,OAAO9W,MAAM,CAACulB,KAAd,KAAwB,QAA5B,EAAsC;AACpCvlB,MAAAA,MAAM,CAACulB,KAAP,GAAe;AACb5O,QAAAA,IAAI,EAAE3W,MAAM,CAACulB,KADA;AAEb7O,QAAAA,IAAI,EAAE1W,MAAM,CAACulB;AAFA,OAAf;AAID;;AAED,QAAI,OAAOvlB,MAAM,CAACslB,KAAd,KAAwB,QAA5B,EAAsC;AACpCtlB,MAAAA,MAAM,CAACslB,KAAP,GAAetlB,MAAM,CAACslB,KAAP,CAAanoB,QAAb,EAAf;AACD;;AAED,QAAI,OAAO6C,MAAM,CAACipB,OAAd,KAA0B,QAA9B,EAAwC;AACtCjpB,MAAAA,MAAM,CAACipB,OAAP,GAAiBjpB,MAAM,CAACipB,OAAP,CAAe9rB,QAAf,EAAjB;AACD;;AAED2C,IAAAA,eAAe,CAAC8C,MAAD,EAAO5C,MAAP,EAAe,KAAK+I,WAAL,CAAiB2E,WAAhC,CAAf;;AAEA,QAAI1N,MAAM,CAAC2lB,QAAX,EAAqB;AACnB3lB,MAAAA,MAAM,CAACqlB,QAAP,GAAkBlB,YAAY,CAACnkB,MAAM,CAACqlB,QAAR,EAAkBrlB,MAAM,CAACqkB,SAAzB,EAAoCrkB,MAAM,CAACskB,UAA3C,CAA9B;AACD;;AAED,WAAOtkB,MAAP;AACD;;AAEDqpB,EAAAA,kBAAkB,GAAG;AACnB,UAAMrpB,MAAM,GAAG,EAAf;;AAEA,QAAI,KAAKsQ,OAAT,EAAkB;AAChB,WAAK,MAAMhN,GAAX,IAAkB,KAAKgN,OAAvB,EAAgC;AAC9B,YAAI,KAAKvH,WAAL,CAAiBoE,OAAjB,CAAyB7J,GAAzB,MAAkC,KAAKgN,OAAL,CAAahN,GAAb,CAAtC,EAAyD;AACvDtD,UAAAA,MAAM,CAACsD,GAAD,CAAN,GAAc,KAAKgN,OAAL,CAAahN,GAAb,CAAd;AACD;AACF;AACF;;AAED,WAAOtD,MAAP;AACD;;AAED8oB,EAAAA,cAAc,GAAG;AACf,UAAMrB,GAAG,GAAG,KAAKS,aAAL,EAAZ;AACA,UAAM8B,QAAQ,GAAGvC,GAAG,CAAC5pB,YAAJ,CAAiB,OAAjB,EAA0BT,KAA1B,CAAgC8nB,oBAAhC,CAAjB;;AACA,QAAI8E,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAC5qB,MAAT,GAAkB,CAA3C,EAA8C;AAC5C4qB,MAAAA,QAAQ,CAAC1O,GAAT,CAAa2O,KAAK,IAAIA,KAAK,CAAC/rB,IAAN,EAAtB,EACGkC,OADH,CACW8pB,MAAM,IAAIzC,GAAG,CAACrmB,SAAJ,CAAc4C,MAAd,CAAqBkmB,MAArB,CADrB;AAED;AACF;;AAEDX,EAAAA,4BAA4B,CAAChO,UAAD,EAAa;AACvC,UAAM;AAAE4O,MAAAA;AAAF,QAAY5O,UAAlB;;AAEA,QAAI,CAAC4O,KAAL,EAAY;AACV;AACD;;AAED,SAAK1C,GAAL,GAAW0C,KAAK,CAACvF,QAAN,CAAewF,MAA1B;;AACA,SAAKtB,cAAL;;AACA,SAAKF,mBAAL,CAAyB,KAAKD,cAAL,CAAoBwB,KAAK,CAAC1O,SAA1B,CAAzB;AACD,GAhlBiC;;;AAolBZ,SAAf1Y,eAAe,CAAC/C,MAAD,EAAS;AAC7B,WAAO,KAAK6K,IAAL,CAAU,YAAY;AAC3B,UAAIC,IAAI,GAAG7B,IAAI,CAACvF,GAAL,CAAS,IAAT,EAAewF,UAAf,CAAX;;AACA,YAAMoH,OAAO,GAAG,OAAOtQ,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;AAEA,UAAI,CAAC8K,IAAD,IAAS,eAAepK,IAAf,CAAoBV,MAApB,CAAb,EAA0C;AACxC;AACD;;AAED,UAAI,CAAC8K,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIsc,OAAJ,CAAY,IAAZ,EAAkB9W,OAAlB,CAAP;AACD;;AAED,UAAI,OAAOtQ,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAO8K,IAAI,CAAC9K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED8K,QAAAA,IAAI,CAAC9K,MAAD,CAAJ;AACD;AACF,KAnBM,CAAP;AAoBD;;AAzmBiC;AA4mBpC;AACA;AACA;AACA;AACA;AACA;;;AAEAwC,kBAAkB,CAAC4kB,OAAD,CAAlB;;ACjvBA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;;AAEA,MAAMxkB,MAAI,GAAG,SAAb;AACA,MAAMsG,UAAQ,GAAG,YAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAM+b,YAAY,GAAG,YAArB;AACA,MAAMC,kBAAkB,GAAG,IAAIzkB,MAAJ,CAAY,UAASwkB,YAAa,MAAlC,EAAyC,GAAzC,CAA3B;AAEA,MAAM9X,SAAO,GAAG,EACd,GAAGia,OAAO,CAACja,OADG;AAEdsO,EAAAA,SAAS,EAAE,OAFG;AAGdpP,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAHM;AAIdxE,EAAAA,OAAO,EAAE,OAJK;AAKdohB,EAAAA,OAAO,EAAE,EALK;AAMd5D,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEI,kCAFJ,GAGE,kCAHF,GAIA;AAVI,CAAhB;AAaA,MAAM3X,aAAW,GAAG,EAClB,GAAG0Z,OAAO,CAAC1Z,WADO;AAElBub,EAAAA,OAAO,EAAE;AAFS,CAApB;AAKA,MAAMjqB,OAAK,GAAG;AACZknB,EAAAA,IAAI,EAAG,OAAM9c,WAAU,EADX;AAEZ+c,EAAAA,MAAM,EAAG,SAAQ/c,WAAU,EAFf;AAGZgd,EAAAA,IAAI,EAAG,OAAMhd,WAAU,EAHX;AAIZid,EAAAA,KAAK,EAAG,QAAOjd,WAAU,EAJb;AAKZkd,EAAAA,QAAQ,EAAG,WAAUld,WAAU,EALnB;AAMZmd,EAAAA,KAAK,EAAG,QAAOnd,WAAU,EANb;AAOZod,EAAAA,OAAO,EAAG,UAASpd,WAAU,EAPjB;AAQZqd,EAAAA,QAAQ,EAAG,WAAUrd,WAAU,EARnB;AASZsd,EAAAA,UAAU,EAAG,aAAYtd,WAAU,EATvB;AAUZud,EAAAA,UAAU,EAAG,aAAYvd,WAAU;AAVvB,CAAd;AAaA,MAAMa,iBAAe,GAAG,MAAxB;AACA,MAAMC,iBAAe,GAAG,MAAxB;AAEA,MAAMmgB,cAAc,GAAG,iBAAvB;AACA,MAAMC,gBAAgB,GAAG,eAAzB;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,OAAN,SAAsBnD,OAAtB,CAA8B;AAC5B;AAEkB,aAAPja,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD;;AAEc,aAAJvK,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD;;AAEe,aAAL5D,KAAK,GAAG;AACjB,WAAOA,OAAP;AACD;;AAEqB,aAAX0O,WAAW,GAAG;AACvB,WAAOA,aAAP;AACD,GAjB2B;;;AAqB5B0a,EAAAA,aAAa,GAAG;AACd,WAAO,KAAKW,QAAL,MAAmB,KAAKyB,WAAL,EAA1B;AACD;;AAED/B,EAAAA,UAAU,GAAG;AACX,UAAMhB,GAAG,GAAG,KAAKS,aAAL,EAAZ,CADW;;AAIX,SAAKc,iBAAL,CAAuBhuB,cAAc,CAACW,OAAf,CAAuB0uB,cAAvB,EAAuC5C,GAAvC,CAAvB,EAAoE,KAAKsB,QAAL,EAApE;;AACA,QAAIE,OAAO,GAAG,KAAKuB,WAAL,EAAd;;AACA,QAAI,OAAOvB,OAAP,KAAmB,UAAvB,EAAmC;AACjCA,MAAAA,OAAO,GAAGA,OAAO,CAACvtB,IAAR,CAAa,KAAKsN,QAAlB,CAAV;AACD;;AAED,SAAKggB,iBAAL,CAAuBhuB,cAAc,CAACW,OAAf,CAAuB2uB,gBAAvB,EAAyC7C,GAAzC,CAAvB,EAAsEwB,OAAtE;AAEAxB,IAAAA,GAAG,CAACrmB,SAAJ,CAAc4C,MAAd,CAAqBiG,iBAArB,EAAsCC,iBAAtC;AACD,GAtC2B;;;AA0C5B0e,EAAAA,mBAAmB,CAACF,UAAD,EAAa;AAC9B,SAAKR,aAAL,GAAqB9mB,SAArB,CAA+BwR,GAA/B,CAAoC,GAAEqS,YAAa,IAAG,KAAKkE,gBAAL,CAAsBT,UAAtB,CAAkC,EAAxF;AACD;;AAED8B,EAAAA,WAAW,GAAG;AACZ,WAAO,KAAKxhB,QAAL,CAAcnL,YAAd,CAA2B,iBAA3B,KAAiD,KAAKyS,OAAL,CAAa2Y,OAArE;AACD;;AAEDH,EAAAA,cAAc,GAAG;AACf,UAAMrB,GAAG,GAAG,KAAKS,aAAL,EAAZ;AACA,UAAM8B,QAAQ,GAAGvC,GAAG,CAAC5pB,YAAJ,CAAiB,OAAjB,EAA0BT,KAA1B,CAAgC8nB,kBAAhC,CAAjB;;AACA,QAAI8E,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAC5qB,MAAT,GAAkB,CAA3C,EAA8C;AAC5C4qB,MAAAA,QAAQ,CAAC1O,GAAT,CAAa2O,KAAK,IAAIA,KAAK,CAAC/rB,IAAN,EAAtB,EACGkC,OADH,CACW8pB,MAAM,IAAIzC,GAAG,CAACrmB,SAAJ,CAAc4C,MAAd,CAAqBkmB,MAArB,CADrB;AAED;AACF,GAzD2B;;;AA6DN,SAAfnnB,eAAe,CAAC/C,MAAD,EAAS;AAC7B,WAAO,KAAK6K,IAAL,CAAU,YAAY;AAC3B,UAAIC,IAAI,GAAG7B,IAAI,CAACvF,GAAL,CAAS,IAAT,EAAewF,UAAf,CAAX;;AACA,YAAMoH,OAAO,GAAG,OAAOtQ,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;AAEA,UAAI,CAAC8K,IAAD,IAAS,eAAepK,IAAf,CAAoBV,MAApB,CAAb,EAA0C;AACxC;AACD;;AAED,UAAI,CAAC8K,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIyf,OAAJ,CAAY,IAAZ,EAAkBja,OAAlB,CAAP;AACArH,QAAAA,IAAI,CAAC5F,GAAL,CAAS,IAAT,EAAe6F,UAAf,EAAyB4B,IAAzB;AACD;;AAED,UAAI,OAAO9K,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAO8K,IAAI,CAAC9K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED8K,QAAAA,IAAI,CAAC9K,MAAD,CAAJ;AACD;AACF,KApBM,CAAP;AAqBD;;AAnF2B;AAsF9B;AACA;AACA;AACA;AACA;AACA;;;AAEAwC,kBAAkB,CAAC+nB,OAAD,CAAlB;;AChKA;AACA;AACA;AACA;AACA;AACA;AAcA;AACA;AACA;AACA;AACA;;AAEA,MAAM3nB,MAAI,GAAG,WAAb;AACA,MAAMsG,UAAQ,GAAG,cAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMS,cAAY,GAAG,WAArB;AAEA,MAAMwD,SAAO,GAAG;AACdd,EAAAA,MAAM,EAAE,EADM;AAEdoe,EAAAA,MAAM,EAAE,MAFM;AAGd9kB,EAAAA,MAAM,EAAE;AAHM,CAAhB;AAMA,MAAM+H,aAAW,GAAG;AAClBrB,EAAAA,MAAM,EAAE,QADU;AAElBoe,EAAAA,MAAM,EAAE,QAFU;AAGlB9kB,EAAAA,MAAM,EAAE;AAHU,CAApB;AAMA,MAAM+kB,cAAc,GAAI,WAAUthB,WAAU,EAA5C;AACA,MAAMuhB,YAAY,GAAI,SAAQvhB,WAAU,EAAxC;AACA,MAAMsF,mBAAmB,GAAI,OAAMtF,WAAU,GAAEO,cAAa,EAA5D;AAEA,MAAMihB,wBAAwB,GAAG,eAAjC;AACA,MAAM3f,mBAAiB,GAAG,QAA1B;AAEA,MAAM4f,iBAAiB,GAAG,wBAA1B;AACA,MAAMC,yBAAuB,GAAG,mBAAhC;AACA,MAAMC,kBAAkB,GAAG,WAA3B;AACA,MAAMC,kBAAkB,GAAG,WAA3B;AACA,MAAMC,mBAAmB,GAAG,kBAA5B;AACA,MAAMC,mBAAiB,GAAG,WAA1B;AACA,MAAMC,0BAAwB,GAAG,kBAAjC;AAEA,MAAMC,aAAa,GAAG,QAAtB;AACA,MAAMC,eAAe,GAAG,UAAxB;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,SAAN,SAAwBxiB,aAAxB,CAAsC;AACpCC,EAAAA,WAAW,CAAC5N,OAAD,EAAU6E,MAAV,EAAkB;AAC3B,UAAM7E,OAAN;AACA,SAAKowB,cAAL,GAAsB,KAAKviB,QAAL,CAAc6J,OAAd,KAA0B,MAA1B,GAAmCrU,MAAnC,GAA4C,KAAKwK,QAAvE;AACA,SAAKsH,OAAL,GAAe,KAAKC,UAAL,CAAgBvQ,MAAhB,CAAf;AACA,SAAKsW,SAAL,GAAkB,GAAE,KAAKhG,OAAL,CAAa3K,MAAO,IAAGolB,kBAAmB,KAAI,KAAKza,OAAL,CAAa3K,MAAO,IAAGslB,mBAAoB,KAAI,KAAK3a,OAAL,CAAa3K,MAAO,KAAIilB,wBAAyB,EAAlK;AACA,SAAKY,QAAL,GAAgB,EAAhB;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKC,aAAL,GAAqB,IAArB;AACA,SAAKC,aAAL,GAAqB,CAArB;AAEAtmB,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKikB,cAArB,EAAqCZ,YAArC,EAAmD,MAAM,KAAKiB,QAAL,EAAzD;AAEA,SAAKC,OAAL;;AACA,SAAKD,QAAL;AACD,GAfmC;;;AAmBlB,aAAPze,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD;;AAEc,aAAJvK,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GAzBmC;;;AA6BpCipB,EAAAA,OAAO,GAAG;AACR,UAAMC,UAAU,GAAG,KAAKP,cAAL,KAAwB,KAAKA,cAAL,CAAoB/sB,MAA5C,GACjB4sB,aADiB,GAEjBC,eAFF;AAIA,UAAMU,YAAY,GAAG,KAAKzb,OAAL,CAAama,MAAb,KAAwB,MAAxB,GACnBqB,UADmB,GAEnB,KAAKxb,OAAL,CAAama,MAFf;AAIA,UAAMuB,UAAU,GAAGD,YAAY,KAAKV,eAAjB,GACjB,KAAKY,aAAL,EADiB,GAEjB,CAFF;AAIA,SAAKT,QAAL,GAAgB,EAAhB;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKE,aAAL,GAAqB,KAAKO,gBAAL,EAArB;AAEA,UAAMC,OAAO,GAAGnxB,cAAc,CAACC,IAAf,CAAoB,KAAKqb,SAAzB,CAAhB;AAEA6V,IAAAA,OAAO,CAAC7Q,GAAR,CAAYngB,OAAO,IAAI;AACrB,YAAMixB,cAAc,GAAGjuB,sBAAsB,CAAChD,OAAD,CAA7C;AACA,YAAMwK,MAAM,GAAGymB,cAAc,GAAGpxB,cAAc,CAACW,OAAf,CAAuBywB,cAAvB,CAAH,GAA4C,IAAzE;;AAEA,UAAIzmB,MAAJ,EAAY;AACV,cAAM0mB,SAAS,GAAG1mB,MAAM,CAAC4G,qBAAP,EAAlB;;AACA,YAAI8f,SAAS,CAACvP,KAAV,IAAmBuP,SAAS,CAACC,MAAjC,EAAyC;AACvC,iBAAO,CACL3gB,WAAW,CAACogB,YAAD,CAAX,CAA0BpmB,MAA1B,EAAkC6G,GAAlC,GAAwCwf,UADnC,EAELI,cAFK,CAAP;AAID;AACF;;AAED,aAAO,IAAP;AACD,KAfD,EAgBGtwB,MAhBH,CAgBUywB,IAAI,IAAIA,IAhBlB,EAiBGC,IAjBH,CAiBQ,CAACjK,CAAD,EAAIE,CAAJ,KAAUF,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAC,CAAC,CAAD,CAjB1B,EAkBGriB,OAlBH,CAkBWmsB,IAAI,IAAI;AACf,WAAKf,QAAL,CAAcjvB,IAAd,CAAmBgwB,IAAI,CAAC,CAAD,CAAvB;;AACA,WAAKd,QAAL,CAAclvB,IAAd,CAAmBgwB,IAAI,CAAC,CAAD,CAAvB;AACD,KArBH;AAsBD;;AAEDpjB,EAAAA,OAAO,GAAG;AACR9D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKimB,cAAtB,EAAsCniB,WAAtC;AACA,UAAMD,OAAN;AACD,GA3EmC;;;AA+EpCoH,EAAAA,UAAU,CAACvQ,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmN,SADI;AAEP,SAAGxB,WAAW,CAACI,iBAAZ,CAA8B,KAAK/C,QAAnC,CAFI;AAGP,UAAI,OAAOhJ,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;AAHO,KAAT;;AAMA,QAAI,OAAOA,MAAM,CAAC2F,MAAd,KAAyB,QAAzB,IAAqC1G,SAAS,CAACe,MAAM,CAAC2F,MAAR,CAAlD,EAAmE;AACjE,UAAI;AAAEsQ,QAAAA;AAAF,UAASjW,MAAM,CAAC2F,MAApB;;AACA,UAAI,CAACsQ,EAAL,EAAS;AACPA,QAAAA,EAAE,GAAG3Y,MAAM,CAACsF,MAAD,CAAX;AACA5C,QAAAA,MAAM,CAAC2F,MAAP,CAAcsQ,EAAd,GAAmBA,EAAnB;AACD;;AAEDjW,MAAAA,MAAM,CAAC2F,MAAP,GAAiB,IAAGsQ,EAAG,EAAvB;AACD;;AAEDnW,IAAAA,eAAe,CAAC8C,MAAD,EAAO5C,MAAP,EAAe0N,aAAf,CAAf;AAEA,WAAO1N,MAAP;AACD;;AAEDisB,EAAAA,aAAa,GAAG;AACd,WAAO,KAAKV,cAAL,KAAwB/sB,MAAxB,GACL,KAAK+sB,cAAL,CAAoBkB,WADf,GAEL,KAAKlB,cAAL,CAAoB9e,SAFtB;AAGD;;AAEDyf,EAAAA,gBAAgB,GAAG;AACjB,WAAO,KAAKX,cAAL,CAAoB7K,YAApB,IAAoCljB,IAAI,CAACkvB,GAAL,CACzCtxB,QAAQ,CAAC8G,IAAT,CAAcwe,YAD2B,EAEzCtlB,QAAQ,CAACC,eAAT,CAAyBqlB,YAFgB,CAA3C;AAID;;AAEDiM,EAAAA,gBAAgB,GAAG;AACjB,WAAO,KAAKpB,cAAL,KAAwB/sB,MAAxB,GACLA,MAAM,CAACouB,WADF,GAEL,KAAKrB,cAAL,CAAoBhf,qBAApB,GAA4C+f,MAF9C;AAGD;;AAEDV,EAAAA,QAAQ,GAAG;AACT,UAAMnf,SAAS,GAAG,KAAKwf,aAAL,KAAuB,KAAK3b,OAAL,CAAajE,MAAtD;;AACA,UAAMqU,YAAY,GAAG,KAAKwL,gBAAL,EAArB;;AACA,UAAMW,SAAS,GAAG,KAAKvc,OAAL,CAAajE,MAAb,GAAsBqU,YAAtB,GAAqC,KAAKiM,gBAAL,EAAvD;;AAEA,QAAI,KAAKhB,aAAL,KAAuBjL,YAA3B,EAAyC;AACvC,WAAKmL,OAAL;AACD;;AAED,QAAIpf,SAAS,IAAIogB,SAAjB,EAA4B;AAC1B,YAAMlnB,MAAM,GAAG,KAAK8lB,QAAL,CAAc,KAAKA,QAAL,CAAcrsB,MAAd,GAAuB,CAArC,CAAf;;AAEA,UAAI,KAAKssB,aAAL,KAAuB/lB,MAA3B,EAAmC;AACjC,aAAKmnB,SAAL,CAAennB,MAAf;AACD;;AAED;AACD;;AAED,QAAI,KAAK+lB,aAAL,IAAsBjf,SAAS,GAAG,KAAK+e,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;AAC9E,WAAKE,aAAL,GAAqB,IAArB;;AACA,WAAKqB,MAAL;;AACA;AACD;;AAED,SAAK,IAAInnB,CAAC,GAAG,KAAK4lB,QAAL,CAAcpsB,MAA3B,EAAmCwG,CAAC,EAApC,GAAyC;AACvC,YAAMonB,cAAc,GAAG,KAAKtB,aAAL,KAAuB,KAAKD,QAAL,CAAc7lB,CAAd,CAAvB,IACnB6G,SAAS,IAAI,KAAK+e,QAAL,CAAc5lB,CAAd,CADM,KAElB,OAAO,KAAK4lB,QAAL,CAAc5lB,CAAC,GAAG,CAAlB,CAAP,KAAgC,WAAhC,IAA+C6G,SAAS,GAAG,KAAK+e,QAAL,CAAc5lB,CAAC,GAAG,CAAlB,CAFzC,CAAvB;;AAIA,UAAIonB,cAAJ,EAAoB;AAClB,aAAKF,SAAL,CAAe,KAAKrB,QAAL,CAAc7lB,CAAd,CAAf;AACD;AACF;AACF;;AAEDknB,EAAAA,SAAS,CAACnnB,MAAD,EAAS;AAChB,SAAK+lB,aAAL,GAAqB/lB,MAArB;;AAEA,SAAKonB,MAAL;;AAEA,UAAME,OAAO,GAAG,KAAK3W,SAAL,CAAerY,KAAf,CAAqB,GAArB,EACbqd,GADa,CACTpgB,QAAQ,IAAK,GAAEA,QAAS,oBAAmByK,MAAO,MAAKzK,QAAS,UAASyK,MAAO,IADvE,CAAhB;;AAGA,UAAMunB,IAAI,GAAGlyB,cAAc,CAACW,OAAf,CAAuBsxB,OAAO,CAACE,IAAR,CAAa,GAAb,CAAvB,CAAb;;AAEA,QAAID,IAAI,CAAC9rB,SAAL,CAAeC,QAAf,CAAwBupB,wBAAxB,CAAJ,EAAuD;AACrD5vB,MAAAA,cAAc,CAACW,OAAf,CAAuBwvB,0BAAvB,EAAiD+B,IAAI,CAACxiB,OAAL,CAAawgB,mBAAb,CAAjD,EACG9pB,SADH,CACawR,GADb,CACiB3H,mBADjB;AAGAiiB,MAAAA,IAAI,CAAC9rB,SAAL,CAAewR,GAAf,CAAmB3H,mBAAnB;AACD,KALD,MAKO;AACL;AACAiiB,MAAAA,IAAI,CAAC9rB,SAAL,CAAewR,GAAf,CAAmB3H,mBAAnB;AAEAjQ,MAAAA,cAAc,CAACiB,OAAf,CAAuBixB,IAAvB,EAA6BpC,yBAA7B,EACG1qB,OADH,CACWgtB,SAAS,IAAI;AACpB;AACA;AACApyB,QAAAA,cAAc,CAACwB,IAAf,CAAoB4wB,SAApB,EAAgC,GAAErC,kBAAmB,KAAIE,mBAAoB,EAA7E,EACG7qB,OADH,CACWmsB,IAAI,IAAIA,IAAI,CAACnrB,SAAL,CAAewR,GAAf,CAAmB3H,mBAAnB,CADnB,EAHoB;;AAOpBjQ,QAAAA,cAAc,CAACwB,IAAf,CAAoB4wB,SAApB,EAA+BpC,kBAA/B,EACG5qB,OADH,CACWitB,OAAO,IAAI;AAClBryB,UAAAA,cAAc,CAACa,QAAf,CAAwBwxB,OAAxB,EAAiCtC,kBAAjC,EACG3qB,OADH,CACWmsB,IAAI,IAAIA,IAAI,CAACnrB,SAAL,CAAewR,GAAf,CAAmB3H,mBAAnB,CADnB;AAED,SAJH;AAKD,OAbH;AAcD;;AAED5F,IAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAK0jB,cAA1B,EAA0Cb,cAA1C,EAA0D;AACxD9jB,MAAAA,aAAa,EAAEjB;AADyC,KAA1D;AAGD;;AAEDonB,EAAAA,MAAM,GAAG;AACP/xB,IAAAA,cAAc,CAACC,IAAf,CAAoB,KAAKqb,SAAzB,EACGxa,MADH,CACUwxB,IAAI,IAAIA,IAAI,CAAClsB,SAAL,CAAeC,QAAf,CAAwB4J,mBAAxB,CADlB,EAEG7K,OAFH,CAEWktB,IAAI,IAAIA,IAAI,CAAClsB,SAAL,CAAe4C,MAAf,CAAsBiH,mBAAtB,CAFnB;AAGD,GAxMmC;;;AA4Md,SAAflI,eAAe,CAAC/C,MAAD,EAAS;AAC7B,WAAO,KAAK6K,IAAL,CAAU,YAAY;AAC3B,YAAMC,IAAI,GAAGwgB,SAAS,CAAC7hB,WAAV,CAAsB,IAAtB,KAA+B,IAAI6hB,SAAJ,CAAc,IAAd,EAAoB,OAAOtrB,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1D,CAA5C;;AAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B;AACD;;AAED,UAAI,OAAO8K,IAAI,CAAC9K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED8K,MAAAA,IAAI,CAAC9K,MAAD,CAAJ;AACD,KAZM,CAAP;AAaD;;AA1NmC;AA6NtC;AACA;AACA;AACA;AACA;;;AAEAqF,YAAY,CAACiC,EAAb,CAAgB9I,MAAhB,EAAwBkQ,mBAAxB,EAA6C,MAAM;AACjD1T,EAAAA,cAAc,CAACC,IAAf,CAAoB4vB,iBAApB,EACGzqB,OADH,CACWmtB,GAAG,IAAI,IAAIjC,SAAJ,CAAciC,GAAd,CADlB;AAED,CAHD;AAKA;AACA;AACA;AACA;AACA;AACA;;AAEA/qB,kBAAkB,CAAC8oB,SAAD,CAAlB;;ACjTA;AACA;AACA;AACA;AACA;AACA;AAaA;AACA;AACA;AACA;AACA;;AAEA,MAAM1oB,MAAI,GAAG,KAAb;AACA,MAAMsG,UAAQ,GAAG,QAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMS,YAAY,GAAG,WAArB;AAEA,MAAM2L,YAAU,GAAI,OAAMlM,WAAU,EAApC;AACA,MAAMmM,cAAY,GAAI,SAAQnM,WAAU,EAAxC;AACA,MAAMgM,YAAU,GAAI,OAAMhM,WAAU,EAApC;AACA,MAAMiM,aAAW,GAAI,QAAOjM,WAAU,EAAtC;AACA,MAAMW,oBAAoB,GAAI,QAAOX,WAAU,GAAEO,YAAa,EAA9D;AAEA,MAAM6jB,wBAAwB,GAAG,eAAjC;AACA,MAAMviB,iBAAiB,GAAG,QAA1B;AACA,MAAMhB,iBAAe,GAAG,MAAxB;AACA,MAAMC,iBAAe,GAAG,MAAxB;AAEA,MAAMghB,iBAAiB,GAAG,WAA1B;AACA,MAAMJ,uBAAuB,GAAG,mBAAhC;AACA,MAAM5b,eAAe,GAAG,SAAxB;AACA,MAAMue,kBAAkB,GAAG,uBAA3B;AACA,MAAMviB,oBAAoB,GAAG,0EAA7B;AACA,MAAMigB,wBAAwB,GAAG,kBAAjC;AACA,MAAMuC,8BAA8B,GAAG,iCAAvC;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,GAAN,SAAkB7kB,aAAlB,CAAgC;AAC9B;AAEe,aAAJlG,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GAL6B;;;AAS9B+T,EAAAA,IAAI,GAAG;AACL,QAAK,KAAK3N,QAAL,CAAc7M,UAAd,IACH,KAAK6M,QAAL,CAAc7M,UAAd,CAAyBC,QAAzB,KAAsCC,IAAI,CAACC,YADxC,IAEH,KAAK0M,QAAL,CAAc5H,SAAd,CAAwBC,QAAxB,CAAiC4J,iBAAjC,CAFF,EAEwD;AACtD;AACD;;AAED,QAAIxO,QAAJ;AACA,UAAMkJ,MAAM,GAAGvH,sBAAsB,CAAC,KAAK4K,QAAN,CAArC;;AACA,UAAM4kB,WAAW,GAAG,KAAK5kB,QAAL,CAAc0B,OAAd,CAAsBogB,uBAAtB,CAApB;;AAEA,QAAI8C,WAAJ,EAAiB;AACf,YAAMC,YAAY,GAAGD,WAAW,CAAC1L,QAAZ,KAAyB,IAAzB,IAAiC0L,WAAW,CAAC1L,QAAZ,KAAyB,IAA1D,GAAiEuL,kBAAjE,GAAsFve,eAA3G;AACAzS,MAAAA,QAAQ,GAAGzB,cAAc,CAACC,IAAf,CAAoB4yB,YAApB,EAAkCD,WAAlC,CAAX;AACAnxB,MAAAA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,CAAC2C,MAAT,GAAkB,CAAnB,CAAnB;AACD;;AAED,UAAM4b,SAAS,GAAGve,QAAQ,GACxB4I,YAAY,CAACwC,OAAb,CAAqBpL,QAArB,EAA+B6Y,YAA/B,EAA2C;AACzC1O,MAAAA,aAAa,EAAE,KAAKoC;AADqB,KAA3C,CADwB,GAIxB,IAJF;AAMA,UAAMmR,SAAS,GAAG9U,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoM,YAApC,EAAgD;AAChExO,MAAAA,aAAa,EAAEnK;AADiD,KAAhD,CAAlB;;AAIA,QAAI0d,SAAS,CAACjS,gBAAV,IAA+B8S,SAAS,KAAK,IAAd,IAAsBA,SAAS,CAAC9S,gBAAnE,EAAsF;AACpF;AACD;;AAED,SAAK4kB,SAAL,CAAe,KAAK9jB,QAApB,EAA8B4kB,WAA9B;;AAEA,UAAMtW,QAAQ,GAAG,MAAM;AACrBjS,MAAAA,YAAY,CAACwC,OAAb,CAAqBpL,QAArB,EAA+B8Y,cAA/B,EAA6C;AAC3C3O,QAAAA,aAAa,EAAE,KAAKoC;AADuB,OAA7C;AAGA3D,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqM,aAApC,EAAiD;AAC/CzO,QAAAA,aAAa,EAAEnK;AADgC,OAAjD;AAGD,KAPD;;AASA,QAAIkJ,MAAJ,EAAY;AACV,WAAKmnB,SAAL,CAAennB,MAAf,EAAuBA,MAAM,CAACxJ,UAA9B,EAA0Cmb,QAA1C;AACD,KAFD,MAEO;AACLA,MAAAA,QAAQ;AACT;AACF,GAxD6B;;;AA4D9BwV,EAAAA,SAAS,CAAC3xB,OAAD,EAAU2b,SAAV,EAAqB1U,QAArB,EAA+B;AACtC,UAAM0rB,cAAc,GAAGhX,SAAS,KAAKA,SAAS,CAACoL,QAAV,KAAuB,IAAvB,IAA+BpL,SAAS,CAACoL,QAAV,KAAuB,IAA3D,CAAT,GACrBlnB,cAAc,CAACC,IAAf,CAAoBwyB,kBAApB,EAAwC3W,SAAxC,CADqB,GAErB9b,cAAc,CAACa,QAAf,CAAwBib,SAAxB,EAAmC5H,eAAnC,CAFF;AAIA,UAAM6e,MAAM,GAAGD,cAAc,CAAC,CAAD,CAA7B;AACA,UAAMpW,eAAe,GAAGtV,QAAQ,IAAK2rB,MAAM,IAAIA,MAAM,CAAC3sB,SAAP,CAAiBC,QAAjB,CAA0B4I,iBAA1B,CAA/C;;AAEA,UAAMqN,QAAQ,GAAG,MAAM,KAAK0W,mBAAL,CAAyB7yB,OAAzB,EAAkC4yB,MAAlC,EAA0C3rB,QAA1C,CAAvB;;AAEA,QAAI2rB,MAAM,IAAIrW,eAAd,EAA+B;AAC7BqW,MAAAA,MAAM,CAAC3sB,SAAP,CAAiB4C,MAAjB,CAAwBkG,iBAAxB;;AACA,WAAKX,cAAL,CAAoB+N,QAApB,EAA8Bnc,OAA9B,EAAuC,IAAvC;AACD,KAHD,MAGO;AACLmc,MAAAA,QAAQ;AACT;AACF;;AAED0W,EAAAA,mBAAmB,CAAC7yB,OAAD,EAAU4yB,MAAV,EAAkB3rB,QAAlB,EAA4B;AAC7C,QAAI2rB,MAAJ,EAAY;AACVA,MAAAA,MAAM,CAAC3sB,SAAP,CAAiB4C,MAAjB,CAAwBiH,iBAAxB;AAEA,YAAMgjB,aAAa,GAAGjzB,cAAc,CAACW,OAAf,CAAuB+xB,8BAAvB,EAAuDK,MAAM,CAAC5xB,UAA9D,CAAtB;;AAEA,UAAI8xB,aAAJ,EAAmB;AACjBA,QAAAA,aAAa,CAAC7sB,SAAd,CAAwB4C,MAAxB,CAA+BiH,iBAA/B;AACD;;AAED,UAAI8iB,MAAM,CAAClwB,YAAP,CAAoB,MAApB,MAAgC,KAApC,EAA2C;AACzCkwB,QAAAA,MAAM,CAAC1iB,YAAP,CAAoB,eAApB,EAAqC,KAArC;AACD;AACF;;AAEDlQ,IAAAA,OAAO,CAACiG,SAAR,CAAkBwR,GAAlB,CAAsB3H,iBAAtB;;AACA,QAAI9P,OAAO,CAAC0C,YAAR,CAAqB,MAArB,MAAiC,KAArC,EAA4C;AAC1C1C,MAAAA,OAAO,CAACkQ,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD;;AAEDvJ,IAAAA,MAAM,CAAC3G,OAAD,CAAN;;AAEA,QAAIA,OAAO,CAACiG,SAAR,CAAkBC,QAAlB,CAA2B4I,iBAA3B,CAAJ,EAAiD;AAC/C9O,MAAAA,OAAO,CAACiG,SAAR,CAAkBwR,GAAlB,CAAsB1I,iBAAtB;AACD;;AAED,QAAIiL,MAAM,GAAGha,OAAO,CAACgB,UAArB;;AACA,QAAIgZ,MAAM,IAAIA,MAAM,CAAC+M,QAAP,KAAoB,IAAlC,EAAwC;AACtC/M,MAAAA,MAAM,GAAGA,MAAM,CAAChZ,UAAhB;AACD;;AAED,QAAIgZ,MAAM,IAAIA,MAAM,CAAC/T,SAAP,CAAiBC,QAAjB,CAA0BmsB,wBAA1B,CAAd,EAAmE;AACjE,YAAMU,eAAe,GAAG/yB,OAAO,CAACuP,OAAR,CAAgBwgB,iBAAhB,CAAxB;;AAEA,UAAIgD,eAAJ,EAAqB;AACnBlzB,QAAAA,cAAc,CAACC,IAAf,CAAoBkwB,wBAApB,EAA8C+C,eAA9C,EACG9tB,OADH,CACW+tB,QAAQ,IAAIA,QAAQ,CAAC/sB,SAAT,CAAmBwR,GAAnB,CAAuB3H,iBAAvB,CADvB;AAED;;AAED9P,MAAAA,OAAO,CAACkQ,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD;;AAED,QAAIjJ,QAAJ,EAAc;AACZA,MAAAA,QAAQ;AACT;AACF,GA3H6B;;;AA+HR,SAAfW,eAAe,CAAC/C,MAAD,EAAS;AAC7B,WAAO,KAAK6K,IAAL,CAAU,YAAY;AAC3B,YAAMC,IAAI,GAAG7B,IAAI,CAACvF,GAAL,CAAS,IAAT,EAAewF,UAAf,KAA4B,IAAIykB,GAAJ,CAAQ,IAAR,CAAzC;;AAEA,UAAI,OAAO3tB,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAO8K,IAAI,CAAC9K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED8K,QAAAA,IAAI,CAAC9K,MAAD,CAAJ;AACD;AACF,KAVM,CAAP;AAWD;;AA3I6B;AA8IhC;AACA;AACA;AACA;AACA;;;AAEAqF,YAAY,CAACiC,EAAb,CAAgBlM,QAAhB,EAA0B2O,oBAA1B,EAAgDmB,oBAAhD,EAAsE,UAAUhG,KAAV,EAAiB;AACrF,MAAI,CAAC,GAAD,EAAM,MAAN,EAAcnH,QAAd,CAAuB,KAAK8U,OAA5B,CAAJ,EAA0C;AACxC3N,IAAAA,KAAK,CAAC0D,cAAN;AACD;;AAED,MAAIzH,UAAU,CAAC,IAAD,CAAd,EAAsB;AACpB;AACD;;AAED,QAAM2J,IAAI,GAAG7B,IAAI,CAACvF,GAAL,CAAS,IAAT,EAAewF,UAAf,KAA4B,IAAIykB,GAAJ,CAAQ,IAAR,CAAzC;AACA7iB,EAAAA,IAAI,CAAC6L,IAAL;AACD,CAXD;AAaA;AACA;AACA;AACA;AACA;AACA;;AAEAnU,kBAAkB,CAACmrB,GAAD,CAAlB;;AC9NA;AACA;AACA;AACA;AACA;AACA;AAYA;AACA;AACA;AACA;AACA;;AAEA,MAAM/qB,IAAI,GAAG,OAAb;AACA,MAAMsG,QAAQ,GAAG,UAAjB;AACA,MAAME,SAAS,GAAI,IAAGF,QAAS,EAA/B;AAEA,MAAMuV,mBAAmB,GAAI,gBAAerV,SAAU,EAAtD;AACA,MAAMglB,eAAe,GAAI,YAAWhlB,SAAU,EAA9C;AACA,MAAMilB,cAAc,GAAI,WAAUjlB,SAAU,EAA5C;AACA,MAAMmV,aAAa,GAAI,UAASnV,SAAU,EAA1C;AACA,MAAMklB,cAAc,GAAI,WAAUllB,SAAU,EAA5C;AACA,MAAMkM,UAAU,GAAI,OAAMlM,SAAU,EAApC;AACA,MAAMmM,YAAY,GAAI,SAAQnM,SAAU,EAAxC;AACA,MAAMgM,UAAU,GAAI,OAAMhM,SAAU,EAApC;AACA,MAAMiM,WAAW,GAAI,QAAOjM,SAAU,EAAtC;AAEA,MAAMa,eAAe,GAAG,MAAxB;AACA,MAAMskB,eAAe,GAAG,MAAxB;AACA,MAAMrkB,eAAe,GAAG,MAAxB;AACA,MAAMskB,kBAAkB,GAAG,SAA3B;AAEA,MAAM9gB,WAAW,GAAG;AAClB0X,EAAAA,SAAS,EAAE,SADO;AAElBqJ,EAAAA,QAAQ,EAAE,SAFQ;AAGlBlJ,EAAAA,KAAK,EAAE;AAHW,CAApB;AAMA,MAAMpY,OAAO,GAAG;AACdiY,EAAAA,SAAS,EAAE,IADG;AAEdqJ,EAAAA,QAAQ,EAAE,IAFI;AAGdlJ,EAAAA,KAAK,EAAE;AAHO,CAAhB;AAMA,MAAMtG,qBAAqB,GAAG,2BAA9B;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMyP,KAAN,SAAoB5lB,aAApB,CAAkC;AAChCC,EAAAA,WAAW,CAAC5N,OAAD,EAAU6E,MAAV,EAAkB;AAC3B,UAAM7E,OAAN;AAEA,SAAKmV,OAAL,GAAe,KAAKC,UAAL,CAAgBvQ,MAAhB,CAAf;AACA,SAAKsnB,QAAL,GAAgB,IAAhB;AACA,SAAKqH,oBAAL,GAA4B,KAA5B;AACA,SAAKC,uBAAL,GAA+B,KAA/B;;AACA,SAAKlH,aAAL;AACD,GAT+B;;;AAaV,aAAXha,WAAW,GAAG;AACvB,WAAOA,WAAP;AACD;;AAEiB,aAAPP,OAAO,GAAG;AACnB,WAAOA,OAAP;AACD;;AAEc,aAAJvK,IAAI,GAAG;AAChB,WAAOA,IAAP;AACD,GAvB+B;;;AA2BhC+T,EAAAA,IAAI,GAAG;AACL,UAAMwD,SAAS,GAAG9U,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoM,UAApC,CAAlB;;AAEA,QAAI+E,SAAS,CAACjS,gBAAd,EAAgC;AAC9B;AACD;;AAED,SAAK2mB,aAAL;;AAEA,QAAI,KAAKve,OAAL,CAAa8U,SAAjB,EAA4B;AAC1B,WAAKpc,QAAL,CAAc5H,SAAd,CAAwBwR,GAAxB,CAA4B3I,eAA5B;AACD;;AAED,UAAMqN,QAAQ,GAAG,MAAM;AACrB,WAAKtO,QAAL,CAAc5H,SAAd,CAAwB4C,MAAxB,CAA+BwqB,kBAA/B;;AACA,WAAKxlB,QAAL,CAAc5H,SAAd,CAAwBwR,GAAxB,CAA4B1I,eAA5B;;AAEA7E,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqM,WAApC;;AAEA,WAAKyZ,kBAAL;AACD,KAPD;;AASA,SAAK9lB,QAAL,CAAc5H,SAAd,CAAwB4C,MAAxB,CAA+BuqB,eAA/B;;AACAzsB,IAAAA,MAAM,CAAC,KAAKkH,QAAN,CAAN;;AACA,SAAKA,QAAL,CAAc5H,SAAd,CAAwBwR,GAAxB,CAA4B4b,kBAA5B;;AAEA,SAAKjlB,cAAL,CAAoB+N,QAApB,EAA8B,KAAKtO,QAAnC,EAA6C,KAAKsH,OAAL,CAAa8U,SAA1D;AACD;;AAED1O,EAAAA,IAAI,GAAG;AACL,QAAI,CAAC,KAAK1N,QAAL,CAAc5H,SAAd,CAAwBC,QAAxB,CAAiC6I,eAAjC,CAAL,EAAwD;AACtD;AACD;;AAED,UAAM8Q,SAAS,GAAG3V,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCsM,UAApC,CAAlB;;AAEA,QAAI0F,SAAS,CAAC9S,gBAAd,EAAgC;AAC9B;AACD;;AAED,UAAMoP,QAAQ,GAAG,MAAM;AACrB,WAAKtO,QAAL,CAAc5H,SAAd,CAAwBwR,GAAxB,CAA4B2b,eAA5B;;AACAlpB,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCuM,YAApC;AACD,KAHD;;AAKA,SAAKvM,QAAL,CAAc5H,SAAd,CAAwB4C,MAAxB,CAA+BkG,eAA/B;;AACA,SAAKX,cAAL,CAAoB+N,QAApB,EAA8B,KAAKtO,QAAnC,EAA6C,KAAKsH,OAAL,CAAa8U,SAA1D;AACD;;AAEDjc,EAAAA,OAAO,GAAG;AACR,SAAK0lB,aAAL;;AAEA,QAAI,KAAK7lB,QAAL,CAAc5H,SAAd,CAAwBC,QAAxB,CAAiC6I,eAAjC,CAAJ,EAAuD;AACrD,WAAKlB,QAAL,CAAc5H,SAAd,CAAwB4C,MAAxB,CAA+BkG,eAA/B;AACD;;AAED,UAAMf,OAAN;AACD,GApF+B;;;AAwFhCoH,EAAAA,UAAU,CAACvQ,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmN,OADI;AAEP,SAAGxB,WAAW,CAACI,iBAAZ,CAA8B,KAAK/C,QAAnC,CAFI;AAGP,UAAI,OAAOhJ,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;AAHO,KAAT;AAMAF,IAAAA,eAAe,CAAC8C,IAAD,EAAO5C,MAAP,EAAe,KAAK+I,WAAL,CAAiB2E,WAAhC,CAAf;AAEA,WAAO1N,MAAP;AACD;;AAED8uB,EAAAA,kBAAkB,GAAG;AACnB,QAAI,CAAC,KAAKxe,OAAL,CAAame,QAAlB,EAA4B;AAC1B;AACD;;AAED,QAAI,KAAKE,oBAAL,IAA6B,KAAKC,uBAAtC,EAA+D;AAC7D;AACD;;AAED,SAAKtH,QAAL,GAAgBznB,UAAU,CAAC,MAAM;AAC/B,WAAK6W,IAAL;AACD,KAFyB,EAEvB,KAAKpG,OAAL,CAAaiV,KAFU,CAA1B;AAGD;;AAEDwJ,EAAAA,cAAc,CAAC7pB,KAAD,EAAQ8pB,aAAR,EAAuB;AACnC,YAAQ9pB,KAAK,CAACK,IAAd;AACE,WAAK,WAAL;AACA,WAAK,UAAL;AACE,aAAKopB,oBAAL,GAA4BK,aAA5B;AACA;;AACF,WAAK,SAAL;AACA,WAAK,UAAL;AACE,aAAKJ,uBAAL,GAA+BI,aAA/B;AACA;AARJ;;AAaA,QAAIA,aAAJ,EAAmB;AACjB,WAAKH,aAAL;;AACA;AACD;;AAED,UAAMza,WAAW,GAAGlP,KAAK,CAAC0B,aAA1B;;AACA,QAAI,KAAKoC,QAAL,KAAkBoL,WAAlB,IAAiC,KAAKpL,QAAL,CAAc3H,QAAd,CAAuB+S,WAAvB,CAArC,EAA0E;AACxE;AACD;;AAED,SAAK0a,kBAAL;AACD;;AAEDpH,EAAAA,aAAa,GAAG;AACdriB,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+ByV,mBAA/B,EAAoDQ,qBAApD,EAA2E,MAAM,KAAKvI,IAAL,EAAjF;AACArR,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BolB,eAA/B,EAAgDlpB,KAAK,IAAI,KAAK6pB,cAAL,CAAoB7pB,KAApB,EAA2B,IAA3B,CAAzD;AACAG,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BqlB,cAA/B,EAA+CnpB,KAAK,IAAI,KAAK6pB,cAAL,CAAoB7pB,KAApB,EAA2B,KAA3B,CAAxD;AACAG,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BuV,aAA/B,EAA8CrZ,KAAK,IAAI,KAAK6pB,cAAL,CAAoB7pB,KAApB,EAA2B,IAA3B,CAAvD;AACAG,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BslB,cAA/B,EAA+CppB,KAAK,IAAI,KAAK6pB,cAAL,CAAoB7pB,KAApB,EAA2B,KAA3B,CAAxD;AACD;;AAED2pB,EAAAA,aAAa,GAAG;AACdpc,IAAAA,YAAY,CAAC,KAAK6U,QAAN,CAAZ;AACA,SAAKA,QAAL,GAAgB,IAAhB;AACD,GAxJ+B;;;AA4JV,SAAfvkB,eAAe,CAAC/C,MAAD,EAAS;AAC7B,WAAO,KAAK6K,IAAL,CAAU,YAAY;AAC3B,UAAIC,IAAI,GAAG7B,IAAI,CAACvF,GAAL,CAAS,IAAT,EAAewF,QAAf,CAAX;;AACA,YAAMoH,OAAO,GAAG,OAAOtQ,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;AAEA,UAAI,CAAC8K,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAI4jB,KAAJ,CAAU,IAAV,EAAgBpe,OAAhB,CAAP;AACD;;AAED,UAAI,OAAOtQ,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAO8K,IAAI,CAAC9K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED8K,QAAAA,IAAI,CAAC9K,MAAD,CAAJ,CAAa,IAAb;AACD;AACF,KAfM,CAAP;AAgBD;;AA7K+B;AAgLlC;AACA;AACA;AACA;AACA;AACA;;;AAEAwC,kBAAkB,CAACksB,KAAD,CAAlB;;;;"}