/**
 * QR Registration Notifier
 * Simple notification system for QR registration events
 */

class QRRegistrationNotifier {
    constructor() {
        this.init();
    }

    init() {
        // Listen for QR registration form submissions
        this.setupFormListener();
        
        // Check if we should show a notification (from URL params or localStorage)
        this.checkForPendingNotification();
    }

    setupFormListener() {
        // Listen for form submissions on QR registration pages
        document.addEventListener('DOMContentLoaded', () => {
            const forms = document.querySelectorAll('form[action*="register"], form[action*="qr"]');
            
            forms.forEach(form => {
                form.addEventListener('submit', (e) => {
                    this.handleFormSubmission(form, e);
                });
            });
        });
    }

    handleFormSubmission(form, event) {
        // Get form data
        const formData = new FormData(form);
        const studentData = {
            name: formData.get('name') || 'Unknown Student',
            email: formData.get('email') || 'No email provided',
            mobile: formData.get('mobile') || formData.get('phone') || 'No mobile provided',
            course: formData.get('course') || 'No course selected',
            registration_date: new Date().toLocaleDateString()
        };

        // Store notification data for after form submission
        localStorage.setItem('qr_registration_notification', JSON.stringify({
            studentData: studentData,
            timestamp: Date.now()
        }));

        // Show immediate notification if permissions are granted
        if (Notification.permission === 'granted') {
            setTimeout(() => {
                this.showQRRegistrationNotification(studentData);
            }, 1000); // Delay to allow form submission to complete
        }
    }

    checkForPendingNotification() {
        const pendingNotification = localStorage.getItem('qr_registration_notification');
        
        if (pendingNotification) {
            try {
                const data = JSON.parse(pendingNotification);
                
                // Only show if it's recent (within last 5 minutes)
                if (Date.now() - data.timestamp < 5 * 60 * 1000) {
                    this.showQRRegistrationNotification(data.studentData);
                }
                
                // Clear the stored notification
                localStorage.removeItem('qr_registration_notification');
            } catch (error) {
                console.error('Error parsing pending notification:', error);
                localStorage.removeItem('qr_registration_notification');
            }
        }
    }

    showQRRegistrationNotification(studentData) {
        // Show browser notification if permission granted
        if (Notification.permission === 'granted') {
            const notification = new Notification('📝 New QR Registration Received', {
                body: `New student registration:\n• Name: ${studentData.name}\n• Email: ${studentData.email}\n• Mobile: ${studentData.mobile}\n• Course: ${studentData.course}`,
                icon: '/static/images/notification-icon.png', // You can add an icon
                badge: '/static/images/badge-icon.png',
                tag: 'qr-registration',
                requireInteraction: true,
                actions: [
                    {
                        action: 'view',
                        title: 'View Registration'
                    },
                    {
                        action: 'dismiss',
                        title: 'Dismiss'
                    }
                ]
            });

            notification.onclick = () => {
                window.focus();
                // You can redirect to the temp students page
                // window.location.href = '/students/temp-students/';
                notification.close();
            };

            // Auto close after 10 seconds
            setTimeout(() => {
                notification.close();
            }, 10000);
        }

        // Also show custom in-app notification
        this.showCustomNotification(studentData);
        
        // Try to send to backend if available
        this.sendToBackend(studentData);
    }

    showCustomNotification(studentData) {
        // Create custom notification element
        const notification = document.createElement('div');
        notification.className = 'qr-notification-popup';
        notification.innerHTML = `
            <div class="qr-notification-content">
                <div class="qr-notification-header">
                    <span class="qr-notification-icon">📝</span>
                    <h3>New QR Registration</h3>
                    <button class="qr-notification-close" onclick="this.closest('.qr-notification-popup').remove()">×</button>
                </div>
                <div class="qr-notification-body">
                    <p><strong>Student Details:</strong></p>
                    <ul>
                        <li><strong>Name:</strong> ${studentData.name}</li>
                        <li><strong>Email:</strong> ${studentData.email}</li>
                        <li><strong>Mobile:</strong> ${studentData.mobile}</li>
                        <li><strong>Course:</strong> ${studentData.course}</li>
                        <li><strong>Date:</strong> ${studentData.registration_date}</li>
                    </ul>
                </div>
                <div class="qr-notification-actions">
                    <button class="btn-primary" onclick="window.open('/students/temp-students/', '_blank')">View All Registrations</button>
                    <button class="btn-secondary" onclick="this.closest('.qr-notification-popup').remove()">Dismiss</button>
                </div>
            </div>
        `;

        // Add styles if not present
        this.addNotificationStyles();

        // Add to page
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Auto remove after 15 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }
        }, 15000);

        // Play notification sound if available
        this.playNotificationSound();
    }

    addNotificationStyles() {
        if (document.getElementById('qr-notification-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'qr-notification-styles';
        styles.textContent = `
            .qr-notification-popup {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                width: 90%;
                background: white;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
                transform: translateX(100%);
                transition: transform 0.3s ease;
                border-left: 4px solid #007bff;
            }

            .qr-notification-popup.show {
                transform: translateX(0);
            }

            .qr-notification-content {
                padding: 0;
            }

            .qr-notification-header {
                display: flex;
                align-items: center;
                padding: 15px 20px;
                border-bottom: 1px solid #eee;
                background: #f8f9fa;
                border-radius: 12px 12px 0 0;
            }

            .qr-notification-icon {
                font-size: 24px;
                margin-right: 10px;
            }

            .qr-notification-header h3 {
                margin: 0;
                flex: 1;
                color: #333;
                font-size: 16px;
            }

            .qr-notification-close {
                background: none;
                border: none;
                font-size: 20px;
                color: #999;
                cursor: pointer;
                padding: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
            }

            .qr-notification-close:hover {
                background: #f0f0f0;
                color: #666;
            }

            .qr-notification-body {
                padding: 15px 20px;
            }

            .qr-notification-body p {
                margin: 0 0 10px 0;
                font-weight: 600;
                color: #333;
            }

            .qr-notification-body ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .qr-notification-body li {
                padding: 4px 0;
                color: #666;
                font-size: 14px;
            }

            .qr-notification-actions {
                padding: 15px 20px;
                border-top: 1px solid #eee;
                display: flex;
                gap: 10px;
                justify-content: flex-end;
            }

            .qr-notification-actions button {
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.2s ease;
            }

            .btn-primary {
                background: #007bff;
                color: white;
            }

            .btn-primary:hover {
                background: #0056b3;
            }

            .btn-secondary {
                background: #6c757d;
                color: white;
            }

            .btn-secondary:hover {
                background: #545b62;
            }

            @media (max-width: 480px) {
                .qr-notification-popup {
                    top: 10px;
                    right: 10px;
                    left: 10px;
                    width: auto;
                    max-width: none;
                }

                .qr-notification-actions {
                    flex-direction: column;
                }
            }
        `;
        
        document.head.appendChild(styles);
    }

    playNotificationSound() {
        try {
            // Create a simple notification sound
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (error) {
            console.log('Could not play notification sound:', error);
        }
    }

    async sendToBackend(studentData) {
        try {
            // Try to send notification to backend
            const response = await fetch('/librarian/qr-registration-notification/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken(),
                },
                body: JSON.stringify({
                    student_data: studentData
                })
            });

            if (response.ok) {
                console.log('QR registration notification sent to backend');
            }
        } catch (error) {
            console.log('Could not send to backend (expected in demo):', error);
        }
    }

    getCSRFToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
        return '';
    }

    // Public method to manually trigger notification (for testing)
    triggerTestNotification() {
        const testData = {
            name: 'Test Student',
            email: '<EMAIL>',
            mobile: '9876543210',
            course: 'Computer Science',
            registration_date: new Date().toLocaleDateString()
        };

        this.showQRRegistrationNotification(testData);
    }
}

// Initialize the QR registration notifier
const qrNotifier = new QRRegistrationNotifier();

// Add global function for easy testing
window.triggerQRNotification = () => qrNotifier.triggerTestNotification();
