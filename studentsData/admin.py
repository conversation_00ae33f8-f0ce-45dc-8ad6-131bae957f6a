from django.contrib import admin
from .models import *

admin.site.register(StudentData)
admin.site.register(Invoice)
admin.site.register(Payment)
admin.site.register(Shift)
admin.site.register(ShiftRate)
admin.site.register(Months)
admin.site.register(RegistrationFee)
admin.site.register(States)
admin.site.register(Courses)
admin.site.register(Seat)
admin.site.register(Booking)
admin.site.register(ShortenedURL)
admin.site.register(TempStudentData)

@admin.register(RegistrationFeedback)
class RegistrationFeedbackAdmin(admin.ModelAdmin):
    list_display = [
        'temp_student_name',
        'librarian',
        'ease_rating',
        'design_rating',
        'would_recommend',
        'submitted_at'
    ]
    list_filter = [
        'librarian',
        'ease_rating',
        'design_rating',
        'time_taken',
        'device_used',
        'would_recommend',
        'faced_issues',
        'qr_easy_to_scan',
        'submitted_at'
    ]
    search_fields = [
        'temp_student__name',
        'temp_student__email',
        'librarian__library_name',
        'suggestions',
        'issue_description'
    ]
    readonly_fields = [
        'temp_student',
        'librarian',
        'ip_address',
        'user_agent',
        'submitted_at'
    ]
    fieldsets = (
        ('Student Information', {
            'fields': ('temp_student', 'librarian', 'submitted_at')
        }),
        ('Feedback Ratings', {
            'fields': ('ease_rating', 'design_rating', 'would_recommend')
        }),
        ('Experience Details', {
            'fields': ('time_taken', 'device_used', 'faced_issues', 'issue_description')
        }),
        ('QR Code Feedback', {
            'fields': ('qr_easy_to_scan', 'qr_problem_description')
        }),
        ('Suggestions', {
            'fields': ('suggestions',)
        }),
        ('Technical Information', {
            'fields': ('ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
    )

    def temp_student_name(self, obj):
        return obj.temp_student.name if obj.temp_student else "Anonymous"
    temp_student_name.short_description = "Student Name"
    temp_student_name.admin_order_field = 'temp_student__name'
