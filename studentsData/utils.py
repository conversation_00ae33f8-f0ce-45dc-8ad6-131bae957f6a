from django.utils import timezone

from librarian.models import Li<PERSON>rian_param
from .models import Booking, <PERSON><PERSON>


def update_seat_availability(librarian):
    """
    Update the availability of seats based on expired bookings.
    """
    now = timezone.now()

    expired_bookings = Booking.objects.filter(
        expire_date__lt=now, student__librarian=librarian
    )

    for booking in expired_bookings:

        seat = booking.seat

        seat.is_available = True
        seat.save()

    return len(expired_bookings)
