{% extends "base.html" %}

{% block title %}Create Coupon - Librainian{% endblock %}

{% block page_title %}Create Coupon{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="/blogs/">Blogs</a></li>
<li class="breadcrumb-item"><a href="/blogs/coupons/">Coupons</a></li>
<li class="breadcrumb-item active" aria-current="page">Create Coupon</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Create Coupon Styles */
    .coupon-creation-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }

    .coupon-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 2rem;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .coupon-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .coupon-header h1 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 1;
    }

    .coupon-header p {
        font-size: 1.125rem;
        opacity: 0.9;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    .form-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: #6366f1;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        display: block;
    }

    .required-label::after {
        content: " *";
        color: #ef4444;
        font-size: 0.875rem;
        font-weight: 700;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(156, 163, 175, 0.3);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        width: 100%;
    }

    .form-control:focus {
        outline: none;
        border-color: #6366f1;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        background: rgba(255, 255, 255, 1);
    }

    .form-select {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(156, 163, 175, 0.3);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        width: 100%;
        cursor: pointer;
    }

    .form-select:focus {
        outline: none;
        border-color: #6366f1;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        background: rgba(255, 255, 255, 1);
    }

    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .input-group {
        position: relative;
    }

    .input-group-text {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6b7280;
        font-size: 1rem;
        z-index: 2;
        background: none;
        border: none;
        pointer-events: none;
    }

    .input-group .form-control {
        padding-left: 2.5rem;
    }

    .help-text {
        font-size: 0.875rem;
        color: #6b7280;
        margin-top: 0.25rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border: none;
        border-radius: 12px;
        padding: 0.875rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        justify-content: center;
        width: 100%;
        max-width: 300px;
        margin: 2rem auto 0;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        color: white;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
        margin-bottom: 2rem;
    }

    .btn-secondary:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
    }

    .discount-preview {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border-radius: 16px;
        padding: 1.5rem;
        color: white;
        margin-top: 1rem;
        text-align: center;
        opacity: 0;
        transition: all 0.3s ease;
    }

    .discount-preview.show {
        opacity: 1;
    }

    .preview-title {
        font-size: 1.125rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .preview-value {
        font-size: 2rem;
        font-weight: 700;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .coupon-creation-container,
        .form-section {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .coupon-header {
            padding: 1.5rem 1rem;
        }

        .coupon-header h1 {
            font-size: 1.5rem;
        }

        .form-row {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .btn-primary {
            max-width: none;
            width: 100%;
        }
    }

    /* Animation */
    .fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="coupon-creation-container fade-in">
    <!-- Header -->
    <div class="coupon-header">
        <h1><i class="fas fa-ticket-alt me-2"></i>Create New Coupon</h1>
        <p>Design promotional coupons and discount offers</p>
    </div>

    <!-- Back Button -->
    <a href="/blogs/coupons/" class="btn-secondary">
        <i class="fas fa-arrow-left"></i>
        Back to Coupons
    </a>

    <!-- Coupon Creation Form -->
    <form method="post" id="couponForm">
        {% csrf_token %}

        <!-- Basic Information Section -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-info-circle"></i>
                Basic Information
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="code" class="form-label required-label">Coupon Code</label>
                    <div class="input-group">
                        <i class="fas fa-tag input-group-text"></i>
                        <input type="text" name="code" id="code" class="form-control"
                               required maxlength="20" placeholder="e.g., SAVE20, WELCOME10"
                               style="text-transform: uppercase;">
                    </div>
                    <div class="help-text">Enter a unique coupon code (letters and numbers only)</div>
                </div>

                <div class="form-group">
                    <label for="description" class="form-label">Description</label>
                    <div class="input-group">
                        <i class="fas fa-align-left input-group-text"></i>
                        <input type="text" name="description" id="description" class="form-control"
                               maxlength="100" placeholder="Brief description of the offer">
                    </div>
                    <div class="help-text">Optional description for internal reference</div>
                </div>
            </div>
        </div>

        <!-- Discount Configuration Section -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-percentage"></i>
                Discount Configuration
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="discount_type" class="form-label required-label">Discount Type</label>
                    <select name="discount_type" id="discount_type" class="form-select" required>
                        <option value="">Select discount type...</option>
                        <option value="percentage">Percentage (%)</option>
                        <option value="amount">Fixed Amount (₹)</option>
                    </select>
                    <div class="help-text">Choose between percentage or fixed amount discount</div>
                </div>

                <div class="form-group">
                    <label for="discount" class="form-label required-label">Discount Value</label>
                    <div class="input-group">
                        <i class="fas fa-calculator input-group-text"></i>
                        <input type="number" name="discount" id="discount" class="form-control"
                               required min="1" step="0.01" placeholder="Enter discount value">
                    </div>
                    <div class="help-text">Enter the discount amount or percentage</div>
                </div>
            </div>

            <!-- Discount Preview -->
            <div class="discount-preview" id="discountPreview">
                <div class="preview-title">Discount Preview</div>
                <div class="preview-value" id="previewValue">-</div>
            </div>
        </div>

        <!-- Usage & Validity Section -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-calendar-alt"></i>
                Usage & Validity
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="usage_limit" class="form-label required-label">Usage Limit</label>
                    <div class="input-group">
                        <i class="fas fa-users input-group-text"></i>
                        <input type="number" name="usage_limit" id="usage_limit" class="form-control"
                               required min="1" placeholder="Maximum number of uses">
                    </div>
                    <div class="help-text">Maximum number of times this coupon can be used</div>
                </div>

                <div class="form-group">
                    <label for="expiry_date" class="form-label required-label">Expiry Date</label>
                    <div class="input-group">
                        <i class="fas fa-calendar input-group-text"></i>
                        <input type="date" name="expiry_date" id="expiry_date" class="form-control" required>
                    </div>
                    <div class="help-text">Date when the coupon expires</div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="minimum_amount" class="form-label">Minimum Order Amount</label>
                    <div class="input-group">
                        <i class="fas fa-rupee-sign input-group-text"></i>
                        <input type="number" name="minimum_amount" id="minimum_amount" class="form-control"
                               min="0" step="0.01" placeholder="Minimum order value (optional)">
                    </div>
                    <div class="help-text">Minimum order amount required to use this coupon</div>
                </div>

                <div class="form-group">
                    <label for="maximum_discount" class="form-label">Maximum Discount Cap</label>
                    <div class="input-group">
                        <i class="fas fa-rupee-sign input-group-text"></i>
                        <input type="number" name="maximum_discount" id="maximum_discount" class="form-control"
                               min="0" step="0.01" placeholder="Maximum discount amount (optional)">
                    </div>
                    <div class="help-text">Maximum discount amount for percentage-based coupons</div>
                </div>
            </div>
        </div>

        <button type="submit" class="btn-primary">
            <i class="fas fa-plus-circle"></i>
            Create Coupon
        </button>
    </form>

    <!-- Messages -->
    {% if messages %}
        <div class="mt-3">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i>{{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Enhanced Coupon Creation Form
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('couponForm');
        const discountTypeSelect = document.getElementById('discount_type');
        const discountInput = document.getElementById('discount');
        const codeInput = document.getElementById('code');
        const expiryDateInput = document.getElementById('expiry_date');
        const discountPreview = document.getElementById('discountPreview');
        const previewValue = document.getElementById('previewValue');

        // Set minimum date to today
        const today = new Date().toISOString().split('T')[0];
        expiryDateInput.min = today;

        // Auto-generate coupon code
        function generateCouponCode() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let result = '';
            for (let i = 0; i < 8; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }

        // Add generate button functionality
        const generateBtn = document.createElement('button');
        generateBtn.type = 'button';
        generateBtn.className = 'btn btn-outline-secondary btn-sm mt-1';
        generateBtn.innerHTML = '<i class="fas fa-random me-1"></i>Generate';
        generateBtn.onclick = function() {
            codeInput.value = generateCouponCode();
            updatePreview();
        };
        codeInput.parentNode.parentNode.appendChild(generateBtn);

        // Update discount preview
        function updatePreview() {
            const discountType = discountTypeSelect.value;
            const discountValue = parseFloat(discountInput.value) || 0;

            if (discountType && discountValue > 0) {
                let previewText = '';
                if (discountType === 'percentage') {
                    previewText = `${discountValue}% OFF`;
                } else if (discountType === 'amount') {
                    previewText = `₹${discountValue} OFF`;
                }
                previewValue.textContent = previewText;
                discountPreview.classList.add('show');
            } else {
                discountPreview.classList.remove('show');
            }
        }

        // Validate discount value based on type
        function validateDiscount() {
            const discountType = discountTypeSelect.value;
            const discountValue = parseFloat(discountInput.value) || 0;

            if (discountType === 'percentage') {
                if (discountValue > 100) {
                    discountInput.value = 100;
                    showToast('Percentage discount cannot exceed 100%', 'warning');
                }
                discountInput.max = 100;
                discountInput.step = 0.01;
            } else if (discountType === 'amount') {
                discountInput.max = '';
                discountInput.step = 0.01;
            }
        }

        // Format coupon code
        function formatCouponCode() {
            let value = codeInput.value.toUpperCase();
            value = value.replace(/[^A-Z0-9]/g, ''); // Remove special characters
            codeInput.value = value;
        }

        // Event listeners
        discountTypeSelect.addEventListener('change', function() {
            validateDiscount();
            updatePreview();
        });

        discountInput.addEventListener('input', function() {
            validateDiscount();
            updatePreview();
        });

        codeInput.addEventListener('input', formatCouponCode);

        // Form validation
        form.addEventListener('submit', function(e) {
            const discountType = discountTypeSelect.value;
            const discountValue = parseFloat(discountInput.value) || 0;
            const usageLimit = parseInt(document.getElementById('usage_limit').value) || 0;

            // Validate discount value
            if (discountType === 'percentage' && discountValue > 100) {
                e.preventDefault();
                showToast('Percentage discount cannot exceed 100%', 'error');
                return false;
            }

            // Validate usage limit
            if (usageLimit > 10000) {
                if (!confirm(`Usage limit of ${usageLimit} is very high. Are you sure you want to continue?`)) {
                    e.preventDefault();
                    return false;
                }
            }

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
            submitBtn.disabled = true;

            // Re-enable button after 5 seconds (in case of error)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 5000);
        });

        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 3000);
        }

        // Auto-focus on first input
        codeInput.focus();
    });
</script>
{% endblock %}
