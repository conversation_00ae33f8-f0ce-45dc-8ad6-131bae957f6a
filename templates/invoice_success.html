<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5L8WZWCC');</script>
<!-- End Google Tag Manager -->

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="robots" content="noindex, nofollow">
    <title>Invoice Created Successfully - Librainian</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- Dark Mode CSS -->
    <link rel="stylesheet" href="/static/css/dark-mode.css">

    <style>
        /* CSS Variables for Light/Dark Mode */
        :root {
            /* Light Mode Colors */
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --text-primary: #111827;
            --text-secondary: #4b5563;
            --text-muted: #9ca3af;
            --border-color: #e5e7eb;

            /* Glassmorphism Variables */
            --glass-bg: rgba(255, 255, 255, 0.15);
            --glass-border: rgba(255, 255, 255, 0.3);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            --glass-backdrop: blur(20px);

            /* Gradients */
            --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        /* Dark Mode Variables */
        body.dark-mode {
            --bg-primary: #111827;
            --bg-secondary: #1f2937;
            --text-primary: #ffffff;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --border-color: #4b5563;

            /* Dark Glassmorphism */
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

            /* Dark Gradients */
            --gradient-hero: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        }

        /* Modern Success Page Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comfortaa', sans-serif;
            background: var(--gradient-hero);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 1rem;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            overflow-x: hidden;
            overflow-y: auto;
            position: relative;
            color: var(--text-primary);
            transition: background 0.3s ease, color 0.3s ease;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .page-wrapper {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }

        .success-container {
            background: var(--glass-bg);
            backdrop-filter: var(--glass-backdrop);
            -webkit-backdrop-filter: var(--glass-backdrop);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            padding: 3rem 2rem;
            text-align: center;
            box-shadow: var(--glass-shadow);
            max-width: 600px;
            width: 100%;
            position: relative;
            z-index: 1;
            animation: slideInUp 0.8s ease-out;
            color: var(--text-primary);
            transition: all 0.3s ease;
            margin: auto;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .success-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            background: var(--gradient-success);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            animation: bounceIn 1s ease-out 0.5s both;
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .success-icon::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            background: var(--gradient-success);
            border-radius: 50%;
            opacity: 0.3;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.3;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.1;
            }
        }

        .success-icon i {
            font-size: 3rem;
            color: white;
            position: relative;
            z-index: 1;
            animation: checkmark 0.6s ease-in-out 1.2s both;
        }

        @keyframes checkmark {
            0% {
                transform: scale(0) rotate(-45deg);
            }
            50% {
                transform: scale(1.2) rotate(-45deg);
            }
            100% {
                transform: scale(1) rotate(0deg);
            }
        }

        .success-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
            animation: fadeInUp 0.6s ease-out 0.8s both;
        }

        .success-message {
            font-size: 1.125rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            line-height: 1.6;
            animation: fadeInUp 0.6s ease-out 1s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            animation: fadeInUp 0.6s ease-out 1.2s both;
        }

        .btn-primary {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .btn-secondary {
            background: var(--glass-bg);
            border: 2px solid var(--glass-border);
            border-radius: 12px;
            padding: 1rem 2rem;
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            backdrop-filter: var(--glass-backdrop);
            -webkit-backdrop-filter: var(--glass-backdrop);
        }

        .btn-secondary:hover {
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.1);
            border-color: #6366f1;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .invoice-info {
            background: var(--glass-bg);
            backdrop-filter: var(--glass-backdrop);
            -webkit-backdrop-filter: var(--glass-backdrop);
            border: 1px solid var(--glass-border);
            color: var(--text-primary);
            padding: 1rem;
            border-radius: 12px;
            margin: 1.5rem 0;
            animation: fadeInUp 0.6s ease-out 1.4s both;
            box-shadow: var(--glass-shadow);
        }

        .invoice-info strong {
            font-weight: 700;
            color: var(--text-primary);
        }

        .invoice-info .invoice-number {
            color: var(--text-primary);
            font-weight: 600;
        }



        /* Responsive Design */
        @media (max-width: 768px) {
            .success-container {
                padding: 2rem 1rem;
                margin: 1rem;
            }

            .success-title {
                font-size: 1.5rem;
            }

            .success-message {
                font-size: 1rem;
            }

            .success-icon {
                width: 100px;
                height: 100px;
            }

            .success-icon i {
                font-size: 2.5rem;
            }

            .action-buttons {
                gap: 0.75rem;
            }

            .btn-primary,
            .btn-secondary {
                padding: 0.875rem 1.5rem;
                font-size: 1rem;
            }
        }

        /* Progress Section within Success Card */
        .progress-section {
            margin: 2rem 0;
            padding: 1.5rem;
            background: var(--glass-bg);
            backdrop-filter: var(--glass-backdrop);
            -webkit-backdrop-filter: var(--glass-backdrop);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            animation: fadeInUp 0.6s ease-out 1.6s both;
        }

        .progress-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            margin-bottom: 1rem;
        }

        .progress-line {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--glass-border);
            transform: translateY(-50%);
            z-index: 1;
        }

        .progress-line-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981 0%, #3b82f6 50%, #8b5cf6 100%);
            width: 0%;
            transition: width 1s ease-in-out;
            border-radius: 1px;
        }

        .progress-step {
            position: relative;
            z-index: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
            background: var(--bg-primary);
            border-radius: 15%;
            padding: 0.5rem;
        }

        .step-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            transition: all 0.5s ease;
            border: 3px solid var(--glass-border);
            background: var(--glass-bg);
        }

        .step-icon.active {
            transform: scale(1.1);
            box-shadow: 0 5px 20px rgba(16, 185, 129, 0.4);
        }

        .step-icon.completed {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-color: #10b981;
        }

        .step-icon.email.completed {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-color: #3b82f6;
        }

        .step-icon.sms.completed {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            border-color: #8b5cf6;
        }

        .step-label {
            margin-top: 0.5rem;
            font-size: 0.8rem;
            font-weight: 500;
            color: var(--text-secondary);
            text-align: center;
            min-height: 2rem;
            display: flex;
            align-items: center;
        }

        .step-label.active {
            color: var(--text-primary);
            font-weight: 600;
        }

        .status-message {
            text-align: center;
            padding: 1rem;
            border-radius: 8px;
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
            color: var(--text-primary);
            font-size: 0.9rem;
            margin-top: 1rem;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .status-message.show {
            opacity: 1;
        }

        .disclaimer {
            font-size: 0.75rem;
            color: var(--text-muted);
            margin-top: 0.5rem;
            font-style: italic;
            text-align: center;
        }

        /* Responsive Design for Progress Steps */
        @media (max-width: 768px) {
            .page-wrapper {
                padding: 1rem;
                align-items: flex-start;
            }

            .success-container {
                margin-top: 1rem;
                padding: 2rem 1rem;
            }

            .progress-steps {
                flex-direction: column;
                gap: 1.5rem;
                align-items: stretch;
            }

            .progress-line {
                display: none;
            }

            .progress-step {
                flex-direction: row;
                text-align: left;
                background: transparent;
                padding: 0;
            }

            .step-icon {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
                margin-right: 1rem;
                flex-shrink: 0;
            }

            .step-label {
                font-size: 0.9rem;
                margin-top: 0;
                min-height: auto;
                align-items: flex-start;
                text-align: left;
                line-height: 1.4;
            }

            .progress-section {
                padding: 1rem;
            }

            .success-title {
                font-size: 1.5rem;
            }

            .success-message {
                font-size: 1rem;
            }

            .success-icon {
                width: 100px;
                height: 100px;
            }

            .success-icon i {
                font-size: 2.5rem;
            }

            .action-buttons {
                gap: 0.75rem;
            }

            .btn-primary,
            .btn-secondary {
                padding: 0.875rem 1.5rem;
                font-size: 1rem;
            }
        }

        /* Print Styles */
        @media print {
            body {
                background: white;
            }

            .success-container {
                box-shadow: none;
                border: 1px solid #ddd;
                background: white;
            }

            .action-buttons,
            .progress-section,
            .status-message {
                display: none;
            }
        }
    </style>
</head>
<body>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5L8WZWCC"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

    <div class="page-wrapper">
        <div class="success-container">
        <!-- Success Icon -->
        <div class="success-icon">
            <i class="fas fa-check"></i>
        </div>

        <!-- Success Title -->
        <h1 class="success-title">Invoice Created Successfully!</h1>

        <!-- Success Message -->
        <p class="success-message">
            Your invoice has been created and is ready for processing.
            The student will receive a notification about the new invoice.
        </p>

        <!-- Invoice Information -->
        <div class="invoice-info">
            <strong>Invoice No:</strong> <span class="invoice-number">{{ invoice.invoice_id }}</span><br>
            <strong>Student:</strong> {{ invoice.student.name }}<br>
            <strong>Amount:</strong> ₹{{ invoice.total_amount }}
        </div>

        <!-- Progress Section -->
        <div class="progress-section">
            <div class="progress-title">Processing Status</div>

            <div class="progress-steps">
                <div class="progress-line">
                    <div class="progress-line-fill" id="progressFill"></div>
                </div>

                <div class="progress-step">
                    <div class="step-icon" id="stepInvoice">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="step-label" id="labelInvoice">Invoice<br>Generated</div>
                </div>

                <div class="progress-step">
                    <div class="step-icon email" id="stepEmail">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="step-label" id="labelEmail">Email<br>Sent</div>
                </div>

                <div class="progress-step">
                    <div class="step-icon sms" id="stepSms">
                        <i class="fas fa-sms"></i>
                    </div>
                    <div class="step-label" id="labelSms">SMS<br>Sent</div>
                </div>
            </div>

            <div class="status-message" id="statusMessage">
                <div id="statusText">Invoice generated successfully!</div>
                <div class="disclaimer" id="disclaimerText"></div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="/students/invoice_student/{{ invoice.slug }}/" class="btn-primary">
                <i class="fas fa-eye"></i>
                View Invoice
            </a>
            <!-- Fix back to students list URL -->
            <a href="/students/" class="btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Back to Students
            </a>
        </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Success Page Enhancement
        class SuccessPageManager {
            constructor() {
                this.init();
            }

            init() {
                this.setupAutoRedirect();
                this.trackPageVisit();
                this.playSuccessSound();
            }



            setupAutoRedirect() {
                // Auto-redirect after 15 seconds to students list
                let countdown = 15;
                const redirectTimer = setInterval(() => {
                    countdown--;
                    if (countdown <= 0) {
                        clearInterval(redirectTimer);
                        // Fix redirect URL - use proper students list URL
                        const role = '{{ role|default:"librarian" }}';
                        window.location.href = `/students/`;
                    }
                }, 1000);

                // Clear timer if user interacts with the page
                document.addEventListener('click', () => {
                    clearInterval(redirectTimer);
                });
            }

            trackPageVisit() {
                // Track page visit for analytics
                const path = window.location.pathname;
                let pageData = JSON.parse(localStorage.getItem("page_data")) || {};
                pageData[path] = pageData[path] ? pageData[path] + 1 : 1;
                localStorage.setItem("page_data", JSON.stringify(pageData));

                // Track success event
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'invoice_created', {
                        'event_category': 'invoice',
                        'event_label': 'success_page'
                    });
                }
            }

            playSuccessSound() {
                // Start the tick sequence first
                this.showTickSequence();
            }

            playDoneSound() {
                // Play done.mp3 sound synchronized with main tick
                try {
                    const audio = new Audio('/static/audio/done.mp3');
                    audio.volume = 0.5;

                    // Play sound with user interaction fallback
                    const playPromise = audio.play();
                    if (playPromise !== undefined) {
                        playPromise.catch(error => {
                            console.log('Audio autoplay blocked, will play on user interaction:', error);
                            // Add click listener to play sound on first user interaction
                            document.addEventListener('click', () => {
                                audio.play().catch(e => console.log('Audio play failed:', e));
                            }, { once: true });
                        });
                    }
                } catch (error) {
                    console.log('Success sound not available:', error);
                }
            }

            showTickSequence() {
                const statusMessage = document.getElementById('statusMessage');
                const statusText = document.getElementById('statusText');
                const disclaimerText = document.getElementById('disclaimerText');
                const progressFill = document.getElementById('progressFill');

                // Show status message
                statusMessage.classList.add('show');

                // 1. Invoice step - immediate with sound
                this.activateStep('stepInvoice', 'labelInvoice', () => {
                    statusText.textContent = 'Invoice generated successfully!';
                    progressFill.style.width = '33%';
                    // Play sound synchronized with main tick
                    this.playDoneSound();
                });

                // 2. Email step - after 2 seconds
                setTimeout(() => {
                    this.activateStep('stepEmail', 'labelEmail', () => {
                        statusText.textContent = 'Email notification sent!';
                        progressFill.style.width = '66%';
                    });
                }, 3000);

                // 3. SMS step - after 4 seconds
                setTimeout(() => {
                    this.activateStep('stepSms', 'labelSms', () => {
                        statusText.textContent = 'SMS notification sent!';
                        disclaimerText.textContent = '*Delivery subject to network conditions and service provider availability';
                        progressFill.style.width = '100%';
                    });
                }, 6000);
            }

            activateStep(stepId, labelId, callback) {
                const stepIcon = document.getElementById(stepId);
                const stepLabel = document.getElementById(labelId);

                // Add active state
                stepIcon.classList.add('active');
                stepLabel.classList.add('active');

                // Execute callback
                if (callback) callback();

                // After animation, mark as completed
                setTimeout(() => {
                    stepIcon.classList.remove('active');
                    stepIcon.classList.add('completed');
                    stepLabel.classList.remove('active');
                }, 1000);
            }
        }

        // Utility functions
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Dark Mode Auto-Detection
        function initDarkMode() {
            // Check for saved dark mode preference or system preference
            const savedDarkMode = localStorage.getItem('darkMode');
            const systemDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

            if (savedDarkMode === 'enabled' || (savedDarkMode === null && systemDarkMode)) {
                document.body.classList.add('dark-mode');
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize dark mode
            initDarkMode();

            // Initialize success manager
            window.successManager = new SuccessPageManager();

            // Removed welcome notification

            // Add click tracking to buttons with proper navigation
            document.querySelectorAll('.btn-primary, .btn-secondary').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    const action = this.textContent.trim();
                    console.log(`User clicked: ${action}`);

                    // Check if it's the back button and handle navigation properly
                    if (this.classList.contains('btn-secondary')) {
                        e.preventDefault();

                        // Add loading state
                        const originalText = this.innerHTML;
                        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
                        this.style.pointerEvents = 'none';

                        // Navigate after short delay
                        setTimeout(() => {
                            const role = '{{ role|default:"admin" }}';
                            window.location.href = `/students/`;
                        }, 500);
                    } else {
                        // For other buttons, add loading state but allow normal navigation
                        const originalText = this.innerHTML;
                        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
                        this.style.pointerEvents = 'none';

                        // Restore after a short delay (navigation will happen)
                        setTimeout(() => {
                            this.innerHTML = originalText;
                            this.style.pointerEvents = 'auto';
                        }, 1000);
                    }
                });
            });
        });

        // Prevent right-click and common keyboard shortcuts for security
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        document.addEventListener('keydown', function(e) {
            // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
            if (e.keyCode === 123 ||
                (e.ctrlKey && e.shiftKey && (e.keyCode === 73 || e.keyCode === 74)) ||
                (e.ctrlKey && e.keyCode === 85)) {
                e.preventDefault();
                return false;
            }
        });

        // Add smooth scrolling for better UX
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
