<!DOCTYPE html>
<html>
<head>
    <meta name="google" content="notranslate">
    <title>Checkout</title>
    <script>
        // Inline loader script to show loader immediately when page starts loading
        (function() {
            // Check for dark mode preference immediately
            var isDarkMode = localStorage.getItem('darkMode') === 'enabled';
            var loaderBg = isDarkMode ? 'linear-gradient(135deg, #1f2937 0%, #111827 100%)' : 'rgba(0, 0, 0, 0.85)';

            // Create loader HTML with FSEX300 font and LIBRAINIAN text
            var loaderHTML = `
            <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: ${loaderBg}; z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                    <style>
                        @font-face {
                            font-family: 'FSEX300';
                            src: url('/static/fonts/FSEX300.ttf') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                        }

                        @keyframes blink {
                            0%, 100% { opacity: 1; }
                            50% { opacity: 0; }
                        }

                        @keyframes dots {
                            0% { content: ""; }
                            25% { content: "."; }
                            50% { content: ".."; }
                            75% { content: "..."; }
                            100% { content: ""; }
                        }

                        .loader-text {
                            font-family: 'FSEX300', monospace;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 2px;
                            margin-bottom: 20px;
                            text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                        }

                        .loader-dots::after {
                            content: "";
                            animation: dots 1.5s infinite;
                        }

                        .loader-bar {
                            width: 300px;
                            height: 20px;
                            background-color: rgba(255, 255, 255, 0.2);
                            border-radius: 10px;
                            overflow: hidden;
                            margin: 20px auto;
                        }

                        .loader-progress {
                            width: 0%;
                            height: 100%;
                            background-color: ${isDarkMode ? '#6366f1' : '#6200ee'};
                            border-radius: 10px;
                            animation: progress 2s infinite;
                        }

                        @keyframes progress {
                            0% { width: 0%; }
                            50% { width: 100%; }
                            100% { width: 0%; }
                        }
                    </style>
                    <div class="loader-text">LIBRAINIAN<span class="loader-dots"></span></div>
                    <div class="loader-bar">
                        <div class="loader-progress"></div>
                    </div>
                </div>
            </div>
            `;

            // Add loader to page
            document.write(loaderHTML);

            // Remove loader when page is loaded
            window.addEventListener('load', function() {
                var loader = document.getElementById('initialLoader');
                if (loader) {
                    loader.style.display = 'none';
                }
            });
        })();
    </script>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
  
        
        <!-- disable Print Screen for Windows -->
    
          
        
        <!-- disable print screen for mac -->
    
          
    
        <!-- disabling print screen for Linux -->
    
  
    
        <!-- disabling inspection tool -->
    
          
        
    
        <style>
    
            body {
                -webkit-user-select: none; /* Disable text selection */
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
                margin: 0;
            }
            </style>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Comfortaa', Arial, sans-serif;
        }
    </style>
</head>
<body>
    <h1>Checkout</h1>
    <script>
        var options = {
            "key": "{{ key_id }}", // Your Razorpay key id
            "amount": "{{ amount|floatformat:2 }}00", // Amount in paise
            "currency": "{{ currency }}",
            "name": "Librainian",
            "description": "Advertisement Payment",
            "order_id": "{{ order_id }}", // This is the order_id created from the backend
            "handler": function (response) {
                // Send payment details to your server via AJAX
                var xhr = new XMLHttpRequest();
                xhr.open("POST", "{% url 'payment_success' %}", true);
                xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
                xhr.setRequestHeader("X-CSRFToken", "{{ csrf_token }}"); // Set CSRF token header

                xhr.onreadystatechange = function () {
                    if (xhr.readyState === XMLHttpRequest.DONE) {
                        if (xhr.status === 200) {
                            // Handle successful response
                            window.location.href = "{% url 'advertisement_list' %}"; // Redirect to the advertisement list page
                        } else {
                            alert('Payment failed. Please try again.');
                        }
                    }
                };

                // Send the payment data
                var data = "razorpay_payment_id=" + encodeURIComponent(response.razorpay_payment_id) +
                           "&razorpay_order_id=" + encodeURIComponent(response.razorpay_order_id) +
                           "&razorpay_signature=" + encodeURIComponent(response.razorpay_signature);
                xhr.send(data);
            },
            "prefill": {
                "name": "",
                "email": "",
                "contact": ""
            }
        };
        var rzp1 = new Razorpay(options);
        rzp1.open();
    </script>
</body>
</html>
