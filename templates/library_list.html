<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <link rel="icon" href="/static/img/librainian-logo-black-transparent.png" type="image/x-icon">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta http-equiv="content-language" content="en">
    <meta name="geo.region" content="IN">

    <!-- Include Navbar Head Section -->
    {% with navbar_section="head" %}
        {% include "public_navbar.html" %}
    {% endwith %}

    <title>Library Search | Find Libraries Near You | Librainian</title>
    <link rel="canonical" href="https://www.librainian.com/librarian/library-list/">

    <!-- SEO Meta Tags -->
    <meta name="robots" content="index, follow, max-image-preview:large">
    <meta name="keywords" content="library search, find libraries, study spaces, reading rooms, library facilities, library directory, library management system, libraries near me, study rooms, library search engine, Librainian">
    <meta name="description" content="Search and explore libraries registered with Librainian - The #1 Library Management System. Find study spaces, reading rooms, and library facilities near you. Get details about opening hours, contact information, and available services.">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Librainian">
    <meta property="og:title" content="Library Search | Find Libraries Near You | Librainian">
    <meta property="og:description" content="Search and explore libraries registered with Librainian. Find study spaces, reading rooms, and library facilities near you.">
    <meta property="og:url" content="https://www.librainian.com/librarian/library-list/">
    <meta property="og:image" content="https://www.librainian.com/static/img/library_search.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="en_US">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@librainian_app">
    <meta name="twitter:creator" content="@librainian_app">
    <meta name="twitter:title" content="Library Search | Find Libraries Near You | Librainian">
    <meta name="twitter:description" content="Search and explore libraries registered with Librainian. Find study spaces, reading rooms, and library facilities near you.">
    <meta name="twitter:image" content="https://www.librainian.com/static/img/library_search.jpg">

    <!-- Author and Date Info -->
    <meta itemprop="author" content="Librainian Team">
    <meta itemprop="datePublished" content="2024-01-01">
    <meta itemprop="dateModified" content="2024-07-07">

    <!-- Stylesheets -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css">

    <!-- Structured Data / JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": "Library Search | Find Libraries Near You | Librainian",
        "description": "Search and explore libraries registered with Librainian - The #1 Library Management System. Find study spaces, reading rooms, and library facilities near you.",
        "url": "https://www.librainian.com/librarian/library-list/",
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "logo": {
                "@type": "ImageObject",
                "url": "https://www.librainian.com/static/img/librainian-logo-black-transparent.png"
            }
        },
        "isPartOf": {
            "@type": "WebSite",
            "name": "Librainian",
            "url": "https://www.librainian.com/"
        },
        "breadcrumb": {
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "Home",
                    "item": "https://www.librainian.com/"
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "name": "Library Search",
                    "item": "https://www.librainian.com/librarian/library-list/"
                }
            ]
        }
    }
    </script>

    <!-- Library Directory Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ItemList",
        "itemListElement": [
            {% for l in library %}
            {
                "@type": "ListItem",
                "position": {{ forloop.counter }},
                "item": {
                    "@type": "LocalBusiness",
                    "name": "{{ l.library_name }}",
                    "description": "{{ l.description|default:'Library managed by Librainian' }}",
                    "address": {
                        "@type": "PostalAddress",
                        "addressLocality": "{{ l.librarian_address }}"
                    },
                    "telephone": "{{ l.librarian_phone_num }}",
                    "url": "https://librainian.com/librarian/library-details/{{ l.slug }}/",
                    "image": "{% if l.image %}{{ l.image.url }}{% else %}https://i.postimg.cc/wTvpt244/library-demo.jpg{% endif %}"
                }
            }{% if not forloop.last %},{% endif %}
            {% endfor %}
        ]
    }
    </script>

    <!-- Search Action Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "url": "https://www.librainian.com/",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "https://www.librainian.com/librarian/library-list/?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>





    <!-- Import master CSS variables from code_temp.html -->
    {% include "code_temp.html" %}

    <style>
        :root {
            /* Library list specific variable aliases for compatibility */
            --primary-color: var(--primary);
            --primary-dark: var(--primary-dark);
            --secondary-color: var(--gray-800);
            --accent-color: var(--accent);
            --success-color: var(--success);
            --danger-color: var(--danger);
            --text-color: var(--gray-900);
            --text-light: var(--gray-500);
            --text-white: var(--white);
            --glass-bg: var(--glass-bg);
            --glass-border: var(--glass-border);
            --bg-gradient: var(--gradient-hero);
            --shadow-sm: var(--shadow-sm);
            --shadow-md: var(--shadow-md);
            --shadow-glass: var(--glass-shadow);
            --border-radius: var(--border-radius);
            --transition: var(--transition);
        }

        * {
            box-sizing: border-box;
        }

        body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            padding: 0;
            font-family: 'Comfortaa', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-gradient);
            color: var(--text-color);
            min-height: 100vh;
            overflow-x: hidden;
            transition: background 0.3s ease, color 0.3s ease;
        }

        /* Dark Mode Styles */
        body.dark-mode {
            --bg-gradient: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            --text-color: #ffffff;
            --text-white: #ffffff;
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
        }

        /* Navbar Styles */
        .navbar {
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-bottom: 1px solid var(--glass-border);
            box-shadow: var(--shadow-glass);
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
        }

        .navbar-brand img {
            height: 45px;
            transition: var(--transition);
            max-width: 200px;
            width: auto;
            display: block;
            opacity: 1;
            visibility: visible;
        }

        .navbar-brand img:hover {
            transform: scale(1.05);
        }



        .navbar .nav-link {
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            padding: 0.5rem 1rem;
            transition: var(--transition);
            border-radius: 25px;
        }

        .navbar .nav-link:hover {
            color: var(--text-white);
            background: rgba(255, 255, 255, 0.1);
        }

        .navbar .nav-link.active {
            color: var(--text-white);
            background: rgba(255, 255, 255, 0.2);
            font-weight: 600;
        }

        .navbar .btn {
            border-radius: 25px;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .btn-outline-primary {
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.9);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
            color: var(--text-white);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
            color: var(--text-white);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        /* Dark Mode Toggle Button */
        #darkModeToggle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        #darkModeToggle:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: scale(1.05);
        }

        body.dark-mode #darkModeToggle {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.2);
        }

        body.dark-mode #darkModeToggle:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
        }

        /* Hero Section */
        .hero-section {
            padding: 6rem 0 4rem;
            position: relative;
            text-align: center;
            color: var(--text-white);
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            animation: fadeInUp 0.8s ease-out;
        }

        .hero-description {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 700px;
            margin: 0 auto 3rem;
            line-height: 1.6;
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        /* Search Box */
        .search-container {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            max-width: 700px;
            margin: 0 auto;
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        .search-box {
            width: 100%;
            padding: 1.5rem 4.5rem 1.5rem 2rem;
            font-size: 1.1rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 50px;
            outline: none;
            transition: var(--transition);
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            color: var(--text-white);
            box-shadow: var(--shadow-glass);
        }

        .search-box::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-box:focus {
            border-color: rgba(255, 255, 255, 0.4);
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.3);
        }

        .search-button {
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--text-white);
            border: none;
            border-radius: 50%;
            width: 3.5rem;
            height: 3.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--shadow-sm);
        }

        .search-button:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
            transform: translateY(-50%) scale(1.05);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .search-icon {
            font-size: 1.25rem;
        }

        /* Filter Section */
        .filter-section {
            padding: 1rem 0 2rem;
            margin: 1rem 1rem;
        }

        .filter-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 2rem;
            justify-content: center;
        }

        .filter-button {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 50px;
            padding: 0.75rem 1.5rem;
            font-size: 0.95rem;
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            transition: var(--transition);
            backdrop-filter: blur(8px);
            font-weight: 500;
        }

        .filter-button:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: var(--text-white);
            transform: translateY(-2px);
        }

        .filter-button.active {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--text-white);
            border-color: var(--primary-color);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .sort-dropdown {
            margin-left: auto;
        }

        .dropdown-toggle {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.9);
            border-radius: 50px;
            padding: 0.75rem 1.5rem;
            backdrop-filter: blur(8px);
        }

        .dropdown-toggle:hover, .dropdown-toggle:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: var(--text-white);
        }

        .dropdown-menu {
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            box-shadow: var(--shadow-glass);
        }

        .dropdown-item {
            color: var(--text-white);
            transition: var(--transition);
        }

        .dropdown-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-white);
        }

        /* Library Cards */
        .library-grid {
            margin: 2rem 1rem;
            padding: 2rem;
        }

        #libraryList {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
            padding: 1rem 0;
        }

        /* Responsive grid adjustments for better card distribution */
        @media (min-width: 1200px) {
            #libraryList {
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 2rem;
            }
        }

        @media (min-width: 1400px) {
            #libraryList {
                grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
                gap: 2rem;
            }
        }

        .library-item {
            display: none;
            transition: var(--transition);
        }

        .library-card {
            background: var(--glass-bg);
            backdrop-filter: var(--glass-backdrop);
            -webkit-backdrop-filter: var(--glass-backdrop);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            height: 100%;
            transition: var(--transition-slow);
            position: relative;
            overflow: hidden;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .library-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                transparent 50%,
                rgba(255, 255, 255, 0.05) 100%);
            pointer-events: none;
            z-index: 1;
            border-radius: 20px;
        }



        .library-card:hover {
            transform: translateY(-3px);
            box-shadow:
                0 35px 60px -12px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.15) inset,
                0 2px 4px rgba(255, 255, 255, 0.15) inset;
        }



        .card-img-container {
            position: relative;
            height: 220px;
            overflow: hidden;
        }

        .card-img-top {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .library-card:hover .card-img-top {
            transform: scale(1.1);
        }

        .discount-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: linear-gradient(135deg, var(--accent-color) 0%, #f97316 100%);
            color: var(--text-white);
            padding: 0.75rem 1.25rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.9rem;
            z-index: 2;
            box-shadow: var(--shadow-sm);
            animation: pulse 2s infinite;
        }

        .card-body {
            padding: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-white);
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .card-info {
            margin-bottom: 0.75rem;
            display: flex;
            align-items: flex-start;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.75rem;
            border-radius: 8px;
            backdrop-filter: blur(8px);
        }

        .card-info i {
            margin-right: 0.75rem;
            margin-top: 0.25rem;
            width: 1.25rem;
            text-align: center;
            color: var(--text-white);
        }

        .card-text {
            font-size: 0.95rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        .card-text.description {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            max-height: 4.5em;
            margin-bottom: 1.5rem;
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .card-footer {
            padding: 1rem 1.5rem;
            background: rgba(255, 255, 255, 0.05);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 2;
        }

        /* Glass Component Styles for Library Cards */
        .glass-body {
            background: transparent;
            color: var(--text-primary);
        }

        .glass-header {
            background: rgba(255, 255, 255, 0.1);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .btn-view {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--text-white);
            border: none;
            border-radius: 50px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: var(--transition);
            width: 100%;
            box-shadow: var(--shadow-sm);
        }

        .btn-view:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
            color: var(--text-white);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        /* No Results Message */
        .no-results {
            text-align: center;
            padding: 4rem 2rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .no-results i {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            opacity: 0.6;
            color: rgba(255, 255, 255, 0.6);
        }

        .no-results h3 {
            color: var(--text-white);
            margin-bottom: 1rem;
        }

        .no-results p {
            margin-bottom: 2rem;
            opacity: 0.8;
        }

        /* Footer */
        .footer {
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(16px);
            color: var(--text-white);
            padding: 3rem 0 1.5rem;
            margin-top: 4rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .footer-logo {
            height: 50px;
            margin-bottom: 1rem;
            transition: var(--transition);
        }

        .footer-logo:hover {
            transform: scale(1.05);
        }

        .footer-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.95rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .footer-links h5 {
            font-size: 1.1rem;
            margin-bottom: 1.25rem;
            color: var(--text-white);
        }

        .footer-links ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-links li {
            margin-bottom: 0.75rem;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-links a:hover {
            color: var(--text-white);
            text-decoration: underline;
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 1.5rem;
            margin-top: 2rem;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
            text-align: center;
        }

        /* Dark Mode Component Styles */
        body.dark-mode .navbar {
            background: rgba(255, 255, 255, 0.05);
            border-bottom-color: rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .search-box {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        body.dark-mode .search-box::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        body.dark-mode .search-box:focus {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
        }

        body.dark-mode .filter-button {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.8);
        }

        body.dark-mode .filter-button:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
            color: white;
        }

        body.dark-mode .dropdown-toggle {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.9);
        }

        body.dark-mode .dropdown-toggle:hover,
        body.dark-mode .dropdown-toggle:focus {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
            color: white;
        }

        body.dark-mode .dropdown-menu {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .dropdown-item {
            color: rgba(255, 255, 255, 0.9);
        }

        body.dark-mode .dropdown-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        body.dark-mode .library-card {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .card-info {
            background: rgba(255, 255, 255, 0.05);
        }

        body.dark-mode .card-text.description {
            background: rgba(255, 255, 255, 0.03);
            border-color: rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .card-footer {
            background: rgba(255, 255, 255, 0.03);
            border-top-color: rgba(255, 255, 255, 0.1);
        }

        /* Dark Mode Glass Components */
        body.dark-mode .glass-body {
            color: var(--text-primary);
        }

        body.dark-mode .glass-header {
            background: rgba(255, 255, 255, 0.05);
            border-top-color: rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .no-results {
            color: rgba(255, 255, 255, 0.8);
        }

        body.dark-mode .footer {
            background: rgba(0, 0, 0, 0.3);
            border-top-color: rgba(255, 255, 255, 0.1);
        }

        /* Dark Mode Text Visibility */
        body.dark-mode h1,
        body.dark-mode h2,
        body.dark-mode h3,
        body.dark-mode h4,
        body.dark-mode h5,
        body.dark-mode h6 {
            color: white !important;
        }

        body.dark-mode .hero-title,
        body.dark-mode .hero-description {
            color: white !important;
        }

        body.dark-mode .card-title {
            color: white !important;
        }

        body.dark-mode .card-text {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        body.dark-mode .text-muted {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        body.dark-mode .navbar .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        body.dark-mode .navbar .nav-link:hover {
            color: white !important;
        }

        body.dark-mode .navbar .nav-link.active {
            color: white !important;
        }

        body.dark-mode .btn-outline-primary {
            border-color: rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.9);
        }

        body.dark-mode .btn-outline-primary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
        }

        /* Enhanced Glassmorphism for Dark Mode */
        body.dark-mode .library-card:hover {
            box-shadow:
                0 35px 60px -12px rgba(0, 0, 0, 0.5),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset,
                0 2px 4px rgba(255, 255, 255, 0.1) inset;
        }

        body.dark-mode .back-to-top {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border-color: rgba(255, 255, 255, 0.2);
        }

        body.dark-mode .back-to-top:hover {
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }



        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Back to top button */
        .back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--text-white);
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
            z-index: 99;
            box-shadow: var(--shadow-md);
            border: 2px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(16px);
        }

        .back-to-top.show {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .hero-title {
                font-size: 2rem;
            }

            .search-container {
                max-width: 600px;
            }

            .search-box {
                padding: 1rem 4rem 1rem 1.25rem;
            }
        }

        @media (max-width: 768px) {
            .hero-section {
                padding: 2rem 1rem 0rem 1rem !important;
                text-align: center;
            }

            .hero-content {
                max-width: 100%;
                margin: 0 auto;
            }

            .hero-title {
                font-size: 1.75rem;
                margin-bottom: 1rem;
                padding: 0 1rem;
            }

            .hero-description {
                font-size: 1rem;
                margin-bottom: 2rem;
                padding: 0 1rem;
                max-width: 90%;
                margin-left: auto;
                margin-right: auto;
            }

            .search-container {
                max-width: 95%;
                margin: 0 auto 2rem;
                transform: none;
                padding: 0 1rem;
            }

            .search-box {
                padding: 0.9rem 3.5rem 0.9rem 1.25rem;
                font-size: 1rem;
            }

            .search-icon {
                margin-left: -1.5rem
            }

            .search-button {
                width: 2.5rem;
                height: 2.5rem;
            }

            .filter-section {
                padding: 1rem 0 1rem;
                margin: 0.5rem 0.5rem;
            }

            .filter-container {
                justify-content: center;
                flex-wrap: wrap;
                gap: 0.75rem;
            }

            .filter-button {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }

            .sort-dropdown {
                margin-left: 0;
                margin-top: 1rem;
                width: 100%;
            }

            .library-grid {
                margin: 1rem 0.5rem;
                padding: 1rem;
            }

            #libraryList {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                padding: 0;
            }

            .library-card {
                margin: 0 auto;
                max-width: 100%;
            }

            .card-img-container {
                height: 200px;
            }

            .card-body {
                padding: 1.25rem;
            }

            .card-title {
                font-size: 1.2rem;
                margin-bottom: 0.75rem;
            }

            .card-info {
                padding: 0.5rem;
                margin-bottom: 0.5rem;
            }

            .card-text.description {
                padding: 0.75rem;
                margin-bottom: 1rem;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                max-height: 3em;
            }

            .card-footer {
                padding: 1rem 1.25rem;
            }

            .btn-view {
                padding: 0.75rem 1.25rem;
                font-size: 0.95rem;
            }

            /* Mobile navbar adjustments */
            .navbar {
                padding: 0.75rem 1rem;
            }

            .navbar .container {
                padding: 0;
            }

            .navbar-brand {
                margin-right: auto;
            }

            .navbar-brand img {
                height: 35px;
                max-width: 150px;
            }

            .navbar-toggler {
                border: none;
                padding: 0.25rem 0.5rem;
                margin-left: 0.5rem;
            }

            .navbar-toggler:focus {
                box-shadow: none;
            }

            .navbar-toggler-icon {
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
            }

            .navbar-collapse {
                margin-top: 1rem;
                text-align: center;
            }

            .navbar-nav {
                text-align: center;
                margin-bottom: 1rem;
            }

            .nav-item {
                margin: 0.25rem 0;
            }

            .nav-link {
                padding: 0.5rem 1rem !important;
                border-radius: 25px !important;
                margin: 0.25rem 0 !important;
            }

            #darkModeToggle {
                width: 35px;
                height: 35px;
                margin-right: 0.5rem;
            }

            .navbar .btn {
                padding: 0.4rem 1rem;
                font-size: 0.9rem;
                margin: 0.25rem;
                border-radius: 25px;
            }

            .ms-lg-3 {
                margin-top: 1rem !important;
                justify-content: center;
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            /* Fix mobile container alignment */
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
                max-width: 100%;
            }

            /* Mobile body adjustments */
            body {
                overflow-x: hidden;
            }

            /* Mobile section spacing */
            .filter-section,
            .library-grid {
                margin-left: 0.5rem;
                margin-right: 0.5rem;
            }

            /* Mobile footer adjustments */
            .footer {
                padding: 2rem 0 1rem;
                margin-top: 2rem;
            }

            .footer-logo {
                height: 40px;
            }

            .back-to-top {
                bottom: 1rem;
                right: 1rem;
                width: 3rem;
                height: 3rem;
            }
        }

        /* Additional mobile optimizations */
        @media (max-width: 480px) {
            .hero-section {
                padding: 1.5rem 0.5rem 1rem;
            }

            .hero-title {
                font-size: 1.5rem;
                padding: 0 0.5rem;
            }

            .hero-description {
                font-size: 0.95rem;
                padding: 0 0.5rem;
                max-width: 95%;
            }

            .search-container {
                max-width: 100%;
                padding: 0 0.5rem;
            }

            .search-box {
                padding: 0.8rem 3rem 0.8rem 1rem;
                font-size: 0.95rem;
            }

            .search-button {
                width: 2.25rem;
                height: 2.25rem;
                right: 0.25rem;
            }

            .filter-section {
                margin: 0.25rem 0.25rem;
                padding: 0.75rem 0.5rem 1rem;
            }

            .filter-button {
                padding: 0.4rem 0.8rem;
                font-size: 0.85rem;
            }

            .library-grid {
                margin: 0.5rem 0.25rem;
                padding: 0.5rem;
            }

            .card-body {
                padding: 1rem;
            }

            .card-title {
                font-size: 1.1rem;
            }

            .card-info {
                padding: 0.4rem;
                font-size: 0.9rem;
            }

            .card-text.description {
                padding: 0.5rem;
                font-size: 0.9rem;
            }

            .card-footer {
                padding: 0.75rem 1rem;
            }

            .btn-view {
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
            }

            .navbar {
                padding: 0.5rem 0.75rem;
            }

            .navbar .container {
                padding: 0 0.5rem;
            }

            .navbar-brand img {
                height: 30px;
                max-width: 120px;
            }

            #darkModeToggle {
                width: 32px;
                height: 32px;
                margin-right: 0.25rem;
            }

            .navbar .btn {
                padding: 0.35rem 0.8rem;
                font-size: 0.85rem;
            }

            .discount-badge {
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
                top: 0.75rem;
                right: 0.75rem;
            }
        }
    </style>
</head>

<body style="padding-top: 80px;">
    <!-- Include Public Navigation Body -->
    {% with navbar_section="body" %}
        {% include "public_navbar.html" %}
    {% endwith %}

    <!-- Back to top button -->
    <button class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content text-center">
                <h1 class="hero-title" data-aos="fade-up">Find Your Perfect Library</h1>
                <p class="hero-description" data-aos="fade-up" data-aos-delay="100">
                    Discover libraries registered with Librainian. Explore study spaces, reading rooms, and library facilities near you.
                </p>
            </div>
        </div>
        <div class="search-container" data-aos="fade-up" data-aos-delay="200">
            <input type="text" id="searchInput" class="search-box" placeholder="Search by name, location or description...">
            <button class="search-button">
                <i class="fas fa-search search-icon"></i>
            </button>
        </div>
    </section>

    <!-- Filter Section -->
    <section class="filter-section">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="filter-container">
                        <button class="filter-button active" data-filter="all">All Libraries</button>
                        <button class="filter-button" data-filter="discount">With Discount</button>
                        <div class="dropdown sort-dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                Sort By
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                                <li><a class="dropdown-item" href="#" data-sort="name-asc">Name (A-Z)</a></li>
                                <li><a class="dropdown-item" href="#" data-sort="name-desc">Name (Z-A)</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" data-sort="discount">Highest Discount</a></li>
                            </ul>
                        </div>
                    </div>
                    <p class="text-muted small mb-4">
                        <i class="fas fa-info-circle me-1"></i> Type at least 3 characters to search. Popular libraries are shown by default.
                    </p>
                </div>
            </div>

        <div class="library-grid">
            <!-- Library List -->
            <div id="libraryList">
                {% for l in library %}
                <div class="library-item" data-aos="fade-up" data-aos-delay="{{ forloop.counter|add:2 }}00" data-discount="{% if l.discount_available %}true{% else %}false{% endif %}" data-name="{{ l.library_name }}" data-discount-amount="{{ l.discount_amount|default:0 }}" style="display: none;">
                    <div class="library-card glass-card">
                        <div class="card-img-container">
                            <img src="{% if l.image %}{{ l.image.url }}{% else %}https://i.postimg.cc/wTvpt244/library-demo.jpg{% endif %}" class="card-img-top" alt="{{ l.library_name }}" loading="lazy">
                            {% if l.discount_available %}
                            <div class="discount-badge">
                                <i class="fas fa-tags me-1"></i> {{ l.discount_amount }}% Off
                            </div>
                            {% endif %}
                        </div>
                        <div class="card-body glass-body">
                            <h3 class="card-title">{{ l.library_name }}</h3>
                            <div class="card-info">
                                <i class="fas fa-map-marker-alt"></i>
                                <div class="card-text">{{ l.librarian_address }}</div>
                            </div>
                            <div class="card-info">
                                <i class="fas fa-phone"></i>
                                <div class="card-text">{{ l.librarian_phone_num }}</div>
                            </div>
                            <div class="card-text description">{{ l.description|default:"No description available." }}</div>
                        </div>
                        <div class="card-footer glass-header">
                            <a href="/librarian/library-details/{{ l.slug }}/" class="btn btn-view">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}

                <!-- No Results Message -->
                <div class="col-12 no-results" style="display: none;">
                    <i class="fas fa-search"></i>
                    <h3>No libraries found</h3>
                    <p>Try different search terms or filters to find libraries</p>
                    <button class="btn btn-outline-primary mt-3" id="resetSearch">
                        <i class="fas fa-refresh me-2"></i>Reset Search
                    </button>
                </div>
            </div>
        </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4 mb-lg-0">
                    <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" class="footer-logo">
                    <p class="footer-text">
                        Librainian is the #1 Library Management System helping libraries streamline operations, manage resources, and enhance user experience.
                    </p>
                </div>
                <div class="col-6 col-lg-2 mb-4 mb-lg-0">
                    <div class="footer-links">
                        <h5>Quick Links</h5>
                        <ul>
                            <li><a href="/">Home</a></li>
                            <li><a href="/about/">About Us</a></li>
                            <li><a href="/blogs/p/">Blog</a></li>
                            <li><a href="/contact/">Contact</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-6 col-lg-3 mb-4 mb-lg-0">
                    <div class="footer-links">
                        <h5>Services</h5>
                        <ul>
                            <li><a href="#">Library Management</a></li>
                            <li><a href="#">Student Management</a></li>
                            <li><a href="#">Resource Tracking</a></li>
                            <li><a href="#">Analytics & Reports</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="footer-links">
                        <h5>Contact Us</h5>
                        <ul>
                            <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                            <li><i class="fas fa-phone me-2"></i> +91 1234567890</li>
                            <li><i class="fas fa-map-marker-alt me-2"></i> India</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom text-center">
                <p>&copy; 2024 Librainian. All rights reserved.</p>
            </div>
        </div>
    </footer>


    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS animation library
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true
            });

            // Dark mode functionality is now handled by public navbar

            // DOM Elements
            const searchInput = document.getElementById('searchInput');
            const searchButton = document.querySelector('.search-button');
            const libraryItems = document.querySelectorAll('.library-item');
            const filterButtons = document.querySelectorAll('.filter-button');
            const sortOptions = document.querySelectorAll('.dropdown-item');
            const resetSearchButton = document.getElementById('resetSearch');
            const backToTopButton = document.getElementById('backToTop');
            const noResultsElement = document.querySelector('.no-results');

            // Current filter and sort state
            let currentFilter = 'all';
            let currentSort = 'name-asc';



            // Show popular libraries on page load
            showPopularLibraries();

            // Focus on search input when page loads
            setTimeout(() => {
                searchInput.focus();
            }, 1000);

            // Filter libraries based on current filter and search term
            function filterLibraries() {
                const searchValue = searchInput.value.toLowerCase().replace(/\b(library|libraries|in|at|near|the)\b/g, '').trim();
                let found = false;

                libraryItems.forEach(function(item) {
                    const libraryName = item.querySelector('.card-title').textContent.toLowerCase();
                    const libraryAddress = item.querySelector('.card-info:nth-child(2)').textContent.toLowerCase();
                    const libraryDescription = item.querySelector('.description').textContent.toLowerCase();
                    const hasDiscount = item.getAttribute('data-discount') === 'true';

                    // Check if item matches search term
                    const matchesSearch = searchValue.length < 3 ||
                        libraryName.includes(searchValue) ||
                        libraryAddress.includes(searchValue) ||
                        libraryDescription.includes(searchValue);

                    // Check if item matches current filter
                    const matchesFilter = currentFilter === 'all' ||
                        (currentFilter === 'discount' && hasDiscount);

                    // Show item if it matches both search and filter
                    if (matchesSearch && matchesFilter) {
                        item.style.display = 'block';
                        found = true;
                    } else {
                        item.style.display = 'none';
                    }
                });

                // Show/hide no results message
                noResultsElement.style.display = found ? 'none' : 'block';

                // Apply current sort after filtering
                sortLibraries();
            }

            // Sort libraries based on current sort option
            function sortLibraries() {
                const libraryList = document.getElementById('libraryList');
                const items = Array.from(libraryItems);

                items.sort(function(a, b) {
                    const nameA = a.getAttribute('data-name').toLowerCase();
                    const nameB = b.getAttribute('data-name').toLowerCase();
                    const discountA = parseInt(a.getAttribute('data-discount-amount')) || 0;
                    const discountB = parseInt(b.getAttribute('data-discount-amount')) || 0;

                    switch (currentSort) {
                        case 'name-asc':
                            return nameA.localeCompare(nameB);
                        case 'name-desc':
                            return nameB.localeCompare(nameA);
                        case 'discount':
                            return discountB - discountA;
                        default:
                            return 0;
                    }
                });

                // Reorder items in the DOM
                items.forEach(function(item) {
                    libraryList.appendChild(item);
                });
            }

            // Show popular libraries (first 6)
            function showPopularLibraries() {
                let count = 0;
                libraryItems.forEach(function(item) {
                    if (count < 6) {
                        item.style.display = 'block';
                        count++;
                    } else {
                        item.style.display = 'none';
                    }
                });

                // Hide no results message
                noResultsElement.style.display = 'none';
            }

            // Reset search and filters
            function resetSearch() {
                searchInput.value = '';
                currentFilter = 'all';
                currentSort = 'name-asc';

                // Update UI to reflect reset
                filterButtons.forEach(function(button) {
                    button.classList.remove('active');
                    if (button.getAttribute('data-filter') === 'all') {
                        button.classList.add('active');
                    }
                });

                showPopularLibraries();
                searchInput.focus();
            }

            // Event Listeners

            // Search input
            searchInput.addEventListener('input', filterLibraries);

            // Search button
            searchButton.addEventListener('click', function() {
                filterLibraries();
            });

            // Filter buttons
            filterButtons.forEach(function(button) {
                button.addEventListener('click', function() {
                    // Update active state
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // Update current filter
                    currentFilter = this.getAttribute('data-filter');

                    // Apply filter
                    filterLibraries();
                });
            });

            // Sort options
            sortOptions.forEach(function(option) {
                option.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Update current sort
                    currentSort = this.getAttribute('data-sort');

                    // Update dropdown button text
                    const sortText = this.textContent;
                    document.getElementById('sortDropdown').textContent = 'Sort: ' + sortText;

                    // Apply sort
                    sortLibraries();
                });
            });

            // Reset search button
            resetSearchButton.addEventListener('click', resetSearch);

            // Back to top button
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.add('show');
                } else {
                    backToTopButton.classList.remove('show');
                }
            });

            backToTopButton.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // Handle Enter key in search box
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    filterLibraries();
                }
            });
        });
    </script>

    <!-- Analytics Script -->
    <script>
        window.addEventListener("load", function () {
            const path = window.location.pathname; // Get current page path
            let pageData = JSON.parse(localStorage.getItem("page_data")) || {}; // Get stored data or empty object

            // Increment count for the current path
            pageData[path] = pageData[path] ? pageData[path] + 1 : 1;

            // Store updated data back to localStorage
            localStorage.setItem("page_data", JSON.stringify(pageData));
        });

        // Function to send page view data
        function sendPageData() {
            const pageData = JSON.parse(localStorage.getItem("page_data")) || {};

            if (Object.keys(pageData).length > 0) {
                fetch(location.origin + "/librarian/track-page-view/", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRFToken": "{{ csrf_token }}",
                    },
                    body: JSON.stringify(pageData),
                })
                .then(() => {
                    localStorage.removeItem("page_data");
                })
                .catch((error) => console.error("Error sending page data:", error));
                localStorage.removeItem("page_data");
            } else {
                console.log("No page data to send");
            }
        }

        // Send data every 10 seconds
        setInterval(sendPageData, 10000);
    </script>
</body>

</html>