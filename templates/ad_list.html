{% extends "base.html" %}

{% block title %}Advertisements - Librainian{% endblock %}

{% block content %}
<div class="advertisements-content fade-in">
    <div class="container-fluid">
        <h1>Advertisements</h1>
        <a href="{% url 'advertisement_create' %}" class="btn btn-success mb-3">Create New Ad</a>
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Subject</th>
                    <th>Amount</th>
                    <th>Priority</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for ad in ads %}
                <tr>
                    <td>{{ ad.subject }}</td>
                    <td>₹{{ ad.amount }}</td>
                    <td>{{ ad.priority }}</td>
                    <td>{{ ad.status|yesno:"Active,Inactive" }}</td>
                    <td>
                        <a href="{% url 'advertisement_detail' ad.pk %}" class="btn btn-primary btn-sm">View</a>
                        <a href="{% url 'advertisement_update' ad.pk %}" class="btn btn-warning btn-sm">Edit</a>
                        <form method="POST" action="{% url 'advertisement_delete' ad.pk %}" style="display:inline-block;">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                        </form>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    </div>
</div>
{% endblock %}
