{% extends "base.html" %}

{% block title %}FCM Token Debug - Librainian{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3>🔧 FCM Token Debug & Registration</h3>
                    <p>Debug Firebase Cloud Messaging token generation and registration</p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h4>📱 Token Registration</h4>
                            <button id="requestPermissionBtn" class="btn btn-primary mb-3">Request Notification Permission</button>
                            <button id="getTokenBtn" class="btn btn-success mb-3">Get FCM Token</button>
                            <button id="registerTokenBtn" class="btn btn-warning mb-3" disabled>Register Token with Server</button>
                            
                            <div id="status" class="alert alert-info">
                                <strong>Status:</strong> Ready to start
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h4>📊 Debug Information</h4>
                            <div id="debugInfo" class="bg-light p-3 rounded">
                                <small>Debug information will appear here...</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <h4>🎫 Token Information</h4>
                            <div id="tokenInfo" class="bg-dark text-light p-3 rounded">
                                <small>Token will appear here...</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <h4>📝 Console Logs</h4>
                            <div id="consoleLogs" class="bg-secondary text-light p-3 rounded" style="height: 300px; overflow-y: auto;">
                                <small>Console logs will appear here...</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Firebase SDK -->
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-messaging.js"></script>

<script>
// Override console.log to capture logs
const originalLog = console.log;
const originalError = console.error;
const originalWarn = console.warn;

function addToConsoleLog(message, type = 'log') {
    const consoleLogs = document.getElementById('consoleLogs');
    const timestamp = new Date().toLocaleTimeString();
    const color = type === 'error' ? 'text-danger' : type === 'warn' ? 'text-warning' : 'text-light';
    consoleLogs.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
    consoleLogs.scrollTop = consoleLogs.scrollHeight;
}

console.log = function(...args) {
    originalLog.apply(console, args);
    addToConsoleLog(args.join(' '), 'log');
};

console.error = function(...args) {
    originalError.apply(console, args);
    addToConsoleLog(args.join(' '), 'error');
};

console.warn = function(...args) {
    originalWarn.apply(console, args);
    addToConsoleLog(args.join(' '), 'warn');
};

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyDw-BI4MFB1PKezXYPIZyee8IizHw2QDjI",
    authDomain: "librainian-app.firebaseapp.com",
    projectId: "librainian-app",
    storageBucket: "librainian-app.firebasestorage.app",
    messagingSenderId: "623132670328",
    appId: "1:623132670328:web:982c79e13e3cc69f3df08d",
    measurementId: "G-XNDKJL6JWH"
};

const vapidKey = "BFm8KEWYXyt703OsjQ4338IbyV72W3m6nndMoZhzRV9SlSj0UHMv4INixoql0AJLWh6LJKC1CrP3r_M8YqsGrAY";

let app, messaging, currentToken;

function updateStatus(message, type = 'info') {
    const status = document.getElementById('status');
    status.className = `alert alert-${type}`;
    status.innerHTML = `<strong>Status:</strong> ${message}`;
}

function updateDebugInfo(info) {
    const debugInfo = document.getElementById('debugInfo');
    debugInfo.innerHTML = `<pre>${JSON.stringify(info, null, 2)}</pre>`;
}

function updateTokenInfo(token) {
    const tokenInfo = document.getElementById('tokenInfo');
    if (token) {
        tokenInfo.innerHTML = `<strong>FCM Token:</strong><br><small>${token}</small>`;
    } else {
        tokenInfo.innerHTML = '<small>No token available</small>';
    }
}

// Initialize Firebase
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Initializing Firebase...');
    
    try {
        app = firebase.initializeApp(firebaseConfig);
        messaging = firebase.messaging();
        console.log('✅ Firebase initialized successfully');
        
        updateDebugInfo({
            firebaseApp: 'Initialized',
            messaging: 'Available',
            vapidKey: vapidKey.substring(0, 20) + '...',
            permission: Notification.permission
        });
        
        updateStatus('Firebase initialized. Ready to request permission.', 'success');
    } catch (error) {
        console.error('❌ Firebase initialization failed:', error);
        updateStatus('Firebase initialization failed: ' + error.message, 'danger');
    }
});

// Request permission
document.getElementById('requestPermissionBtn').addEventListener('click', function() {
    console.log('🔔 Requesting notification permission...');
    updateStatus('Requesting permission...', 'warning');
    
    Notification.requestPermission().then(function(permission) {
        console.log('📋 Permission result:', permission);
        
        if (permission === 'granted') {
            updateStatus('Permission granted! You can now get the FCM token.', 'success');
            document.getElementById('getTokenBtn').disabled = false;
        } else {
            updateStatus('Permission denied. Cannot get FCM token.', 'danger');
        }
        
        updateDebugInfo({
            permission: permission,
            timestamp: new Date().toISOString()
        });
    }).catch(function(error) {
        console.error('❌ Permission request failed:', error);
        updateStatus('Permission request failed: ' + error.message, 'danger');
    });
});

// Get FCM token
document.getElementById('getTokenBtn').addEventListener('click', function() {
    console.log('🎫 Getting FCM token...');
    updateStatus('Getting FCM token...', 'warning');
    
    messaging.getToken({ vapidKey: vapidKey }).then((token) => {
        console.log('📱 FCM Token received:', token);
        
        if (token) {
            currentToken = token;
            updateTokenInfo(token);
            updateStatus('FCM token received! You can now register it with the server.', 'success');
            document.getElementById('registerTokenBtn').disabled = false;
            
            updateDebugInfo({
                tokenReceived: true,
                tokenLength: token.length,
                tokenPreview: token.substring(0, 50) + '...',
                timestamp: new Date().toISOString()
            });
        } else {
            updateStatus('No FCM token available. Check service worker registration.', 'warning');
            updateDebugInfo({
                tokenReceived: false,
                reason: 'No token available',
                timestamp: new Date().toISOString()
            });
        }
    }).catch((error) => {
        console.error('❌ Error getting FCM token:', error);
        updateStatus('Error getting FCM token: ' + error.message, 'danger');
        updateDebugInfo({
            error: error.message,
            timestamp: new Date().toISOString()
        });
    });
});

// Register token with server
document.getElementById('registerTokenBtn').addEventListener('click', function() {
    if (!currentToken) {
        updateStatus('No token to register!', 'danger');
        return;
    }
    
    console.log('💾 Registering token with server...');
    updateStatus('Registering token with server...', 'warning');
    
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    
    fetch('/librarian/save-device-token/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': csrfToken
        },
        body: `token=${encodeURIComponent(currentToken)}&device_type=web`
    })
    .then(response => {
        console.log('📡 Server response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('✅ Server response:', data);
        
        if (data.status === 'success' || data.success) {
            updateStatus('Token registered successfully with server!', 'success');
        } else {
            updateStatus('Token registration failed: ' + data.message, 'warning');
        }
        
        updateDebugInfo({
            serverResponse: data,
            timestamp: new Date().toISOString()
        });
    })
    .catch(error => {
        console.error('❌ Error registering token:', error);
        updateStatus('Error registering token: ' + error.message, 'danger');
    });
});

// Register service worker
if ('serviceWorker' in navigator) {
    console.log('🔧 Registering service worker...');
    navigator.serviceWorker.register('/firebase-messaging-sw.js')
        .then(function(registration) {
            console.log('✅ Service Worker registered:', registration.scope);
        })
        .catch(function(error) {
            console.error('❌ Service Worker registration failed:', error);
        });
}
</script>
{% endblock %}
