<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advertisement Details</title>
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">


  
        
        <!-- disable Print Screen for Windows -->
    
          
        
        <!-- disable print screen for mac -->
    
          
    
        <!-- disabling print screen for Linux -->
    
  
    
        <!-- disabling inspection tool -->
    
          
        
    
        <style>
    
            body {
                -webkit-user-select: none; /* Disable text selection */
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
                margin: 0;
            }

            </style>
</head>
<body>

    <div class="container mt-5">
        <h1>{{ ad.subject }}</h1>
        <p><strong>Amount:</strong> ${{ ad.amount }}</p>
        <p><strong>Priority:</strong> {{ ad.priority }}</p>
        <p><strong>Status:</strong> {{ ad.status }}</p>
        <p><strong>Bid Amount:</strong> ${{ ad.bid_amount }}</p>

        <h3 class="mt-4">Place a Bid</h3>
        <form id="bid-form" method="POST" action="{% url 'place_bid' ad.id %}">
            {% csrf_token %}
            <div class="form-group">
                <label for="bid_amount">Bid Amount:</label>
                <input type="number" step="0.01" class="form-control" name="bid_amount" id="bid_amount" required>
            </div>
            <button type="submit" class="btn btn-success">Place Bid</button>
        </form>

        <div id="bid-result" class="mt-3"></div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
        document.getElementById('bid-form').onsubmit = function(event) {
            event.preventDefault();
            var formData = new FormData(this);
            var url = this.action;

            fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': formData.get('csrfmiddlewaretoken'),
                }
            })
            .then(response => response.json())
            .then(data => {
                var resultDiv = document.getElementById('bid-result');
                if (data.success) {
                    resultDiv.innerHTML = '<p class="text-success">' + data.message + '</p>';
                } else {
                    resultDiv.innerHTML = '<p class="text-danger">' + data.message + '</p>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        };
    </script>
</body>
</html>
