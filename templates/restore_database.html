<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="google" content="notranslate">
    <meta name="theme-color" content="#6366f1">
    <meta name="robots" content="noindex, nofollow">

    <title>Database Restoration - Librainian</title>

    <!-- Favicon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">

    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #10b981;
            --secondary-dark: #059669;
            --success: #22c55e;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #3b82f6;
            --light: #f8fafc;
            --dark: #1e293b;
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
        }

        * {
            box-sizing: border-box;
            transition: all 0.3s ease;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Glass Design Components */
        .glass-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
            position: relative;
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            overflow: hidden;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .glass-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            text-align: center;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .glass-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255,255,255,0.1);
            transform: rotate(-45deg);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) rotate(-45deg); }
            50% { transform: translateX(100%) rotate(-45deg); }
        }

        .glass-header h3 {
            margin: 0;
            font-weight: 700;
            font-size: 1.75rem;
            position: relative;
            z-index: 1;
        }

        .glass-body {
            padding: 2.5rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            font-size: 0.95rem;
        }

        .form-label i {
            margin-right: 0.75rem;
            color: var(--primary);
            font-size: 1.1rem;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 0.875rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .form-control:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            outline: none;
            background: rgba(255, 255, 255, 0.95);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        /* Button Styles */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(99, 102, 241, 0.3);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.2);
            transform: skewX(-45deg);
            transition: all 0.5s;
            z-index: -1;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
            color: white;
        }

        .back-btn {
            background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
            color: white;
            border: none;
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            text-align: center;
            display: block;
            text-decoration: none;
            margin-top: 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
        }

        .back-btn:hover {
            background: linear-gradient(135deg, var(--secondary-dark), var(--secondary));
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
            color: white;
            text-decoration: none;
        }

        /* Alert Styles */
        .alert {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-top: 1.5rem;
            color: var(--text-primary);
        }

        /* Footer */
        .glass-footer {
            text-align: center;
            padding: 2rem;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.875rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Custom file input styling */
        .custom-file-input {
            position: relative;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 0.875rem 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .custom-file-input:hover {
            background: rgba(255, 255, 255, 0.95);
        }

        .custom-file-input input[type=file] {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .custom-file-input .file-select-name {
            display: block;
            color: var(--text-secondary);
            font-size: 1rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .glass-container {
                padding: 1rem 0.5rem;
            }

            .glass-card {
                border-radius: 20px;
            }

            .glass-body {
                padding: 2rem 1.5rem;
            }

            .glass-header {
                padding: 1.5rem;
            }

            .glass-header h3 {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .glass-body {
                padding: 1.5rem 1rem;
            }

            .form-control {
                padding: 0.75rem;
            }
        }

        /* Disable zoom on mobile */
        @media (max-width: 768px) {
            body {
                touch-action: pan-x pan-y;
            }
        }
    </style>
</head>
<body class="modern-dashboard">
    <div class="glass-container">
        <div class="glass-card">
            <div class="glass-header">
                <h3><i class="fas fa-database me-2"></i>Database Restoration</h3>
            </div>
            <div class="glass-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="db_name" class="form-label">
                            <i class="fas fa-database"></i>
                            Database Name
                        </label>
                        <input type="text" class="form-control" id="db_name" name="db_name" required placeholder="Enter database name">
                    </div>

                    <div class="form-group">
                        <label for="db_user" class="form-label">
                            <i class="fas fa-user"></i>
                            Database User
                        </label>
                        <input type="text" class="form-control" id="db_user" name="db_user" required placeholder="Enter database user">
                    </div>

                    <div class="form-group">
                        <label for="db_password" class="form-label">
                            <i class="fas fa-lock"></i>
                            Database Password
                        </label>
                        <input type="password" class="form-control" id="db_password" name="db_password" required placeholder="Enter database password">
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="db_host" class="form-label">
                                    <i class="fas fa-server"></i>
                                    Database Host
                                </label>
                                <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" placeholder="Enter host">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="db_port" class="form-label">
                                    <i class="fas fa-plug"></i>
                                    Port
                                </label>
                                <input type="number" class="form-control" id="db_port" name="db_port" value="5432" placeholder="Port">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="backup_file" class="form-label">
                            <i class="fas fa-file-archive"></i>
                            Backup File
                        </label>
                        <div class="custom-file-input">
                            <input type="file" id="backup_file" name="backup_file" required accept=".sql,.dump,.backup">
                            <div class="file-select-name">Choose backup file...</div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-database me-2"></i>
                        Restore Database
                    </button>
                </form>

                <a href="/{{role}}/dashboard" class="back-btn">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Dashboard
                </a>

                {% if messages %}
                <div class="mt-4">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="glass-footer">
                <p>&copy; {{ current_year|default:"2024" }} Librainian | Database Management System</p>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5.3.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Modern file input handling
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('backup_file');
            const fileName = document.querySelector('.file-select-name');

            if (fileInput && fileName) {
                fileInput.addEventListener('change', function() {
                    const file = this.files[0];
                    if (file) {
                        fileName.textContent = file.name;
                        fileName.style.color = 'var(--text-primary)';
                    } else {
                        fileName.textContent = 'Choose backup file...';
                        fileName.style.color = 'var(--text-muted)';
                    }
                });
            }

            // Form validation
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const requiredFields = form.querySelectorAll('[required]');
                    let isValid = true;

                    requiredFields.forEach(field => {
                        if (!field.value.trim()) {
                            isValid = false;
                            field.style.borderColor = 'var(--danger)';
                        } else {
                            field.style.borderColor = '';
                        }
                    });

                    if (!isValid) {
                        e.preventDefault();
                        alert('Please fill in all required fields.');
                    }
                });
            }
        });
    </script>
</body>
</html>