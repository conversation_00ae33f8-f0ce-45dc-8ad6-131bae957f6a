<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Librainian!</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">


            <!-- Disable Right click -->

      
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
    <style>
        /* General body styling */
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            padding: 20px;

            font-family: Arial, sans-serif;
            line-height: 1.6;
            background-color: #f8f8f8;            
        }
        /* Container for email content */
        .container {
            max-width: 600px;
            margin: auto;
            background: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        /* Logo styling */
        .logo {
            width: 35%;
            float: left;
        }
        /* Clear float */
        .clear {
            clear: both;
        }
        /* Welcome image styling */
        .welcome-image {
            width: 100%;
            height: auto;
            margin-top: 20px;
            border-radius: 8px;
        }
        /* Centered text */
        .centered-text {
            text-align: center;
            font-size: 24px;  
            margin-top: 20px;
            margin-bottom: 20px;
        }
        /* Feature section */
        .feature-section {
            margin: 40px 0;
            text-align: center;
        }
        .feature-item {
            margin-bottom: 30px;
        }
        .feature-image {
            width: 100%;
            max-width: 350px; /* Constrain image size */
            height: auto;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .feature-text {
            font-size: 26px;
            color: #333;
            margin-top: 10px;
        }
        /* Footer styling */
        .footer {
            background-color: #000;
            color: #fff;
            text-align: center;
            /* padding: 10px; */
            border-radius: 0 0 8px 8px;
            margin-top: 20px;
        }
        .footer a {
            color: #fff;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        #lib {
            font-family: "Silkscreen", sans-serif;
            font-size: 20px;
        }

        /* Left-aligned text */
        .left-aligned-text {
            text-align: left;
            font-size: 24px;
        }
        .brain img {
            width: 250px;
            height: auto;
            margin-left: 33%;
            margin-right: 35%;
            margin-top: 1px;
            opacity: 0.6;
        }
        /* Responsive adjustments */
        @media (max-width: 600px) {
            .logo {
                width: 50%;
                float: none;
                display: block;
                margin: 0 auto;
            }
            .footer {
                padding: 20px;
            }
            .centered-text, .left-aligned-text {
                font-size: 18px;
            }
            .brain img {
                width: 60%;
                height: auto;
                margin-left: 20%;
                margin-top: 1px;
                opacity: 0.6;
            }

        }
    </style>
</head>
<body>

    <div class="container">
        <!-- Logo on top left -->
        <img src="/static/img/link_cover.jpg" alt="Librainian Logo" class="logo" loading="lazy" style="margin-left: 30%;">
        <div class="clear"></div>

        <!-- Welcome Image -->
        <img src="https://img.freepik.com/free-vector/stylish-welcome-lettering-banner-join-with-joy-happiness_1017-57675.jpg" alt="Welcome" class="welcome-image" loading="lazy">

        <!-- Centered Welcome Message -->
        <div class="centered-text">
            <p>Dear {{librarian.user.first_name}} {{librarian.user.last_name}},</p>
            <p>Welcome to Librainian! We are thrilled to have you with us.<br> Your library account named {{librarian.library_name}} is now ready to use with fantastic features waiting for you to explore.</p>
        </div>

        <!-- Feature Sections -->
        <div class="feature-section">
            <!-- Feature 1 -->
            <div class="feature-item">
                <img src="https://img.freepik.com/free-vector/coffee-break-concept-illustration_114360-3707.jpg" alt="Feature 1" class="feature-image" loading="lazy">
                <div class="feature-text">Related and easy to use. Everything on your fingertip.</div>
            </div>
            <!-- Feature 2 -->
            <div class="feature-item">
                <img src="https://img.freepik.com/free-vector/online-report-concept-illustration_114360-5508.jpg" alt="Feature 2" class="feature-image" loading="lazy">
                <div class="feature-text">Sit down and rest to get amazing analytics.</div>
            </div>
            <!-- Feature 3 -->
            <div class="feature-item">
                <img src="https://img.freepik.com/free-vector/hand-drawn-flat-design-api-illustration_52683-84601.jpg" alt="Feature 3" class="feature-image" loading="lazy">
                <div class="feature-text">Cross-platform availability.</div>
            </div>
        </div>

        <!-- Final Message -->
        <div class="left-aligned-text" style="margin-top: 20px;">
            <p>If you did not sign up for this account, please ignore this email.</p>
            <p>Best regards,<br>Librainian Team</p>
        </div>
    <!-- Footer -->
    <div class="footer">
        </div>
</div>
</body>
</html>
