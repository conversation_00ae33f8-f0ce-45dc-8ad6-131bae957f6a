{% extends "email_base.html" %}

{% block email_title %}New Support Ticket - Librainian{% endblock %}

{% block email_subject %}New Support Ticket Notification{% endblock %}

{% block email_description %}Notification about a new support ticket that requires attention from the library commander.{% endblock %}

{% block preview_text %}New support ticket #{{ ticket_number }} requires your attention. Issue: {{ type }}{% endblock %}

{% block header_icon %}🎫{% endblock %}

{% block email_header_title %}New Support Ticket{% endblock %}

{% block email_header_subtitle %}Ticket #{{ ticket_number }} requires your attention{% endblock %}


{% block extra_css %}
<style>
    /* Ticket-specific styles */
    .ticket-priority {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .priority-high {
        background-color: #fee2e2;
        color: #dc2626;
        border: 1px solid #fecaca;
    }

    .priority-medium {
        background-color: #fef3c7;
        color: #d97706;
        border: 1px solid #fde68a;
    }

    .priority-low {
        background-color: #dcfce7;
        color: #16a34a;
        border: 1px solid #bbf7d0;
    }

    .ticket-status {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border: 1px solid #0ea5e9;
        border-radius: 8px;
        color: #0c4a6e;
        font-weight: 600;
        font-size: 14px;
    }

    .description-box {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        font-style: italic;
        color: #475569;
        line-height: 1.6;
    }
</style>
{% endblock %}

{% block email_content %}
<h2 class="greeting">Hello Library Commander,</h2>
<p class="message">
    A new support ticket has been registered in the system and requires your immediate attention.
    Please review the details below and take appropriate action.
</p>

<!-- Ticket Details Card -->
<div class="info-card">
    <h3 class="card-title">🎫 Ticket Details</h3>

    <div class="detail-row">
        <span class="detail-label">Ticket Number</span>
        <span class="detail-value"><strong>#{{ ticket_number }}</strong></span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Issue Type</span>
        <span class="detail-value">{{ type }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Priority</span>
        <span class="detail-value">
            <span class="ticket-priority priority-{{ priority|default:'medium'|lower }}">
                {{ priority|default:'Medium' }}
            </span>
        </span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Status</span>
        <span class="detail-value">
            <span class="ticket-status">
                🔄 Open
            </span>
        </span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Subject</span>
        <span class="detail-value">{{ subject }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Description</span>
        <div class="detail-value">
            <div class="description-box">{{ description }}</div>
        </div>
    </div>

    {% if submitted_by %}
    <div class="detail-row">
        <span class="detail-label">Submitted By</span>
        <span class="detail-value">{{ submitted_by }}</span>
    </div>
    {% endif %}

    {% if submitted_at %}
    <div class="detail-row">
        <span class="detail-label">Submitted At</span>
        <span class="detail-value">{{ submitted_at|date:"F j, Y, g:i a" }}</span>
    </div>
    {% endif %}
</div>

<!-- Action Section -->
<div class="button-container">
    <p class="message" style="text-align: center; margin-bottom: 20px;">
        Click the button below to view and manage this ticket in the admin panel.
    </p>
    <a href="{{ ticket_url|default:'#' }}" class="action-button">
        🔍 View Ticket Details
    </a>
</div>

<p style="margin: 30px 0 0 0; color: #6b7280; font-size: 14px; text-align: center;">
    This is an automated notification. Please do not reply to this email.
</p>
{% endblock %}


