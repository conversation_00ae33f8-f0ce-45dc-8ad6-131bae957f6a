<!DOCTYPE html>
<html lang="en">
<head>

<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5L8WZWCC');</script>
<!-- End Google Tag Manager -->


    <!-- Immediate Dark Mode Detection -->
    <script>
        (function() {
            if (localStorage.getItem('darkMode') === 'enabled') {
                document.documentElement.classList.add('dark-mode-immediate');
                document.body.classList.add('dark-mode');
            }
        })();
    </script>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="google" content="notranslate">
    <meta name="theme-color" content="#6366f1" id="theme-color-meta">
    <meta name="apple-mobile-web-app-status-bar-style" content="default" id="status-bar-meta">

    <title>{% block title %}Librainian Dashboard{% endblock %}</title>
    
    <!-- Favicon -->
    <link href="/static/favicon.ico" rel="icon">
    
    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">
    
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Include code template for common styles and scripts -->
    {% include "code_temp.html" %}
    
    <!-- Page specific styles -->
    {% block extra_css %}{% endblock %}
</head>
<body class="modern-dashboard">
 
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5L8WZWCC"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->


    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p class="loading-text" id="loading-text">Loading...</p>
        </div>
    </div>

    <!-- Immediate dark mode styles for loading screen -->
    <style>
        html.dark-mode-immediate .loading-screen,
        body.dark-mode .loading-screen {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
        }

        html.dark-mode-immediate .loading-spinner,
        body.dark-mode .loading-spinner {
            color: rgba(255, 255, 255, 0.95) !important;
        }

        html.dark-mode-immediate .spinner,
        body.dark-mode .spinner {
            border: 4px solid rgba(255, 255, 255, 0.2) !important;
            border-top: 4px solid rgba(255, 255, 255, 0.9) !important;
        }

        /* GLOBAL BOOTSTRAP PAGINATION OVERRIDE - Unset ALL Bootstrap Pagination Styles */
        .pagination,
        .pagination li,
        .pagination li a,
        .pagination li span,
        .pagination .page-item,
        .pagination .page-link,
        .dataTables_wrapper .dataTables_paginate,
        .dataTables_wrapper .dataTables_paginate .paginate_button,
        .dataTables_wrapper .dataTables_paginate .paginate_button:link,
        .dataTables_wrapper .dataTables_paginate .paginate_button:visited,
        .dataTables_wrapper .dataTables_paginate .paginate_button:active,
        .dataTables_wrapper .dataTables_paginate .paginate_button:focus,
        .dataTables_wrapper .dataTables_paginate .paginate_button:hover,
        .dataTables_paginate,
        .dataTables_paginate .paginate_button,
        .dataTables_paginate .paginate_button:link,
        .dataTables_paginate .paginate_button:visited,
        .dataTables_paginate .paginate_button:active,
        .dataTables_paginate .paginate_button:focus,
        .dataTables_paginate .paginate_button:hover,
        .paginate_button,
        .paginate_button:link,
        .paginate_button:visited,
        .paginate_button:active,
        .paginate_button:focus,
        .paginate_button:hover,
        a.paginate_button,
        a.paginate_button:link,
        a.paginate_button:visited,
        a.paginate_button:active,
        a.paginate_button:focus,
        a.paginate_button:hover {
            /* Unset ALL Bootstrap pagination properties */
            padding: unset !important;
            margin: unset !important;
            font-size: unset !important;
            font-weight: unset !important;
            line-height: unset !important;
            color: #fff !important;
            text-align: unset !important;
            text-decoration: unset !important;
            vertical-align: unset !important;
            border: unset !important;
            border-radius: unset !important;
            background: unset !important;
            background-color: unset !important;
            background-image: unset !important;
            box-shadow: unset !important;
            cursor: unset !important;
            display: unset !important;
            position: unset !important;
            z-index: unset !important;
            outline: unset !important;
            transition: unset !important;
            transform: unset !important;
            opacity: unset !important;
            pointer-events: unset !important;
        }

        /* GLOBAL Custom Glassmorphism Pagination Styling */
        .dataTables_wrapper .dataTables_paginate,
        .dataTables_paginate {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            gap: 0.25rem !important;
            margin-top: 1.5rem !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button,
        .dataTables_paginate .paginate_button,
        .paginate_button,
        a.paginate_button {
            background: transparent !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: 8px !important;
            margin: 0 2px !important;
            padding: 0.5rem 0.75rem !important;
            color: white !important;
            font-weight: 500 !important;
            font-size: 0.875rem !important;
            text-decoration: none !important;
            transition: all 0.2s ease !important;
            box-shadow: none !important;
            min-width: 40px !important;
            text-align: center !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            outline: none !important;
            cursor: pointer !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button:hover,
        .dataTables_paginate .paginate_button:hover,
        .paginate_button:hover,
        a.paginate_button:hover {
            background: rgba(255, 255, 255, 0.1) !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
            color: white !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1) !important;
            text-decoration: none !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        .dataTables_paginate .paginate_button.current,
        .paginate_button.current,
        a.paginate_button.current {
            background: rgba(99, 102, 241, 0.3) !important;
            border-color: rgba(99, 102, 241, 0.6) !important;
            color: white !important;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2) !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover,
        .dataTables_paginate .paginate_button.current:hover,
        .paginate_button.current:hover,
        a.paginate_button.current:hover {
            background: rgba(99, 102, 241, 0.4) !important;
            border-color: rgba(99, 102, 241, 0.7) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.3) !important;
            color: white !important;
            text-decoration: none !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
        .dataTables_paginate .paginate_button.disabled,
        .paginate_button.disabled,
        a.paginate_button.disabled {
            background: transparent !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            color: rgba(255, 255, 255, 0.4) !important;
            cursor: not-allowed !important;
            transform: none !important;
            box-shadow: none !important;
            opacity: 0.5 !important;
            pointer-events: none !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover,
        .dataTables_paginate .paginate_button.disabled:hover,
        .paginate_button.disabled:hover,
        a.paginate_button.disabled:hover {
            background: transparent !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            color: rgba(255, 255, 255, 0.4) !important;
            transform: none !important;
            box-shadow: none !important;
            opacity: 0.5 !important;
            text-decoration: none !important;
        }

        /* Previous and Next button styling */
        .dataTables_wrapper .dataTables_paginate .paginate_button.previous,
        .dataTables_wrapper .dataTables_paginate .paginate_button.next,
        .dataTables_paginate .paginate_button.previous,
        .dataTables_paginate .paginate_button.next,
        .paginate_button.previous,
        .paginate_button.next {
            min-width: 70px !important;
            font-weight: 600 !important;
        }

        /* GLOBAL Dark mode adjustments */
        body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button,
        body.dark-mode .dataTables_paginate .paginate_button,
        body.dark-mode .paginate_button,
        body.dark-mode a.paginate_button {
            background: transparent !important;
            border-color: rgba(255, 255, 255, 0.25) !important;
            color: rgba(255, 255, 255, 0.9) !important;
        }

        body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button:hover,
        body.dark-mode .dataTables_paginate .paginate_button:hover,
        body.dark-mode .paginate_button:hover,
        body.dark-mode a.paginate_button:hover {
            background: rgba(255, 255, 255, 0.08) !important;
            border-color: rgba(255, 255, 255, 0.4) !important;
            color: white !important;
        }

        body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        body.dark-mode .dataTables_paginate .paginate_button.current,
        body.dark-mode .paginate_button.current,
        body.dark-mode a.paginate_button.current {
            background: rgba(99, 102, 241, 0.25) !important;
            border-color: rgba(99, 102, 241, 0.5) !important;
            color: white !important;
        }

        body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
        body.dark-mode .dataTables_paginate .paginate_button.disabled,
        body.dark-mode .paginate_button.disabled,
        body.dark-mode a.paginate_button.disabled {
            background: transparent !important;
            border-color: rgba(255, 255, 255, 0.15) !important;
            color: rgba(255, 255, 255, 0.3) !important;
            opacity: 0.4 !important;
        }
    </style>

    <!-- Main Layout Container -->
    <div class="dashboard-layout" id="dashboard-layout">
        <!-- Desktop Sidebar -->
        <div class="sidebar-desktop" id="sidebar-desktop">
            {% include "sidebar.html" %}
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebar-overlay"></div>
        
        <!-- Mobile Sidebar -->
        <div class="sidebar-mobile" id="sidebar-mobile">
            {% include "sidebar.html" %}
        </div>

        <!-- Main Content Area -->
        <div class="main-content" id="main-content">
            <!-- Top Bar -->
            <div class="topbar-container">
                {% include "topbar.html" %}
            </div>

            <!-- Page Content -->
            <div class="page-content">
                {% block content %}
                {% endblock %}
            </div>
        </div>

        <!-- Mobile Bottom Menu -->
        <div class="bottom-menu-mobile d-lg-none">
            {% include "bottom_menu.html" %}
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
        <!-- Toasts will be dynamically added here -->
    </div>

    <!-- Bootstrap 5.3.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- jQuery (for DataTables) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Dashboard Core JavaScript -->
    <script>
        // Modern Dashboard Core
        class ModernDashboard {
            constructor() {
                this.init();
            }

            init() {
                this.setDynamicLoadingText();
                this.setupEventListeners();
                this.initializeComponents();
                this.hideLoadingScreen();
            }

            setupEventListeners() {
                // Mobile menu toggle
                const mobileMenuBtn = document.getElementById('mobile-menu-btn');
                const sidebarOverlay = document.getElementById('sidebar-overlay');
                const sidebarMobile = document.getElementById('sidebar-mobile');

                if (mobileMenuBtn) {
                    mobileMenuBtn.addEventListener('click', () => this.toggleMobileSidebar());
                }

                if (sidebarOverlay) {
                    sidebarOverlay.addEventListener('click', () => this.closeMobileSidebar());
                }

                // Responsive handling
                window.addEventListener('resize', () => this.handleResize());
            }

            toggleMobileSidebar() {
                const overlay = document.getElementById('sidebar-overlay');
                const sidebar = document.getElementById('sidebar-mobile');
                
                if (overlay && sidebar) {
                    overlay.classList.toggle('active');
                    sidebar.classList.toggle('active');
                    document.body.classList.toggle('sidebar-open');
                }
            }

            closeMobileSidebar() {
                const overlay = document.getElementById('sidebar-overlay');
                const sidebar = document.getElementById('sidebar-mobile');
                
                if (overlay && sidebar) {
                    overlay.classList.remove('active');
                    sidebar.classList.remove('active');
                    document.body.classList.remove('sidebar-open');
                }
            }

            handleResize() {
                // Close mobile sidebar on desktop
                if (window.innerWidth >= 992) {
                    this.closeMobileSidebar();
                }
            }

            initializeComponents() {
                // Initialize tooltips
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });

                // Initialize popovers
                const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
                popoverTriggerList.map(function (popoverTriggerEl) {
                    return new bootstrap.Popover(popoverTriggerEl);
                });
            }

            setDynamicLoadingText() {
                const loadingTextElement = document.getElementById('loading-text');
                if (!loadingTextElement) return;

                // Get page title from document title or URL
                let pageTitle = document.title;
                let loadingText = 'Loading...';

                // Extract meaningful page name from title
                if (pageTitle.includes(' - ')) {
                    const titleParts = pageTitle.split(' - ');
                    const pageName = titleParts[0];

                    // Handle specific page types - Check more specific terms first
                    if (pageName.toLowerCase().includes('marketing dashboard')) {
                        loadingText = 'Loading Marketing...';
                    } else if (pageName.toLowerCase().includes('analytics dashboard')) {
                        loadingText = 'Loading Analytics...';
                    } else if (pageName.toLowerCase().includes('complaint management dashboard')) {
                        loadingText = 'Loading Complaints...';
                    } else if (pageName.toLowerCase().includes('marketing')) {
                        loadingText = 'Loading Marketing...';
                    } else if (pageName.toLowerCase().includes('analytics')) {
                        loadingText = 'Loading Analytics...';
                    } else if (pageName.toLowerCase().includes('complaint')) {
                        loadingText = 'Loading Complaints...';
                    } else if (pageName.toLowerCase().includes('student')) {
                        loadingText = 'Loading Students...';
                    } else if (pageName.toLowerCase().includes('transaction')) {
                        loadingText = 'Loading Transactions...';
                    } else if (pageName.toLowerCase().includes('visitor')) {
                        loadingText = 'Loading Visitors...';
                    } else if (pageName.toLowerCase().includes('seat')) {
                        loadingText = 'Loading Seat Selection...';
                    } else if (pageName.toLowerCase().includes('blog')) {
                        loadingText = 'Loading Blog...';
                    } else if (pageName.toLowerCase().includes('download')) {
                        loadingText = 'Loading Downloads...';
                    } else if (pageName.toLowerCase().includes('membership')) {
                        loadingText = 'Loading Membership...';
                    } else if (pageName.toLowerCase().includes('wallet')) {
                        loadingText = 'Loading Wallet...';
                    } else if (pageName.toLowerCase().includes('backup')) {
                        loadingText = 'Loading Backups...';
                    } else if (pageName.toLowerCase().includes('profile')) {
                        loadingText = 'Loading Profile...';
                    } else if (pageName.toLowerCase().includes('settings')) {
                        loadingText = 'Loading Settings...';
                    } else if (pageName.toLowerCase().includes('help')) {
                        loadingText = 'Loading Help...';
                    } else if (pageName.toLowerCase().includes('coupon')) {
                        loadingText = 'Loading Coupons...';
                    } else if (pageName.toLowerCase().includes('report')) {
                        loadingText = 'Loading Reports...';
                    } else if (pageName.toLowerCase().includes('table')) {
                        loadingText = 'Loading Table...';
                    } else if (pageName.toLowerCase().includes('pending')) {
                        loadingText = 'Loading Pending Items...';
                    } else if (pageName.toLowerCase().includes('advertisement')) {
                        loadingText = 'Loading Advertisements...';
                    } else if (pageName.toLowerCase().includes('dashboard')) {
                        loadingText = 'Loading Dashboard...';
                    } else {
                        // Use the page name directly
                        loadingText = `Loading ${pageName}...`;
                    }
                } else {
                    // Fallback: try to get page name from URL
                    const path = window.location.pathname;
                    const segments = path.split('/').filter(segment => segment && segment !== 'librarian' && segment !== 'sublibrarian');

                    if (segments.length > 0) {
                        const lastSegment = segments[segments.length - 1];
                        const pageName = lastSegment.replace(/[-_]/g, ' ')
                            .split(' ')
                            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                            .join(' ');
                        loadingText = `Loading ${pageName}...`;
                    }
                }

                loadingTextElement.textContent = loadingText;
            }

            hideLoadingScreen() {
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    setTimeout(() => {
                        loadingScreen.style.opacity = '0';
                        setTimeout(() => {
                            loadingScreen.style.display = 'none';
                        }, 300);
                    }, 500);
                }
            }

            // Utility methods
            showToast(title, message, type = 'info') {
                const toastContainer = document.querySelector('.toast-container');
                const toastId = 'toast-' + Date.now();
                
                const toastHTML = `
                    <div class="toast" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="toast-header">
                            <i class="fas fa-${this.getToastIcon(type)} me-2 text-${type}"></i>
                            <strong class="me-auto">${title}</strong>
                            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                        </div>
                        <div class="toast-body">${message}</div>
                    </div>
                `;
                
                toastContainer.insertAdjacentHTML('beforeend', toastHTML);
                const toast = new bootstrap.Toast(document.getElementById(toastId));
                toast.show();
            }

            getToastIcon(type) {
                const icons = {
                    'success': 'check-circle',
                    'danger': 'exclamation-triangle',
                    'warning': 'exclamation-circle',
                    'info': 'info-circle'
                };
                return icons[type] || 'info-circle';
            }

            // Dynamic page loading
            loadPage(pageName) {
                const mainContent = document.querySelector('.main-content');
                if (!mainContent) return;

                // Show loading state with page name
                this.showLoadingState(mainContent, pageName);

                // Define page templates
                const pages = {
                    'profile': this.getProfileTemplate(),
                    'help': this.getHelpTemplate()
                };

                // Load page content
                setTimeout(() => {
                    if (pages[pageName]) {
                        mainContent.innerHTML = pages[pageName];
                        this.initializePageScripts(pageName);
                        // Removed success toast message
                    } else {
                        this.showToast('Error', 'Page not found.', 'danger');
                    }
                }, 500);
            }

            showLoadingState(container, pageName = null) {
                let loadingText = 'Loading page...';
                if (pageName) {
                    loadingText = `Loading ${pageName.charAt(0).toUpperCase() + pageName.slice(1)}...`;
                }

                container.innerHTML = `
                    <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
                        <div class="text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="text-muted">${loadingText}</p>
                        </div>
                    </div>
                `;
            }

            getProfileTemplate() {
                return `
                    <div class="profile-content fade-in">
                        <div class="row g-4">
                            <div class="col-12">
                                <div class="modern-card">
                                    <div class="modern-card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-user me-2"></i>
                                            User Profile
                                        </h5>
                                    </div>
                                    <div class="modern-card-body">
                                        <div class="row">
                                            <div class="col-md-4 text-center">
                                                <div class="profile-avatar mb-3">
                                                    U
                                                </div>
                                                <h5>User Profile</h5>
                                                <p class="text-muted">{{ role|title }}</p>
                                            </div>
                                            <div class="col-md-8">
                                                <h6>Profile Information</h6>
                                                <p>This is a simplified profile view. Click the Profile link in the sidebar for the full profile page.</p>
                                                <div class="mt-3">
                                                    <button class="btn-primary-modern" onclick="window.location.href='/{{ role }}/profile/'">
                                                        <i class="fas fa-external-link-alt me-1"></i>
                                                        Open Full Profile
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <style>
                        .profile-avatar {
                            width: 100px;
                            height: 100px;
                            background: linear-gradient(135deg, var(--primary), var(--primary-light));
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                            font-size: 2rem;
                            font-weight: 700;
                            margin: 0 auto;
                        }
                    </style>
                `;
            }

            getHelpTemplate() {
                return `
                    <div class="help-content fade-in">
                        <div class="row g-4">
                            <div class="col-12">
                                <div class="modern-card">
                                    <div class="modern-card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-question-circle me-2"></i>
                                            Help & Support
                                        </h5>
                                    </div>
                                    <div class="modern-card-body">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <h6>Quick Help</h6>
                                                <p>This is a simplified help view. Click the Help link in the sidebar for the full help page.</p>
                                                <div class="help-articles">
                                                    <div class="help-article mb-3 p-3 border rounded">
                                                        <h6>How do I add a new student?</h6>
                                                        <p class="text-muted">Navigate to the Students section and click "Add New Student".</p>
                                                    </div>
                                                    <div class="help-article mb-3 p-3 border rounded">
                                                        <h6>How do I process payments?</h6>
                                                        <p class="text-muted">Go to the Transactions section to view and process payments.</p>
                                                    </div>
                                                </div>
                                                <button class="btn-primary-modern" onclick="window.location.href='/{{ role }}/help/'">
                                                    <i class="fas fa-external-link-alt me-1"></i>
                                                    Open Full Help Page
                                                </button>
                                            </div>
                                            <div class="col-md-4">
                                                <h6>Contact Support</h6>
                                                <div class="support-options">
                                                    <button class="btn-secondary-modern w-100 mb-2" onclick="window.location.href='mailto:<EMAIL>'">
                                                        <i class="fas fa-envelope me-1"></i>
                                                        Email Support
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            initializePageScripts(pageName) {
                // Initialize page-specific functionality
                // Removed console.log message
            }
        }

        // Dark mode styles function
        function applyDarkModeStyles(isDarkMode) {
            const root = document.documentElement;

            // Update browser theme colors
            updateBrowserTheme(isDarkMode);

            if (isDarkMode) {
                // Dark mode color overrides
                root.style.setProperty('--bg-primary', '#111827');
                root.style.setProperty('--bg-secondary', '#1f2937');
                root.style.setProperty('--bg-tertiary', '#374151');
                root.style.setProperty('--text-primary', '#ffffff');
                root.style.setProperty('--text-secondary', '#d1d5db');
                root.style.setProperty('--text-muted', '#9ca3af');
                root.style.setProperty('--border-color', '#4b5563');
                root.style.setProperty('--shadow-color', 'rgba(0, 0, 0, 0.3)');

                // Dark glassmorphism
                root.style.setProperty('--glass-bg', 'rgba(255, 255, 255, 0.05)');
                root.style.setProperty('--glass-border', 'rgba(255, 255, 255, 0.1)');
                root.style.setProperty('--glass-shadow', '0 8px 32px rgba(0, 0, 0, 0.3)');

                // Dark gradients
                root.style.setProperty('--gradient-hero', 'linear-gradient(135deg, #1f2937 0%, #111827 100%)');
                root.style.setProperty('--gradient-glass', 'linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%)');
            } else {
                // Light mode (reset to defaults)
                root.style.setProperty('--bg-primary', '#ffffff');
                root.style.setProperty('--bg-secondary', '#f9fafb');
                root.style.setProperty('--bg-tertiary', '#f3f4f6');
                root.style.setProperty('--text-primary', '#111827');
                root.style.setProperty('--text-secondary', '#4b5563');
                root.style.setProperty('--text-muted', '#9ca3af');
                root.style.setProperty('--border-color', '#e5e7eb');
                root.style.setProperty('--shadow-color', 'rgba(0, 0, 0, 0.1)');

                // Light glassmorphism
                root.style.setProperty('--glass-bg', 'rgba(255, 255, 255, 0.15)');
                root.style.setProperty('--glass-border', 'rgba(255, 255, 255, 0.3)');
                root.style.setProperty('--glass-shadow', '0 8px 32px rgba(0, 0, 0, 0.1)');

                // Light gradients
                root.style.setProperty('--gradient-hero', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
                root.style.setProperty('--gradient-glass', 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)');
            }
        }

        function updateBrowserTheme(isDarkMode) {
            // Update theme-color meta tag
            const themeColorMeta = document.getElementById('theme-color-meta');
            const statusBarMeta = document.getElementById('status-bar-meta');

            if (themeColorMeta) {
                themeColorMeta.setAttribute('content', isDarkMode ? '#111827' : '#6366f1');
            }

            if (statusBarMeta) {
                statusBarMeta.setAttribute('content', isDarkMode ? 'black-translucent' : 'default');
            }
        }

        // Initialize dashboard when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Add modern dashboard class to body
            document.body.classList.add('modern-dashboard');

            // Initialize dark mode from localStorage
            const darkModeEnabled = localStorage.getItem('darkMode') === 'enabled';
            if (darkModeEnabled) {
                document.body.classList.add('dark-mode');
                applyDarkModeStyles(true);
            } else {
                // Ensure light mode theme is set
                updateBrowserTheme(false);
            }

            // Initialize dashboard
            window.modernDashboard = new ModernDashboard();
        });
    </script>

    <!-- Page specific scripts -->
    {% block extra_js %}{% endblock %}

    <!-- Global Floating Add Student Button -->
    {% include "floating_add_student.html" %}
</body>
</html>
