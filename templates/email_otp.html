{% extends "email_base.html" %}

{% block email_title %}Your Security Code - Librainian{% endblock %}

{% block email_subject %}Security Verification Code{% endblock %}

{% block email_description %}One-time password for secure access verification to your Librainian account.{% endblock %}

{% block preview_text %}Your security code: {{ otp }}. Valid for 10 minutes only.{% endblock %}

{% block header_icon %}🔐{% endblock %}

{% block email_header_title %}Security Verification{% endblock %}

{% block email_header_subtitle %}Your one-time password for secure access{% endblock %}

{% block extra_css %}
<style>
    /* OTP-specific styles */
    .otp-container {
        background: linear-gradient(135deg, #f8f9ff 0%, #e8eaff 100%);
        border-radius: 16px;
        padding: 40px 30px;
        text-align: center;
        margin: 30px 0;
        border: 2px solid #6366f1;
        position: relative;
        overflow: hidden;
    }

    .otp-container::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.1), transparent);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(30deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(30deg); }
    }

    .otp-title {
        font-size: 20px;
        font-weight: 700;
        color: #1f2937;
        margin: 0 0 20px 0;
        position: relative;
        z-index: 2;
        font-family: 'Comfortaa', sans-serif;
    }

    .otp-code {
        font-size: 48px;
        font-weight: 800;
        color: #6366f1;
        margin: 20px 0;
        letter-spacing: 8px;
        font-family: 'Courier New', monospace;
        text-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
        position: relative;
        z-index: 2;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .otp-note {
        font-size: 16px;
        color: #6b7280;
        margin: 0;
        position: relative;
        z-index: 2;
        font-weight: 500;
    }

    .timer-section {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border-radius: 12px;
        padding: 20px;
        margin: 25px 0;
        text-align: center;
        border: 1px solid #f59e0b;
    }

    .timer-icon {
        font-size: 24px;
        margin-bottom: 10px;
        display: block;
    }

    .timer-text {
        font-size: 18px;
        font-weight: 600;
        color: #92400e;
        margin: 0 0 5px 0;
        font-family: 'Comfortaa', sans-serif;
    }

    .timer-subtext {
        font-size: 14px;
        color: #a16207;
        margin: 0;
        font-weight: 500;
    }

    .security-tips {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border-radius: 12px;
        padding: 20px;
        margin: 25px 0;
        border: 1px solid #22c55e;
    }

    .security-tips h4 {
        font-size: 16px;
        font-weight: 600;
        color: #15803d;
        margin: 0 0 10px 0;
        font-family: 'Comfortaa', sans-serif;
    }

    .security-tips ul {
        margin: 0;
        padding-left: 20px;
        color: #166534;
        font-size: 14px;
    }

    .security-tips li {
        margin-bottom: 5px;
    }

    .warning-box {
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        border: 1px solid #ef4444;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
        text-align: center;
    }

    .warning-text {
        color: #dc2626;
        font-size: 14px;
        font-weight: 600;
        margin: 0;
    }
</style>
{% endblock %}

{% block email_content %}
<h2 class="greeting">Hello!</h2>
<p class="message">
    We received a request to verify your identity. Please use the security code below to complete your verification process.
</p>

<!-- OTP Section -->
<div class="otp-container">
    <h3 class="otp-title">Your Security Code</h3>
    <p class="otp-code">{{ otp }}</p>
    <p class="otp-note">Enter this code to continue</p>
</div>

<!-- Timer Section -->
<div class="timer-section">
    <div class="timer-icon">⏰</div>
    <p class="timer-text">Valid for 10 minutes only</p>
    <p class="timer-subtext">This code will expire automatically for your security</p>
</div>

<!-- Security Tips -->
<div class="security-tips">
    <h4>🛡️ Security Tips</h4>
    <ul>
        <li>Never share this code with anyone</li>
        <li>Librainian will never ask for your code via phone or email</li>
        <li>If you didn't request this code, please ignore this email</li>
        <li>Use this code only on the official Librainian website</li>
    </ul>
</div>

<!-- Warning -->
<div class="warning-box">
    <p class="warning-text">
        ⚠️ If you didn't request this verification code, please contact our support team immediately.
    </p>
</div>

<p style="margin: 30px 0 0 0; color: #6b7280; font-size: 14px; text-align: center;">
    This is an automated security message. Please do not reply to this email.
</p>
{% endblock %}


