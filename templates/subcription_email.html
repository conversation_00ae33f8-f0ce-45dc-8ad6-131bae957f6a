<!DOCTYPE html>
<html lang="en" translate="no">

<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">

    <title>Welcome to Our Library</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">


            <!-- Disable Right click -->

      
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      

    <style>
        /* Custom styles can be added here */
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            /* font-family: Arial, sans-serif; */
            font-family: "Dancing Script", cursive;
            background-color: #f8f9fa;
            padding: 20px;

        }

        .box {
            /* max-width: 600px; */
            width: 60%;
            height: auto;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0px 0px 10px 7px rgba(0, 0, 0, 0.1);

        }

        .box-heading {
            width: 100%;
            height: auto;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .box-heading img {
            width: 350px;
        }

        .email-footer {
            text-align: center;
        }

        h1 {
            text-align: center;
            margin-top: 20px;
        }

        p {
            font-size: 18px;
            margin-top: 10px;
            /* font-family: "Dancing Script", cursive; */
        }
        #lib {
            font-size: 20px;
            font-family: "Silkscreen", sans-serif;
            color: gray;
            opacity: 40%;
        }
        .email-footer p{
            font-size: 12px;
        }
        @media screen and (max-width: 768px) {
            body {
                background-position: center;
                background-size: cover;
            }

            .box {
                width: 100% !important;
                height: auto;
            }

            .box-heading {
                width: 100%;
            }

            .box-heading h1 {
                font-size: 22px;
            }

            p {
                font-size: 12px;
            }

        }
    </style>
</head>

<body>

    <div class="box">
        <div class="box-heading">
            <!-- <h1>Welcome to Our Library</h1> -->
            <img src="/static/img/library_list/f94d520a-603b-42fc-96d0-77cfd1cf06e4.jpg" alt="" loading="lazy">
        </div>
        <h3 class="mt-2">Hello,<br> {{ email }}</h3>
        <p>Thank you for subscribing to our newsletter! We’re excited to have you on board.</p>
        <p>You will now receive regular updates on our latest products, offers, and exclusive content directly to your
            inbox.</p>
        <p>If you have any questions, feel free to <a href="mailto:<EMAIL>">contact us</a>.</p>
        <p>Best regards,<br> The Librainian Team</p>
        <!-- <a href="[Subscription Confirmation Link]" class="email-button">Confirm Subscription</a> -->
    </div>
    <div class="email-footer">
        <p id="lib">LIBRAINIAN</p>
        <p>If you want to received mail from us please <a href="#">unsubscribe</a>.</p>

    </div>
    <!-- Bootstrap JS and dependencies (optional) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
     
     
</body>

</html>