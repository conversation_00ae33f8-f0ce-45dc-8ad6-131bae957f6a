<!DOCTYPE html>
<html lang="en">
<head>
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5L8WZWCC');</script>
<!-- End Google Tag Manager -->

    {% load static %}
    {% load socialaccount %}
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Basic Meta Tags -->
    <title>Create Account - Librainian</title>
    <meta name="description" content="Create your Librainian account to start managing your library efficiently and securely.">
    <meta name="robots" content="noindex, nofollow">
    <meta name="theme-color" content="#6366f1" id="theme-color-meta">
    <meta name="apple-mobile-web-app-status-bar-style" content="default" id="status-bar-meta">

    <!-- Modern Typography -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">

    <!-- WebPage Schema for Signup -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Create Account - Librainian Library Management System",
        "description": "Create your Librainian account to start managing your library efficiently and securely. Join 600+ libraries worldwide using our trusted platform.",
        "url": "https://librainian.com/{% if role == 'sublibrarian' %}sublibrarian{% elif role == 'librarycommander' %}librarycommander{% else %}librarian{% endif %}/signup/",
        "isPartOf": {
            "@type": "WebSite",
            "name": "Librainian",
            "url": "https://librainian.com/"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "logo": {
                "@type": "ImageObject",
                "url": "https://librainian.com/static/img/logo_trans_name.png",
                "width": 300,
                "height": 100
            },
            "url": "https://librainian.com/"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Librainian Registration Portal",
            "description": "Account creation portal for library management system access",
            "applicationCategory": "Library Management",
            "operatingSystem": "Web Browser"
        },
        "potentialAction": {
            "@type": "RegisterAction",
            "target": {
                "@type": "EntryPoint",
                "urlTemplate": "https://librainian.com/{% if role == 'sublibrarian' %}sublibrarian{% elif role == 'librarycommander' %}librarycommander{% else %}librarian{% endif %}/signup/",
                "actionPlatform": [
                    "https://schema.org/DesktopWebPlatform",
                    "https://schema.org/MobileWebPlatform"
                ]
            }
        }
    }
    </script>

    <!-- BreadcrumbList Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://librainian.com/"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "{% if role == 'sublibrarian' %}Sub-Librarian{% elif role == 'librarycommander' %}Library Commander{% else %}Librarian{% endif %} Signup",
                "item": "https://librainian.com/{% if role == 'sublibrarian' %}sublibrarian{% elif role == 'librarycommander' %}librarycommander{% else %}librarian{% endif %}/signup/"
            }
        ]
    }
    </script>

    <!-- Service Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Service",
        "name": "Librainian Account Registration",
        "description": "Professional library management account creation with secure authentication and comprehensive features.",
        "provider": {
            "@type": "Organization",
            "name": "Librainian",
            "url": "https://librainian.com/"
        },
        "serviceType": "Account Registration",
        "areaServed": {
            "@type": "Country",
            "name": "India"
        },
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "INR",
            "description": "Free account registration with trial access to library management features"
        },
        "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Account Types",
            "itemListElement": [
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "{% if role == 'librarian' %}Librarian Account{% elif role == 'sublibrarian' %}Sub-Librarian Account{% elif role == 'librarycommander' %}Library Commander Account{% else %}Manager Account{% endif %}",
                        "description": "{% if role == 'librarian' %}Full library management access with administrative privileges{% elif role == 'sublibrarian' %}Assistant librarian access with limited administrative features{% elif role == 'librarycommander' %}Multi-library oversight and management capabilities{% else %}Manager-level access for library operations{% endif %}"
                    }
                }
            ]
        }
    }
    </script>

    <!-- Icons & Manifest -->
    <link rel="icon" type="image/x-icon" href="{% static 'favicon.ico' %}">
    <link rel="icon" type="image/png" sizes="512x512" href="{% static 'img/ms-icon-512x512.png' %}">

    <!-- External Resources -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous">

    <!-- Import master CSS variables from code_temp.html -->
    {% include "code_temp.html" %}

    <!-- Modern Glassmorphism CSS Framework -->
    <style>
        :root {
            /* Signup-specific overrides only */
            
            /* Typography */
            --font-primary: 'Comfortaa', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-display: 'Comfortaa', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px;
        }

        body {
            font-family: var(--font-primary);
            background: var(--gradient-hero);
            background-attachment: fixed;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            line-height: 1.7;
            color: var(--gray-900);
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            min-height: 100vh;
            padding: 2rem 0;
        }

        /* Enhanced background with animated particles */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* Signup Container */
        .signup-container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            padding: 2rem;
        }

        .signup-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-xl);
            box-shadow:
                0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset,
                0 2px 4px rgba(255, 255, 255, 0.1) inset;
            padding: 3rem 2.5rem;
            position: relative;
            overflow: visible;
            transition: var(--transition-slow);
            animation: slideInUp 0.6s ease-out;
        }

        .signup-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, 
                rgba(99, 102, 241, 0.1) 0%, 
                rgba(139, 92, 246, 0.05) 50%,
                transparent 100%);
            pointer-events: none;
            opacity: 0.8;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Security Badge with Enhanced Visibility */
        .security-badge {
            position: absolute;
            top: -15px;
            right: -15px;
            background: var(--gradient-secondary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius-lg);
            font-size: 0.875rem;
            font-weight: 600;
            box-shadow: var(--shadow-lg);
            z-index: 20 !important;
            animation: float 3s ease-in-out infinite, pulseBadgeSecure 2s ease-in-out infinite alternate;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        /* Enhanced floating animation for security badge */
        @keyframes pulseBadgeSecure {
            0% {
                box-shadow: var(--shadow-lg), 0 0 0 0 rgba(99, 102, 241, 0.4);
            }
            100% {
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px 5px rgba(99, 102, 241, 0.3);
            }
        }

        /* Brand Logo and Title */
        .brand-logo {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .brand-logo img {
            height: 60px;
            margin-bottom: 1rem;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .brand-title {
            font-family: var(--font-display);
            font-size: 1.75rem;
            font-weight: 800;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(255, 255, 255, 0.3);
        }

        .brand-subtitle {
            color: var(--gray-700);
            font-weight: 500;
            margin-bottom: 0;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
        }

        /* Form Styling */
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .form-label {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 0.5rem;
            display: block;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
        }

        .form-control {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.4);
            border-radius: var(--border-radius);
            padding: 1rem 1.25rem;
            font-size: 1rem;
            font-weight: 500;
            color: var(--gray-900);
            transition: var(--transition);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            width: 100%;
            box-shadow: 
                0 4px 6px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 
                0 0 0 3px rgba(99, 102, 241, 0.2),
                0 4px 12px rgba(99, 102, 241, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2) inset;
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .form-control:hover {
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.25);
        }

        .form-control::placeholder {
            color: var(--gray-600);
            font-weight: 400;
        }

        /* Form Select */
        .form-select {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.4);
            border-radius: var(--border-radius);
            padding: 1rem 1.25rem;
            font-size: 1rem;
            font-weight: 500;
            color: var(--gray-900);
            transition: var(--transition);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            width: 100%;
            box-shadow: 
                0 4px 6px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        }

        .form-select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow:
                0 0 0 3px rgba(99, 102, 241, 0.2),
                0 4px 12px rgba(99, 102, 241, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2) inset;
            background: rgba(255, 255, 255, 0.3);
        }

        /* Password Toggle */
        .password-group {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--gray-500);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2.5rem;
            height: 2.5rem;
        }

        .password-toggle:hover {
            color: var(--primary);
            background: rgba(99, 102, 241, 0.1);
        }

        .password-toggle:focus {
            outline: 2px solid var(--primary);
            outline-offset: 2px;
        }

        .password-group .form-control {
            padding-right: 3.5rem;
        }

        /* Password Strength */
        .password-strength {
            font-size: 0.875rem;
            font-weight: 600;
        }

        .strength-weak { color: var(--danger); }
        .strength-medium { color: var(--warning); }
        .strength-strong { color: var(--success); }

        .password-requirements {
            font-size: 0.875rem;
        }

        .requirement {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;
            color: var(--gray-600);
            transition: var(--transition);
        }

        .requirement.valid {
            color: var(--success);
        }

        .requirement.invalid {
            color: var(--danger);
        }

        .requirement.valid i {
            color: var(--success);
        }

        .requirement.invalid i {
            color: var(--danger);
        }

        /* Password Match */
        .password-match {
            font-size: 0.875rem;
            font-weight: 600;
        }

        .match-valid { color: var(--success); }
        .match-invalid { color: var(--danger); }

        /* Form Check */
        .form-check {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .form-check-input {
            margin: 0;
            width: 1.25rem;
            height: 1.25rem;
            border: 2px solid rgba(255, 255, 255, 0.4);
            border-radius: 0.375rem;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .form-check-input:checked {
            background-color: var(--primary);
            border-color: var(--primary);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
        }

        .form-check-label {
            color: var(--gray-700);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        /* Username Generation Button */
        #generateUsername {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-left: none;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(10px);
            color: rgba(217, 217, 236, 0.9);
            min-width: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-top-right-radius: var(--border-radius);
            border-bottom-right-radius: var(--border-radius);
        }

        #generateUsername:hover {
            background: rgba(255, 255, 255, 0.3);
            color: rgba(217, 217, 236, 1);
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
        }

        #generateUsername i {
            transition: transform 0.3s ease;
            font-size: 1rem;
        }

        #generateUsername:hover i {
            transform: rotate(180deg);
        }

        .input-group .form-control {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .input-group {
            position: relative;
            display: flex;
            flex-wrap: wrap;
            align-items: stretch;
            width: 100%;
        }

        /* Text visibility enhancements */
        .text-muted,
        .form-text,
        small.text-muted,
        small.form-text {
            color: rgba(217, 217, 236, 0.9) !important;
            font-weight: 500;
        }

        /* Signup Button */
        .signup-btn {
            width: 100%;
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: var(--border-radius-lg);
            padding: 1.25rem 2rem;
            font-size: 1.125rem;
            font-weight: 700;
            cursor: pointer;
            transition: var(--transition);
            box-shadow:
                0 10px 25px rgba(99, 102, 241, 0.3),
                0 4px 12px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .signup-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .signup-btn:hover::before {
            left: 100%;
        }

        .signup-btn:hover {
            transform: translateY(-3px);
            box-shadow:
                0 15px 35px rgba(99, 102, 241, 0.4),
                0 8px 20px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.15) inset;
        }

        .signup-btn:active {
            transform: translateY(-1px);
        }

        .signup-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Google Button */
        .google-btn {
            width: 100%;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-lg);
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            color: var(--gray-800);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            text-decoration: none;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow:
                0 4px 12px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        }

        .google-btn:hover {
            border-color: rgba(255, 255, 255, 0.5);
            color: var(--gray-900);
            transform: translateY(-2px);
            box-shadow:
                0 8px 20px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2) inset;
            text-decoration: none;
            background: rgba(255, 255, 255, 0.3);
        }

        .google-btn img {
            width: 24px;
            height: 24px;
        }

        /* Divider */
        .divider {
            position: relative;
            text-align: center;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.3);
        }

        .divider span {
            background: rgba(255, 255, 255, 0.1);
            padding: 0 1rem;
            color: var(--gray-600);
            font-size: 0.875rem;
            backdrop-filter: blur(10px);
        }

        /* Loading State */
        .loading {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Dark Mode Loading Spinner */
        body.dark-mode .spinner {
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-top: 2px solid rgba(255, 255, 255, 0.9);
        }

        /* Alert Messages */
        .alert {
            border-radius: var(--border-radius);
            border: none;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
            font-weight: 500;
            position: relative;
            z-index: 2;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success);
            border-left: 4px solid var(--success);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.2);
            color: var(--danger);
            border-left: 4px solid var(--danger);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
        }

        /* Links - Lighter colors for better visibility */
        .form-link {
            color: rgba(217, 217, 236, 0.9); /* Lighter version of primary */
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
        }

        .form-link:hover {
            color: rgba(79, 70, 229, 1); /* Lighter version of primary-dark */
            text-decoration: underline;
            text-shadow: 0 2px 4px rgba(255, 255, 255, 0.5);
            transform: translateY(-1px);
        }

        /* Footer Links */
        .footer-links {
            text-align: center;
            margin-top: 2rem;
            position: relative;
            z-index: 2;
        }

        .footer-links p {
            color: rgba(213, 220, 230, 0.9); /* Lighter gray for better visibility */
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            body::before {
                animation: none;
            }

            .signup-container {
                padding: 1rem;
                max-width: 100%;
            }

            .signup-card {
                padding: 2rem 1.5rem;
                backdrop-filter: blur(20px);
                -webkit-backdrop-filter: blur(20px);
            }

            .brand-title {
                font-size: 1.5rem;
            }

            .security-badge {
                position: relative;
                top: 0;
                right: 0;
                margin-bottom: 1rem;
                display: inline-block;
            }

            .form-group {
                margin-bottom: 1.25rem;
            }

            .password-requirements {
                font-size: 0.8rem;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .signup-card,
            .security-badge,
            body::before {
                animation: none;
            }

            .signup-btn::before {
                display: none;
            }
        }
    </style>
</head>

<body>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5L8WZWCC"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
 
    <div class="signup-container">
        <div class="signup-card">
            <!-- Security Badge -->
            <div class="security-badge">
                <i class="fas fa-shield-alt me-1"></i>
                Secure Signup
            </div>

            <!-- Brand Logo and Title -->
            <div class="brand-logo">
                <img id="signupLogo" src="{% static 'img/logo_trans_name.png' %}" alt="Librainian - Create Account" loading="eager">
                <h1 class="brand-title">Create Your Account</h1>
                <p class="brand-subtitle">Join the future of library management</p>
            </div>

            <!-- Alert Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}" role="alert">
                        <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' or message.tags == 'danger' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <!-- Signup Form -->
            <form method="post" novalidate id="signupForm">
                {% csrf_token %}

                <!-- Role-specific fields -->
                {% if role == "manager" %}
                <div class="form-group">
                    <label for="library_commander" class="form-label">
                        <i class="fas fa-user-tie me-2"></i>Admin Name
                    </label>
                    <select id="library_commander" name="library_commander" class="form-select" required>
                        <option value="">Select an Admin</option>
                        {% for labc in librarycommander %}
                        <option value="{{ labc.id }}" {% if labc.id == selected_library_commander_id %}selected{% endif %}>
                            {{ labc.user.first_name }} {{ labc.user.last_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                {% endif %}

                {% if role == "librarian" %}
                <div class="form-group">
                    <label for="manager" class="form-label">
                        <i class="fas fa-user-cog me-2"></i>Manager
                    </label>
                    <select id="manager" name="manager" class="form-select" required>
                        <option value="">Select a Manager</option>
                        {% for man in managers %}
                        <option value="{{ man.id }}" {% if man.id == selected_manager_id %}selected{% endif %}>
                            {{ man.user.first_name }} {{ man.user.last_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                {% endif %}

                <!-- Personal Information -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="first_name" class="form-label">
                                <i class="fas fa-user me-2"></i>First Name
                            </label>
                            <input type="text"
                                   id="first_name"
                                   name="first_name"
                                   class="form-control"
                                   placeholder="Enter your first name"
                                   required
                                   pattern="[A-Za-z ]+"
                                   title="Please enter only alphabets"
                                   autocomplete="given-name">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="last_name" class="form-label">
                                <i class="fas fa-user me-2"></i>Last Name
                            </label>
                            <input type="text"
                                   id="last_name"
                                   name="last_name"
                                   class="form-control"
                                   placeholder="Enter your last name"
                                   required
                                   autocomplete="family-name">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="username" class="form-label">
                        <i class="fas fa-at me-2"></i>Username
                    </label>
                    <div class="input-group">
                        <input type="text"
                               id="username"
                               name="username"
                               class="form-control"
                               placeholder="Choose a unique username"
                               required
                               pattern="^[a-zA-Z0-9._-]+$"
                               title="Username can only contain letters, numbers, periods (.), hyphens (-), and underscores (_)"
                               autocomplete="username">
                        <button type="button" id="generateUsername" class="btn btn-outline-primary" title="Generate username suggestion">
                            <i class="fas fa-refresh"></i>
                        </button>
                    </div>
                    <small class="form-text text-muted mt-2">
                        <i class="fas fa-info-circle me-1"></i>
                        Username can only contain letters, numbers, periods (.), hyphens (-), and underscores (_). Click <i class="fas fa-refresh"></i> for suggestions.
                    </small>
                    <div id="username_error" class="text-danger mt-1" style="display: none;"></div>
                </div>

                <!-- Library-specific fields for librarian role -->
                {% if role == "librarian" %}
                <div class="form-group">
                    <label for="libraryname" class="form-label">
                        <i class="fas fa-building me-2"></i>Library Name
                    </label>
                    <input type="text"
                           id="libraryname"
                           name="libraryname"
                           class="form-control"
                           placeholder="Enter your library name"
                           required
                           pattern="^(?!.*(?:https?://|www\.))[a-zA-Z0-9\s,'-]*$"
                           title="Please enter a valid library name without URLs">
                </div>

                <div class="form-group">
                    <label for="google_map_url" class="form-label">
                        <i class="fas fa-map-marker-alt me-2"></i>Google Map URL
                    </label>
                    <input type="url"
                           id="google_map_url"
                           name="google_map_url"
                           class="form-control"
                           placeholder="Enter your library's Google Maps URL"
                           required>
                    <small class="form-text text-muted mt-2">
                        <i class="fas fa-info-circle me-1"></i>
                        This helps students find your library location
                    </small>
                    <div id="url_error" class="text-danger mt-1"></div>
                </div>
                {% endif %}

                <!-- Contact Information -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>Email Address
                            </label>
                            <input type="email"
                                   id="email"
                                   name="email"
                                   class="form-control"
                                   placeholder="Enter your email address"
                                   required
                                   autocomplete="email">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-2"></i>Phone Number
                            </label>
                            <input type="tel"
                                   id="phone"
                                   name="phone"
                                   class="form-control"
                                   placeholder="Enter your phone number"
                                   required
                                   pattern="[0-9]{10}"
                                   maxlength="10"
                                   autocomplete="tel">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="address" class="form-label">
                        <i class="fas fa-map-marker-alt me-2"></i>Address
                    </label>
                    <input type="text"
                           id="address"
                           name="address"
                           class="form-control"
                           placeholder="Enter your complete address"
                           required
                           autocomplete="street-address">
                </div>

                <!-- Password Fields -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password1" class="form-label">
                                <i class="fas fa-lock me-2"></i>Password
                            </label>
                            <div class="password-group">
                                <input type="password"
                                       id="password1"
                                       name="password1"
                                       class="form-control"
                                       placeholder="Create a strong password"
                                       required
                                       autocomplete="new-password">
                                <button type="button" class="password-toggle" id="togglePassword1">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength mt-2" id="passwordStrength"></div>
                            <div class="password-requirements mt-2" id="passwordRequirements">
                                <div class="requirement" id="req-length">
                                    <i class="fas fa-times-circle"></i>
                                    <span>At least 8 characters</span>
                                </div>
                                <div class="requirement" id="req-uppercase">
                                    <i class="fas fa-times-circle"></i>
                                    <span>One uppercase letter</span>
                                </div>
                                <div class="requirement" id="req-number">
                                    <i class="fas fa-times-circle"></i>
                                    <span>One number</span>
                                </div>
                                <div class="requirement" id="req-special">
                                    <i class="fas fa-times-circle"></i>
                                    <span>One special character</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password2" class="form-label">
                                <i class="fas fa-lock me-2"></i>Confirm Password
                            </label>
                            <div class="password-group">
                                <input type="password"
                                       id="password2"
                                       name="password2"
                                       class="form-control"
                                       placeholder="Confirm your password"
                                       required
                                       autocomplete="new-password">
                                <button type="button" class="password-toggle" id="togglePassword2">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-match mt-2" id="passwordMatch"></div>
                        </div>
                    </div>
                </div>

                <!-- Terms and Conditions -->
                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="terms" required>
                        <label class="form-check-label" for="terms">
                            I agree to the <a href="/blogs/p/terms-of-use/" target="_blank" class="form-link">Terms of Service</a>
                            and <a href="/blogs/p/privacy-policy/" target="_blank" class="form-link">Privacy Policy</a>
                        </label>
                    </div>
                </div>

                <!-- Submit Button -->
                <button type="submit" class="signup-btn" id="submitBtn" disabled>
                    <span class="btn-text">
                        <i class="fas fa-user-plus me-2"></i>
                        Create Account
                    </span>
                    <span class="loading d-none">
                        <div class="spinner"></div>
                        Creating Account...
                    </span>
                </button>

                <!-- Google Signup -->
                <div class="text-center">
                    <div class="divider my-3">
                        <span>or</span>
                    </div>
                    <a href="{% url 'google_login' %}" class="google-btn">
                        <img src="{% static 'img/figma_files/google_g.png' %}" alt="Google logo">
                        <span>Sign up with Google</span>
                    </a>
                </div>
            </form>

            <!-- Footer Links -->
            <div class="footer-links">
                <p class="text-center mb-3">
                    Already have an account?
                    <a href="/{{ role }}/login/" class="form-link">
                        <i class="fas fa-sign-in-alt me-1"></i>Sign In
                    </a>
                </p>
                <div class="text-center">
                    <a href="/" class="form-link me-3">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                    <a href="/blogs/p/about/" class="form-link">
                        <i class="fas fa-info-circle me-1"></i>About Us
                    </a>
                </div>
            </div>

            <!-- Security Notice -->
            <div class="text-center mt-3">
                <small class="text-muted">
                    <i class="fas fa-lock me-1"></i>
                    Your data is protected with 256-bit SSL encryption
                </small>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Enhanced Signup Functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('signupForm');
            const submitBtn = document.getElementById('submitBtn');
            const btnText = submitBtn.querySelector('.btn-text');
            const loadingText = submitBtn.querySelector('.loading');

            // Form inputs
            const firstNameInput = document.getElementById('first_name');
            const lastNameInput = document.getElementById('last_name');
            const phoneInput = document.getElementById('phone');
            const libraryNameInput = document.getElementById('libraryname');
            const mapUrlInput = document.getElementById('google_map_url');
            const password1Input = document.getElementById('password1');
            const password2Input = document.getElementById('password2');
            const termsCheckbox = document.getElementById('terms');
            const usernameInput = document.getElementById('username');
            const generateUsernameBtn = document.getElementById('generateUsername');

            // Password elements
            const passwordStrength = document.getElementById('passwordStrength');
            const passwordRequirements = document.getElementById('passwordRequirements');
            const passwordMatch = document.getElementById('passwordMatch');

            // Password requirements
            const requirements = {
                length: document.getElementById('req-length'),
                uppercase: document.getElementById('req-uppercase'),
                number: document.getElementById('req-number'),
                special: document.getElementById('req-special')
            };

            // Debug: Check if elements exist
            console.log('Form elements found:', {
                form: !!form,
                submitBtn: !!submitBtn,
                password1Input: !!password1Input,
                password2Input: !!password2Input,
                termsCheckbox: !!termsCheckbox,
                requirements: Object.keys(requirements).reduce((acc, key) => {
                    acc[key] = !!requirements[key];
                    return acc;
                }, {})
            });

            // Input validation functions
            function allowOnlyAlphabets(input) {
                input.value = input.value.replace(/[^A-Za-z ]/g, '');
            }

            function validatePhone(input) {
                input.value = input.value.replace(/\D/g, '').slice(0, 10);
            }

            function validateLibraryName(input) {
                // Remove URL-like patterns
                input.value = input.value.replace(/(?:https?:\/\/|www\.)\S+/gi, '');
                // Remove special characters except comma, apostrophe, and hyphen
                input.value = input.value.replace(/[^a-zA-Z0-9\s,'-]/g, '');
            }

            function validateUsername(input) {
                const usernameError = document.getElementById('username_error');
                const value = input.value;

                // Remove invalid characters (allow only letters, numbers, periods, hyphens, underscores)
                const cleanValue = value.replace(/[^a-zA-Z0-9._-]/g, '');

                if (value !== cleanValue) {
                    input.value = cleanValue;
                    usernameError.textContent = "Only letters, numbers, periods (.), hyphens (-), and underscores (_) are allowed.";
                    usernameError.style.display = 'block';
                    input.style.borderColor = 'var(--danger)';
                    return false;
                } else {
                    usernameError.style.display = 'none';
                    input.style.borderColor = '';
                    return true;
                }
            }

            function validateMapUrl() {
                const urlError = document.getElementById('url_error');
                const url = mapUrlInput.value;

                if (url.length > 225) {
                    urlError.textContent = "URL cannot be greater than 225 characters.";
                    mapUrlInput.style.borderColor = 'var(--danger)';
                    return false;
                } else {
                    urlError.textContent = "";
                    mapUrlInput.style.borderColor = '';
                    return true;
                }
            }

            // Password strength validation
            function validatePassword() {
                const password = password1Input.value;
                let score = 0;
                let validRequirements = 0;

                // Only validate if password field exists and has content
                if (!password1Input || password.length === 0) {
                    // Reset all requirements to invalid state
                    Object.values(requirements).forEach(req => {
                        if (req) {
                            req.classList.add('invalid');
                            req.classList.remove('valid');
                            const icon = req.querySelector('i');
                            if (icon) icon.className = 'fas fa-times-circle';
                        }
                    });
                    if (passwordStrength) passwordStrength.textContent = '';
                    return false;
                }

                // Check length
                if (password.length >= 8) {
                    if (requirements.length) {
                        requirements.length.classList.add('valid');
                        requirements.length.classList.remove('invalid');
                        const icon = requirements.length.querySelector('i');
                        if (icon) icon.className = 'fas fa-check-circle';
                        score++;
                        validRequirements++;
                    }
                } else {
                    if (requirements.length) {
                        requirements.length.classList.add('invalid');
                        requirements.length.classList.remove('valid');
                        const icon = requirements.length.querySelector('i');
                        if (icon) icon.className = 'fas fa-times-circle';
                    }
                }

                // Check uppercase
                if (/[A-Z]/.test(password)) {
                    if (requirements.uppercase) {
                        requirements.uppercase.classList.add('valid');
                        requirements.uppercase.classList.remove('invalid');
                        const icon = requirements.uppercase.querySelector('i');
                        if (icon) icon.className = 'fas fa-check-circle';
                        score++;
                        validRequirements++;
                    }
                } else {
                    if (requirements.uppercase) {
                        requirements.uppercase.classList.add('invalid');
                        requirements.uppercase.classList.remove('valid');
                        const icon = requirements.uppercase.querySelector('i');
                        if (icon) icon.className = 'fas fa-times-circle';
                    }
                }

                // Check number
                if (/\d/.test(password)) {
                    if (requirements.number) {
                        requirements.number.classList.add('valid');
                        requirements.number.classList.remove('invalid');
                        const icon = requirements.number.querySelector('i');
                        if (icon) icon.className = 'fas fa-check-circle';
                        score++;
                        validRequirements++;
                    }
                } else {
                    if (requirements.number) {
                        requirements.number.classList.add('invalid');
                        requirements.number.classList.remove('valid');
                        const icon = requirements.number.querySelector('i');
                        if (icon) icon.className = 'fas fa-times-circle';
                    }
                }

                // Check special character
                if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
                    if (requirements.special) {
                        requirements.special.classList.add('valid');
                        requirements.special.classList.remove('invalid');
                        const icon = requirements.special.querySelector('i');
                        if (icon) icon.className = 'fas fa-check-circle';
                        score++;
                        validRequirements++;
                    }
                } else {
                    if (requirements.special) {
                        requirements.special.classList.add('invalid');
                        requirements.special.classList.remove('valid');
                        const icon = requirements.special.querySelector('i');
                        if (icon) icon.className = 'fas fa-times-circle';
                    }
                }

                // Update strength indicator
                if (passwordStrength) {
                    if (score === 0) {
                        passwordStrength.textContent = '';
                        passwordStrength.className = 'password-strength';
                    } else if (score < 3) {
                        passwordStrength.textContent = 'Weak password';
                        passwordStrength.className = 'password-strength strength-weak';
                    } else if (score === 3) {
                        passwordStrength.textContent = 'Medium password';
                        passwordStrength.className = 'password-strength strength-medium';
                    } else {
                        passwordStrength.textContent = 'Strong password';
                        passwordStrength.className = 'password-strength strength-strong';
                    }
                }

                return validRequirements === 4;
            }

            // Password match validation
            function validatePasswordMatch() {
                if (!password1Input || !password2Input || !passwordMatch) {
                    return false;
                }

                const password1 = password1Input.value;
                const password2 = password2Input.value;

                if (password2.length === 0) {
                    passwordMatch.textContent = '';
                    passwordMatch.className = 'password-match';
                    return false;
                }

                if (password1 === password2 && password1.length > 0) {
                    passwordMatch.textContent = 'Passwords match';
                    passwordMatch.className = 'password-match match-valid';
                    return true;
                } else {
                    passwordMatch.textContent = 'Passwords do not match';
                    passwordMatch.className = 'password-match match-invalid';
                    return false;
                }
            }

            // Check overall form validity
            function checkFormValidity() {
                if (!submitBtn) return;

                const passwordValid = validatePassword();
                const passwordsMatch = validatePasswordMatch();
                const termsAccepted = termsCheckbox ? termsCheckbox.checked : true;
                const mapUrlValid = mapUrlInput ? validateMapUrl() : true;

                // Check if all required fields are filled
                const requiredFields = form.querySelectorAll('input[required], select[required]');
                let allFieldsFilled = true;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        allFieldsFilled = false;
                    }
                });

                const isValid = passwordValid && passwordsMatch && termsAccepted && mapUrlValid && allFieldsFilled;

                submitBtn.disabled = !isValid;

                // Visual feedback for submit button
                if (isValid) {
                    submitBtn.style.opacity = '1';
                    submitBtn.style.cursor = 'pointer';
                } else {
                    submitBtn.style.opacity = '0.6';
                    submitBtn.style.cursor = 'not-allowed';
                }
            }

            // Password toggle functionality
            function setupPasswordToggle(inputId, toggleId) {
                const input = document.getElementById(inputId);
                const toggle = document.getElementById(toggleId);

                if (input && toggle) {
                    toggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        const currentType = input.getAttribute('type');
                        const newType = currentType === 'password' ? 'text' : 'password';
                        input.setAttribute('type', newType);

                        const icon = toggle.querySelector('i');
                        if (icon) {
                            if (newType === 'password') {
                                icon.className = 'fas fa-eye';
                            } else {
                                icon.className = 'fas fa-eye-slash';
                            }
                        }
                    });

                    // Prevent form submission when clicking toggle
                    toggle.addEventListener('mousedown', function(e) {
                        e.preventDefault();
                    });
                }
            }

            // Event listeners
            if (firstNameInput) {
                firstNameInput.addEventListener('input', function() {
                    allowOnlyAlphabets(this);
                    checkFormValidity();
                });
            }

            if (lastNameInput) {
                lastNameInput.addEventListener('input', function() {
                    allowOnlyAlphabets(this);
                    checkFormValidity();
                });
            }

            if (phoneInput) {
                phoneInput.addEventListener('input', function() {
                    validatePhone(this);
                    checkFormValidity();
                });
            }

            if (libraryNameInput) {
                libraryNameInput.addEventListener('input', function() {
                    validateLibraryName(this);
                    checkFormValidity();
                });
            }

            if (mapUrlInput) {
                mapUrlInput.addEventListener('input', function() {
                    validateMapUrl();
                    checkFormValidity();
                });
            }

            if (password1Input) {
                password1Input.addEventListener('input', function() {
                    validatePassword();
                    validatePasswordMatch(); // Also check match when password1 changes
                    checkFormValidity();
                });
            }

            if (password2Input) {
                password2Input.addEventListener('input', function() {
                    validatePasswordMatch();
                    checkFormValidity();
                });
            }

            if (termsCheckbox) {
                termsCheckbox.addEventListener('change', checkFormValidity);
            }

            if (usernameInput) {
                usernameInput.addEventListener('input', function() {
                    validateUsername(this);
                    checkFormValidity();
                });
            }

            // Add event listeners for all required fields
            const allInputs = form.querySelectorAll('input, select');
            allInputs.forEach(input => {
                input.addEventListener('input', checkFormValidity);
                input.addEventListener('change', checkFormValidity);
            });

            // Setup password toggles
            setupPasswordToggle('password1', 'togglePassword1');
            setupPasswordToggle('password2', 'togglePassword2');

            // Username generation functionality
            function generateUsername() {
                const firstName = firstNameInput ? firstNameInput.value.trim() : '';
                const lastName = lastNameInput ? lastNameInput.value.trim() : '';

                let baseName = '';

                if (!firstName && !lastName) {
                    // If no name is provided, generate a random base
                    const randomChars = 'abcdefghijklmnopqrstuvwxyz';
                    for (let i = 0; i < 3; i++) {
                        baseName += randomChars.charAt(Math.floor(Math.random() * randomChars.length));
                    }
                } else {
                    // Use name to generate base
                    if (firstName) {
                        baseName += firstName.toLowerCase().replace(/[^a-z]/g, '').substring(0, 3);
                    }
                    if (lastName) {
                        baseName += lastName.toLowerCase().replace(/[^a-z]/g, '').substring(0, 2);
                    }

                    // Ensure minimum base length
                    if (baseName.length < 2) {
                        baseName = 'user';
                    }
                }

                // Always add one special character (- or _)
                const separators = ['_', '-'];
                const separator = separators[Math.floor(Math.random() * separators.length)];

                // Always add at least one number (1-2 digits)
                const numbers = Math.floor(Math.random() * 99) + 1;

                // Build username: base + separator + number
                let username = baseName + separator + numbers;

                // Ensure length is between 5-7 characters
                if (username.length > 7) {
                    // Trim base name if too long
                    const excess = username.length - 7;
                    baseName = baseName.substring(0, baseName.length - excess);
                    username = baseName + separator + numbers;
                } else if (username.length < 5) {
                    // Add more numbers if too short
                    const additionalNum = Math.floor(Math.random() * 9);
                    username += additionalNum;
                }

                // Final check: ensure we have at least one letter, one separator, and one number
                const hasLetter = /[a-z]/.test(username);
                const hasSeparator = /[_-]/.test(username);
                const hasNumber = /[0-9]/.test(username);

                if (!hasLetter || !hasSeparator || !hasNumber) {
                    // Fallback: create a guaranteed valid username
                    const fallbackBase = hasLetter ? baseName.substring(0, 2) : 'usr';
                    const fallbackSep = hasSeparator ? separator : '_';
                    const fallbackNum = hasNumber ? numbers : Math.floor(Math.random() * 99) + 1;
                    username = fallbackBase + fallbackSep + fallbackNum;
                }

                return username;
            }

            // Generate username button event
            if (generateUsernameBtn && usernameInput) {
                generateUsernameBtn.addEventListener('click', function() {
                    const suggestedUsername = generateUsername();
                    usernameInput.value = suggestedUsername;

                    // Add animation effect
                    generateUsernameBtn.style.transform = 'rotate(180deg)';
                    setTimeout(() => {
                        generateUsernameBtn.style.transform = 'rotate(0deg)';
                    }, 300);

                    // Trigger validation if needed
                    usernameInput.dispatchEvent(new Event('input'));
                });
            }

            // Auto-generate username when name fields change
            if (firstNameInput && lastNameInput && usernameInput) {
                function autoSuggestUsername() {
                    if (!usernameInput.value.trim()) {
                        const suggestedUsername = generateUsername();
                        usernameInput.placeholder = `Suggestion: ${suggestedUsername}`;
                    }
                }

                firstNameInput.addEventListener('input', autoSuggestUsername);
                lastNameInput.addEventListener('input', autoSuggestUsername);
            }

            // Form submission
            form.addEventListener('submit', function(event) {
                event.preventDefault();

                // Double-check form validity before submission
                checkFormValidity();

                if (!submitBtn.disabled) {
                    // Show loading state
                    submitBtn.disabled = true;
                    btnText.classList.add('d-none');
                    loadingText.classList.remove('d-none');

                    // Submit form after brief delay for UX
                    setTimeout(() => {
                        // Remove the event listener to allow normal form submission
                        form.removeEventListener('submit', arguments.callee);
                        form.submit();
                    }, 500);
                } else {
                    // Highlight missing fields or validation errors
                    const invalidFields = form.querySelectorAll('input:invalid, select:invalid');
                    if (invalidFields.length > 0) {
                        invalidFields[0].focus();
                    }
                }
            });

            // Auto-hide alerts
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    alert.style.transition = 'opacity 0.5s ease-out';
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.remove();
                    }, 500);
                }, 5000);
            });

            // Initialize form state
            setTimeout(() => {
                // Initial form validation check
                checkFormValidity();

                // Set initial password requirements state
                if (password1Input) {
                    validatePassword();
                }

                // Ensure submit button starts disabled
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.style.opacity = '0.6';
                    submitBtn.style.cursor = 'not-allowed';
                }
            }, 100);

            // Dark mode initialization
            const darkModeEnabled = localStorage.getItem('darkMode') === 'enabled';
            if (darkModeEnabled) {
                document.body.classList.add('dark-mode');
                applyDarkModeStyles(true);
            } else {
                // Ensure light mode logo is set
                applyDarkModeStyles(false);
            }
        });

        // Dark mode styles function
        function applyDarkModeStyles(isDarkMode) {
            const root = document.documentElement;
            const signupLogo = document.getElementById('signupLogo');

            if (isDarkMode) {
                // Dark mode color overrides
                root.style.setProperty('--bg-primary', '#111827');
                root.style.setProperty('--bg-secondary', '#1f2937');
                root.style.setProperty('--bg-tertiary', '#374151');
                root.style.setProperty('--text-primary', '#ffffff');
                root.style.setProperty('--text-secondary', '#d1d5db');
                root.style.setProperty('--text-muted', '#9ca3af');
                root.style.setProperty('--border-color', '#4b5563');
                root.style.setProperty('--shadow-color', 'rgba(0, 0, 0, 0.3)');

                // Dark glassmorphism
                root.style.setProperty('--glass-bg', 'rgba(255, 255, 255, 0.05)');
                root.style.setProperty('--glass-border', 'rgba(255, 255, 255, 0.1)');
                root.style.setProperty('--glass-shadow', '0 8px 32px rgba(0, 0, 0, 0.3)');

                // Dark gradients
                root.style.setProperty('--gradient-hero', 'linear-gradient(135deg, #1f2937 0%, #111827 100%)');
                root.style.setProperty('--gradient-glass', 'linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%)');

                // Switch to white logo
                if (signupLogo) {
                    signupLogo.src = signupLogo.src.replace('logo_trans_name.png', 'librainian-logo-white-trans.png');
                }
            } else {
                // Light mode - switch back to original logo
                if (signupLogo) {
                    signupLogo.src = signupLogo.src.replace('librainian-logo-white-trans.png', 'logo_trans_name.png');
                }
            }

            // Update browser theme and status bar
            updateBrowserTheme(isDarkMode);
        }

        // Update browser theme and status bar
        function updateBrowserTheme(isDarkMode) {
            // Update theme-color meta tag
            const themeColorMeta = document.getElementById('theme-color-meta');
            const statusBarMeta = document.getElementById('status-bar-meta');

            if (themeColorMeta) {
                themeColorMeta.setAttribute('content', isDarkMode ? '#111827' : '#6366f1');
            }

            if (statusBarMeta) {
                statusBarMeta.setAttribute('content', isDarkMode ? 'black-translucent' : 'default');
            }
        }
    </script>

    <!-- Dark Mode Styles -->
    <style>
        /* Dark Mode Styles for Signup Page */
        body.dark-mode {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            color: white;
        }

        body.dark-mode .signup-card {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .brand-title {
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .brand-subtitle {
            color: rgba(255, 255, 255, 0.8);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .form-label {
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .form-control {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        body.dark-mode .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        body.dark-mode .form-control:focus {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary);
            color: white;
        }

        body.dark-mode .form-control:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 255, 255, 0.3);
        }

        body.dark-mode .password-toggle {
            color: rgba(255, 255, 255, 0.6);
        }

        body.dark-mode .password-toggle:hover {
            color: var(--primary);
        }

        body.dark-mode .form-link {
            color: rgba(217, 217, 236, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .form-link:hover {
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        body.dark-mode .form-check-label {
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .form-check-input {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
        }

        body.dark-mode .form-check-input:checked {
            background: var(--primary);
            border-color: var(--primary);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
        }

        body.dark-mode .text-muted,
        body.dark-mode .form-text,
        body.dark-mode .form-text.text-muted,
        body.dark-mode small.text-muted,
        body.dark-mode small.form-text {
            color: rgba(217, 217, 236, 0.9) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }



        body.dark-mode .requirement-item {
            color: rgba(255, 255, 255, 0.8);
        }

        body.dark-mode .requirement-item.valid {
            color: var(--success);
        }

        body.dark-mode .requirement-item.invalid {
            color: var(--danger);
        }

        body.dark-mode .alert {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        body.dark-mode .security-badge {
            background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
            color: white;
        }

        body.dark-mode .form-select {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        body.dark-mode .form-select:focus {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary);
            color: white;
        }

        body.dark-mode .form-text {
            color: rgba(255, 255, 255, 0.7);
        }

        body.dark-mode #generateUsername {
            background: rgba(255, 255, 255, 0.05) !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            color: rgba(255, 255, 255, 0.9) !important;
            backdrop-filter: blur(10px);
        }

        body.dark-mode #generateUsername:hover {
            background: rgba(255, 255, 255, 0.1) !important;
            border-color: rgba(255, 255, 255, 0.3) !important;
            color: white !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .input-group .form-control {
            border-right: none;
        }

        body.dark-mode .divider span {
            color: rgba(217, 217, 236, 0.9) !important;
            background: rgba(255, 255, 255, 0.05);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .divider::before {
            background: rgba(255, 255, 255, 0.2);
        }
    </style>
</body>
</html>
