<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="x-apple-disable-message-reformatting">
    <meta name="format-detection" content="telephone=no,address=no,email=no,date=no,url=no">
    <meta name="color-scheme" content="light">
    <meta name="supported-color-schemes" content="light">
    
    <!-- JSON-LD Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "EmailMessage",
        "sender": {
            "@type": "Organization",
            "name": "Librainian",
            "url": "https://librainian.com",
            "logo": "https://librainian.com/static/img/librainian-logo-black-transparent.png",
            "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "+91-XXXXXXXXXX",
                "contactType": "customer service",
                "email": "<EMAIL>"
            }
        },
        "about": "{% block email_subject %}{% endblock %}",
        "dateCreated": "{{ current_date|default:'now'|date:'c' }}",
        "description": "{% block email_description %}{% endblock %}"
    }
    </script>
    
    <title>{% block email_title %}Librainian{% endblock %}</title>
    
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    
    <style>
        /* Reset and base styles */
        * {
            box-sizing: border-box;
        }
        
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }
        
        /* Client-specific styles */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #f8fafc;
            font-family: 'Comfortaa', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #374151;
        }
        
        table {
            border-collapse: collapse !important;
        }
        
        a {
            color: #6366f1;
            text-decoration: none;
        }
        
        /* Container styles */
        .email-wrapper {
            width: 100%;
            background-color: #f8fafc;
            padding: 20px 0;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e7eb;
        }
        
        /* Header styles */
        .email-header {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            text-align: center;
            padding: 40px 30px;
            position: relative;
            overflow: hidden;
        }
        
        .email-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 3s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(30deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(30deg); }
        }
        
        .header-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
            position: relative;
            z-index: 2;
        }
        
        .email-title {
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 10px 0;
            position: relative;
            z-index: 2;
            font-family: 'Comfortaa', sans-serif;
        }
        
        .email-subtitle {
            font-size: 16px;
            margin: 0;
            opacity: 0.9;
            position: relative;
            z-index: 2;
            font-weight: 400;
        }
        
        /* Content styles */
        .email-content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 20px 0;
            font-family: 'Comfortaa', sans-serif;
        }
        
        .message {
            font-size: 16px;
            color: #4b5563;
            margin: 0 0 25px 0;
            line-height: 1.7;
        }
        
        /* Card styles */
        .info-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            border: 1px solid #e2e8f0;
            position: relative;
            overflow: hidden;
        }
        
        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #6366f1, #8b5cf6);
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 15px 0;
            font-family: 'Comfortaa', sans-serif;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .detail-label {
            font-weight: 600;
            color: #6b7280;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            flex-shrink: 0;
            margin-right: 15px;
        }
        
        .detail-value {
            color: #1f2937;
            font-weight: 500;
            text-align: right;
            word-break: break-word;
        }
        
        /* Button styles */
        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white !important;
            padding: 16px 32px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.3);
            border: none;
            cursor: pointer;
            font-family: 'Comfortaa', sans-serif;
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px 0 rgba(99, 102, 241, 0.4);
        }
        
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        
        /* Footer styles */
        .email-footer {
            background-color: #f9fafb;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        
        .footer-logo {
            max-width: 120px;
            margin-bottom: 15px;
        }
        
        .footer-text {
            font-size: 14px;
            color: #6b7280;
            margin: 5px 0;
        }
        
        .footer-links {
            margin: 20px 0 10px 0;
        }
        
        .footer-links a {
            color: #6366f1;
            text-decoration: none;
            margin: 0 10px;
            font-size: 14px;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        /* Responsive design */
        @media only screen and (max-width: 600px) {
            .email-container {
                margin: 0 10px;
                border-radius: 12px;
            }
            
            .email-header {
                padding: 30px 20px;
            }
            
            .email-content {
                padding: 30px 20px;
            }
            
            .email-footer {
                padding: 20px;
            }
            
            .email-title {
                font-size: 24px;
            }
            
            .greeting {
                font-size: 18px;
            }
            
            .detail-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .detail-label {
                margin-bottom: 5px;
            }
            
            .detail-value {
                text-align: left;
            }
            
            .action-button {
                padding: 14px 24px;
                font-size: 15px;
            }
        }
        
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .email-container {
                background-color: #1f2937 !important;
            }
            
            .email-content {
                background-color: #1f2937 !important;
            }
            
            .greeting,
            .message,
            .detail-value {
                color: #f9fafb !important;
            }
            
            .info-card {
                background: linear-gradient(135deg, #374151 0%, #4b5563 100%) !important;
                border-color: #4b5563 !important;
            }
            
            .email-footer {
                background-color: #111827 !important;
                border-color: #374151 !important;
            }
        }
        
        /* Print styles */
        @media print {
            .email-wrapper {
                background-color: white !important;
            }
            
            .email-container {
                box-shadow: none !important;
                border: 1px solid #000 !important;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Preview text (hidden) -->
    <div style="display: none; font-size: 1px; color: #fefefe; line-height: 1px; font-family: 'Comfortaa', sans-serif; max-height: 0px; max-width: 0px; opacity: 0; overflow: hidden;">
        {% block preview_text %}{% endblock %}
    </div>

    <div class="email-wrapper">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
            <tr>
                <td>
                    <div class="email-container">
                        <!-- Header -->
                        <div class="email-header">
                            <div class="header-icon">{% block header_icon %}📧{% endblock %}</div>
                            <h1 class="email-title">{% block email_header_title %}Librainian{% endblock %}</h1>
                            <p class="email-subtitle">{% block email_header_subtitle %}Your Library Management System{% endblock %}</p>
                        </div>

                        <!-- Content -->
                        <div class="email-content">
                            {% block email_content %}
                            <h2 class="greeting">Hello!</h2>
                            <p class="message">This is a default email template.</p>
                            {% endblock %}
                        </div>

                        <!-- Footer -->
                        <div class="email-footer">
                            <img src="https://librainian.com/static/img/librainian-logo-black-transparent.png" alt="Librainian Logo" class="footer-logo">
                            <p class="footer-text"><strong>Librainian</strong></p>
                            <p class="footer-text">Your Gateway to Knowledge and Excellence</p>
                            
                            <div class="footer-links">
                                <a href="https://librainian.com">Website</a>
                                <a href="mailto:<EMAIL>">Support</a>
                                <a href="https://librainian.com/privacy">Privacy</a>
                            </div>
                            
                            <p class="footer-text">&copy; {{ current_year|default:'2024' }} Librainian. All rights reserved.</p>
                            <p class="footer-text">F2/9 Jai Durga Society Netaji Nagar Hill no.3, 90ft road Sakinaka, Kurla West Mumbai 400072</p>
                            
                            {% block footer_extra %}{% endblock %}
                        </div>
                    </div>
                </td>
            </tr>
        </table>
    </div>

    {% block extra_js %}{% endblock %}
</body>
</html>
