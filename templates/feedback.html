{% extends "base.html" %}

{% block title %}Feedback - Librainian{% endblock %}

{% block page_title %}Feedback{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Feedback</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Feedback Form Styles */
    .feedback-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;
    }

    .feedback-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        padding: 3rem 2rem;
        color: white;
        margin-bottom: 3rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .feedback-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .feedback-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 2rem;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 3px solid rgba(255, 255, 255, 0.3);
        position: relative;
        z-index: 1;
        animation: pulse 2s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .feedback-icon i {
        font-size: 2rem;
        color: white;
    }

    .feedback-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        position: relative;
        z-index: 1;
    }

    .feedback-header p {
        font-size: 1.25rem;
        opacity: 0.9;
        margin: 0;
        position: relative;
        z-index: 1;
        line-height: 1.6;
    }

    .feedback-intro {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border-radius: 16px;
        padding: 2rem;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .feedback-intro::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .feedback-intro h3 {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        position: relative;
        z-index: 1;
    }

    .feedback-intro p {
        font-size: 1.125rem;
        opacity: 0.9;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    .emoji-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .emoji-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 1.5rem;
    }

    .emoji-grid {
        display: flex;
        justify-content: center;
        gap: 1rem;
        flex-wrap: wrap;
        margin-bottom: 1rem;
    }

    .emoji {
        width: 60px;
        height: 60px;
        font-size: 2rem;
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(156, 163, 175, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .emoji::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, transparent 0%, rgba(99, 102, 241, 0.1) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .emoji:hover {
        transform: translateY(-4px) scale(1.1);
        border-color: #6366f1;
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
    }

    .emoji:hover::before {
        opacity: 1;
    }

    .emoji.selected {
        background: #6366f1;
        border-color: #6366f1;
        transform: translateY(-4px) scale(1.1);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    }

    .form-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .form-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 1.5rem;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .form-title i {
        color: #6366f1;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(156, 163, 175, 0.3);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        width: 100%;
    }

    .form-control:focus {
        outline: none;
        border-color: #6366f1;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        background: rgba(255, 255, 255, 1);
    }

    .input-group {
        position: relative;
    }

    .input-group-text {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6b7280;
        font-size: 1rem;
        z-index: 2;
        background: none;
        border: none;
        pointer-events: none;
    }

    .input-group .form-control {
        padding-left: 2.5rem;
    }

    .btn-submit {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border: none;
        border-radius: 12px;
        padding: 1rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    }

    .selected-emoji-display {
        background: rgba(99, 102, 241, 0.1);
        border: 2px solid rgba(99, 102, 241, 0.3);
        border-radius: 12px;
        padding: 1rem;
        margin-top: 1rem;
        text-align: center;
        display: none;
    }

    .selected-emoji-display.show {
        display: block;
        animation: slideIn 0.3s ease-out;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .feedback-container,
        .emoji-section,
        .form-section {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .feedback-header {
            padding: 2rem 1rem;
        }

        .feedback-header h1 {
            font-size: 2rem;
        }

        .emoji-grid {
            gap: 0.75rem;
        }

        .emoji {
            width: 50px;
            height: 50px;
            font-size: 1.5rem;
        }
    }

    /* Animation */
    .fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="feedback-container fade-in">
    <!-- Feedback Header -->
    <div class="feedback-header">
        <div class="feedback-icon">
            <i class="fas fa-comments"></i>
        </div>
        <h1>We Value Your Feedback</h1>
        <p>Help us improve our library management system with your valuable insights</p>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    {% endif %}

    <!-- Feedback Introduction -->
    <div class="feedback-intro">
        <h3>Share Your Experience</h3>
        <p>Your feedback helps us create a better experience for all library users. Please take a moment to share your thoughts.</p>
    </div>

    <!-- Emoji Rating Section -->
    <div class="emoji-section">
        <div class="emoji-title">How would you rate your experience?</div>
        <div class="emoji-grid">
            <div class="emoji" onclick="selectEmoji(this, '😊')" data-emoji="😊" title="Happy">😊</div>
            <div class="emoji" onclick="selectEmoji(this, '😢')" data-emoji="😢" title="Sad">😢</div>
            <div class="emoji" onclick="selectEmoji(this, '👍')" data-emoji="👍" title="Good">👍</div>
            <div class="emoji" onclick="selectEmoji(this, '❤️')" data-emoji="❤️" title="Love">❤️</div>
            <div class="emoji" onclick="selectEmoji(this, '😡')" data-emoji="😡" title="Angry">😡</div>
            <div class="emoji" onclick="selectEmoji(this, '😎')" data-emoji="😎" title="Cool">😎</div>
        </div>
        <div class="selected-emoji-display" id="selectedEmojiDisplay">
            <strong>Selected: <span id="selectedEmojiText"></span></strong>
        </div>
    </div>

    <!-- Feedback Form -->
    <div class="form-section">
        <div class="form-title">
            <i class="fas fa-edit"></i>
            Tell Us More
        </div>

        <form method="POST" id="feedbackForm" novalidate>
            {% csrf_token %}
            <input type="hidden" name="selected_emoji" id="selectedEmojiInput">

            <div class="form-group">
                <label for="name" class="form-label">Your Name</label>
                <div class="input-group">
                    <i class="fas fa-user input-group-text"></i>
                    <input type="text" class="form-control" id="name" name="name"
                           placeholder="Enter your full name" required maxlength="100">
                </div>
            </div>

            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <div class="input-group">
                    <i class="fas fa-envelope input-group-text"></i>
                    <input type="email" class="form-control" id="email" name="email"
                           placeholder="Enter your email address" required>
                </div>
            </div>

            <div class="form-group">
                <label for="feedback" class="form-label">Your Feedback</label>
                <div class="input-group">
                    <i class="fas fa-comment-alt input-group-text" style="top: 1rem; transform: none;"></i>
                    <textarea class="form-control" id="feedback" name="feedback" rows="5"
                              placeholder="Share your thoughts, suggestions, or concerns..."
                              required maxlength="1000" style="padding-left: 2.5rem;"></textarea>
                </div>
                <small class="text-muted">Maximum 1000 characters</small>
            </div>

            <button type="submit" class="btn-submit" id="submitBtn">
                <i class="fas fa-paper-plane"></i>
                Submit Feedback
            </button>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Enhanced Feedback System
    class FeedbackManager {
        constructor() {
            this.form = document.getElementById('feedbackForm');
            this.submitBtn = document.getElementById('submitBtn');
            this.selectedEmoji = null;
            this.init();
        }

        init() {
            this.setupFormValidation();
            this.setupFormSubmission();
            this.setupCharacterCount();
        }

        setupFormValidation() {
            const inputs = this.form.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', () => this.clearErrors(input));
            });
        }

        validateField(field) {
            const value = field.value.trim();
            let isValid = true;
            let errorMessage = '';

            // Required field validation
            if (field.hasAttribute('required') && !value) {
                isValid = false;
                errorMessage = 'This field is required.';
            }

            // Email validation
            if (field.type === 'email' && value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid email address.';
                }
            }

            // Name validation
            if (field.name === 'name' && value) {
                if (value.length < 2) {
                    isValid = false;
                    errorMessage = 'Name must be at least 2 characters long.';
                }
            }

            // Feedback validation
            if (field.name === 'feedback' && value) {
                if (value.length < 10) {
                    isValid = false;
                    errorMessage = 'Feedback must be at least 10 characters long.';
                }
            }

            this.showFieldError(field, isValid ? '' : errorMessage);
            return isValid;
        }

        showFieldError(field, message) {
            const existingError = field.parentNode.parentNode.querySelector('.invalid-feedback');
            if (existingError) {
                existingError.remove();
            }

            if (message) {
                field.classList.add('is-invalid');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.style.display = 'block';
                errorDiv.style.fontSize = '0.875rem';
                errorDiv.style.color = '#ef4444';
                errorDiv.style.marginTop = '0.25rem';
                errorDiv.textContent = message;
                field.parentNode.parentNode.appendChild(errorDiv);
            } else {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            }
        }

        clearErrors(field) {
            field.classList.remove('is-invalid', 'is-valid');
            const errorDiv = field.parentNode.parentNode.querySelector('.invalid-feedback');
            if (errorDiv) {
                errorDiv.remove();
            }
        }

        setupCharacterCount() {
            const feedbackTextarea = document.getElementById('feedback');
            const maxLength = feedbackTextarea.getAttribute('maxlength');

            // Create character counter
            const counterDiv = document.createElement('div');
            counterDiv.className = 'character-counter';
            counterDiv.style.cssText = 'text-align: right; font-size: 0.875rem; color: #6b7280; margin-top: 0.25rem;';
            counterDiv.innerHTML = `<span id="charCount">0</span>/${maxLength} characters`;

            feedbackTextarea.parentNode.parentNode.appendChild(counterDiv);

            feedbackTextarea.addEventListener('input', () => {
                const currentLength = feedbackTextarea.value.length;
                document.getElementById('charCount').textContent = currentLength;

                if (currentLength > maxLength * 0.9) {
                    counterDiv.style.color = '#ef4444';
                } else if (currentLength > maxLength * 0.7) {
                    counterDiv.style.color = '#f59e0b';
                } else {
                    counterDiv.style.color = '#6b7280';
                }
            });
        }

        setupFormSubmission() {
            this.form.addEventListener('submit', (e) => {
                e.preventDefault();

                // Validate all fields
                const inputs = this.form.querySelectorAll('input[required], textarea[required]');
                let isFormValid = true;

                inputs.forEach(input => {
                    if (!this.validateField(input)) {
                        isFormValid = false;
                    }
                });

                if (isFormValid) {
                    this.submitForm();
                } else {
                    this.showNotification('Please fix the errors before submitting.', 'error');
                    const firstError = this.form.querySelector('.is-invalid');
                    if (firstError) {
                        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }
            });
        }

        submitForm() {
            const originalText = this.submitBtn.innerHTML;
            this.submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
            this.submitBtn.disabled = true;

            // Submit the form
            this.form.submit();
        }

        showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    }

    // Global emoji selection function
    function selectEmoji(element, emoji) {
        // Remove selection from all emojis
        document.querySelectorAll('.emoji').forEach(e => e.classList.remove('selected'));

        // Add selection to clicked emoji
        element.classList.add('selected');

        // Update hidden input and display
        document.getElementById('selectedEmojiInput').value = emoji;
        document.getElementById('selectedEmojiText').textContent = emoji + ' ' + element.title;
        document.getElementById('selectedEmojiDisplay').classList.add('show');

        // Store selected emoji
        if (window.feedbackManager) {
            window.feedbackManager.selectedEmoji = emoji;
        }
    }

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        window.feedbackManager = new FeedbackManager();
    });
</script>
{% endblock %}
