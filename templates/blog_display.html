<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">

    <!-- Include Navbar Head Section -->
    {% with navbar_section="head" %}
        {% include "public_navbar.html" %}
    {% endwith %}
    <meta content="{{ blog.description }}" name="description" id="metaDescription">
    <meta content="{{ blog.keyword }}" name="keywords">
    <meta name="robots" content="index,follow">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta http-equiv="content-language" content="en">
    <meta name="geo.region" content="IN">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{ blog.title }}">
    <meta property="og:description" content="{{ blog.description }}" id="ogDescription">
    <meta property="og:url" content="https://librainian.com/blogs/p/{{ blog.slug }}/">
    <meta property="og:image" content="https://librainian.com{{ blog.image.url }}">
    <meta property="og:type" content="article">
    <meta property="og:site_name" content="Librainian">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ blog.title }}">
    <meta name="twitter:description" content="{{ blog.description }}" id="twitterDescription">
    <meta name="twitter:image" content="https://librainian.com{{ blog.image.url }}">
    
    <!-- Article Meta Tags -->
    <meta itemprop="author" content="{{ blog.author.user.first_name }} {{ blog.author.user.last_name }}">
    <meta itemprop="datePublished" content="{{ blog.published_date }}">
    <meta itemprop="dateModified" content="{{ blog.updated_at }}">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://librainian.com/blogs/p/{{ blog.slug }}/">

    <title>{{ blog.title }} | Librainian</title>

    <!-- BlogPosting Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "headline": "{{ blog.title|escapejs }}",
        "description": "{{ blog.description|escapejs }}",
        "image": {
            "@type": "ImageObject",
            "url": "https://librainian.com{{ blog.image.url }}",
            "width": 1200,
            "height": 630
        },
        "datePublished": "{{ blog.published_date|date:'c' }}",
        "dateModified": "{{ blog.updated_at|date:'c' }}",
        "author": {
            "@type": "Person",
            "name": "{{ blog.author.user.first_name }} {{ blog.author.user.last_name }}",
            "url": "https://librainian.com/blogs/author/{{ blog.author.user.username }}/"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "logo": {
                "@type": "ImageObject",
                "url": "https://librainian.com/static/img/logo_trans_name.png",
                "width": 300,
                "height": 100
            },
            "url": "https://librainian.com/"
        },
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "https://librainian.com/blogs/p/{{ blog.slug }}/"
        },
        "keywords": "{{ blog.meta_keywords|default:'library management, library blog, educational resources'|escapejs }}",
        "articleSection": "{{ blog.category }}",
        "wordCount": "{{ blog.content|wordcount }}",
        "inLanguage": "en-US",
        "url": "https://librainian.com/blogs/p/{{ blog.slug }}/"
    }
    </script>

    <!-- BlogPosting Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "headline": "{{ blog.title|escapejs }}",
        "description": "{{ blog.description|escapejs }}",
        "image": {
            "@type": "ImageObject",
            "url": "https://librainian.com{{ blog.image.url }}",
            "width": 1200,
            "height": 630
        },
        "datePublished": "{{ blog.published_date|date:'c' }}",
        "dateModified": "{{ blog.updated_at|date:'c' }}",
        "author": {
            "@type": "Person",
            "name": "{{ blog.author.user.first_name }} {{ blog.author.user.last_name }}",
            "url": "https://librainian.com/blogs/author/{{ blog.author.user.username }}/"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "logo": {
                "@type": "ImageObject",
                "url": "https://librainian.com/static/img/logo_trans_name.png",
                "width": 300,
                "height": 100
            },
            "url": "https://librainian.com/"
        },
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "https://librainian.com/blogs/p/{{ blog.slug }}/"
        },
        "keywords": "{{ blog.meta_keywords|default:'library management, library blog, educational resources'|escapejs }}",
        "articleSection": "{{ blog.category }}",
        "wordCount": "{{ blog.content|wordcount }}",
        "inLanguage": "en-US",
        "url": "https://librainian.com/blogs/p/{{ blog.slug }}/"
    }
    </script>

    <!-- BreadcrumbList Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://librainian.com/"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Blog",
                "item": "https://librainian.com/blogs/p/"
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": "{{ blog.category }}",
                "item": "https://librainian.com/blogs/p/cat/{{ blog.category|slugify }}/"
            },
            {
                "@type": "ListItem",
                "position": 4,
                "name": "{{ blog.title|truncatechars:50|escapejs }}",
                "item": "https://librainian.com/blogs/p/{{ blog.slug }}/"
            }
        ]
    }
    </script>

    <!-- WebSite Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Librainian Blog",
        "url": "https://librainian.com/blogs/p/",
        "description": "Insights, tips, and best practices for library management and educational resources.",
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "url": "https://librainian.com/"
        },
        "potentialAction": {
            "@type": "SearchAction",
            "target": {
                "@type": "EntryPoint",
                "urlTemplate": "https://librainian.com/blogs/search/?q={search_term_string}"
            },
            "query-input": "required name=search_term_string"
        }
    }
    </script>

    <!-- BreadcrumbList Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://librainian.com/"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Blog",
                "item": "https://librainian.com/blogs/p/"
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": "{{ blog.category }}",
                "item": "https://librainian.com/blogs/p/cat/{{ blog.category|slugify }}/"
            },
            {
                "@type": "ListItem",
                "position": 4,
                "name": "{{ blog.title|truncatechars:50|escapejs }}",
                "item": "https://librainian.com/blogs/p/{{ blog.slug }}/"
            }
        ]
    }
    </script>

    <!-- WebSite Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Librainian Blog",
        "url": "https://librainian.com/blogs/p/",
        "description": "Insights, tips, and best practices for library management and educational resources.",
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "url": "https://librainian.com/"
        },
        "potentialAction": {
            "@type": "SearchAction",
            "target": {
                "@type": "EntryPoint",
                "urlTemplate": "https://librainian.com/blogs/search/?q={search_term_string}"
            },
            "query-input": "required name=search_term_string"
        }
    }
    </script>

    <!-- Favicon -->
    <link href="https://i.postimg.cc/ZY3b6dw9/librainian-logo-black-transparent-med.png" rel="icon">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">

    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --border-radius: 12px;
            --border-radius-lg: 20px;
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            margin: 0;
            padding: 0;
            color: var(--gray-900);
            line-height: 1.7;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Modern Navigation */
        .modern-navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: 1rem 0;
        }

        .navbar-brand {
            font-family: 'Playfair Display', serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary) !important;
            text-decoration: none;
        }

        .navbar-nav .nav-link {
            color: var(--gray-700) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: var(--border-radius);
            transition: var(--transition);
            margin: 0 0.25rem;
        }

        .navbar-nav .nav-link:hover {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary) !important;
        }

        .btn-modern {
            padding: 0.5rem 1.5rem;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: 0.875rem;
            transition: var(--transition);
            border: 2px solid;
        }

        .btn-outline-modern {
            background: transparent;
            border-color: var(--primary);
            color: var(--primary);
        }

        .btn-outline-modern:hover {
            background: var(--primary);
            color: white;
        }

        .btn-primary-modern {
            background: var(--primary);
            border-color: var(--primary);
            color: white;
        }

        .btn-primary-modern:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
            color: white;
        }

        /* Main Content */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .blog-article {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .blog-header {
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .blog-title {
            font-family: 'Playfair Display', serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .blog-meta {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1.5rem;
            flex-wrap: wrap;
            margin-bottom: 1.5rem;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.8);
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            color: var(--gray-600);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .blog-description {
            font-size: 1.125rem;
            color: var(--gray-700);
            font-style: italic;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .blog-image-container {
            text-align: center;
            margin: 2rem 0;
        }

        .blog-image {
            max-width: 100%;
            height: auto;
            max-height: 500px;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            object-fit: cover;
            transition: var(--transition);
        }

        .blog-image:hover {
            transform: scale(1.02);
            box-shadow: var(--shadow-xl);
        }

        .blog-content {
            padding: 2rem;
            color: var(--gray-800);
            line-height: 1.8;
        }

        .blog-content h1,
        .blog-content h2,
        .blog-content h3,
        .blog-content h4,
        .blog-content h5,
        .blog-content h6 {
            color: var(--gray-900);
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .blog-content p {
            margin-bottom: 1.5rem;
            text-align: justify;
        }

        .blog-content img {
            max-width: 100%;
            height: auto;
            border-radius: var(--border-radius);
            margin: 1.5rem 0;
            box-shadow: var(--shadow-md);
        }

        .blog-content blockquote {
            background: rgba(99, 102, 241, 0.1);
            border-left: 4px solid var(--primary);
            padding: 1rem 1.5rem;
            margin: 1.5rem 0;
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
            font-style: italic;
        }

        /* Related Articles */
        .related-articles {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xl);
            padding: 2rem;
        }

        .related-articles h3 {
            color: var(--gray-900);
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .related-card {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: var(--transition);
            text-decoration: none;
            color: inherit;
            display: block;
        }

        .related-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            text-decoration: none;
            color: inherit;
        }

        .related-card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
        }

        .related-card-date {
            font-size: 0.875rem;
            color: var(--gray-600);
            margin-bottom: 0.75rem;
        }

        /* Social Media */
        .social-media {
            position: fixed;
            right: 2rem;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 1rem;
            z-index: 1000;
        }

        .social-media a {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-700);
            font-size: 1.25rem;
            transition: var(--transition);
            text-decoration: none;
            box-shadow: var(--shadow-lg);
        }

        .social-media a:hover {
            transform: scale(1.1);
            color: var(--primary);
            box-shadow: var(--shadow-xl);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .blog-title {
                font-size: 1.875rem;
            }
            
            .blog-meta {
                flex-direction: column;
                gap: 1rem;
            }
            
            .blog-header,
            .blog-content {
                padding: 1.5rem;
            }
            
            .social-media {
                position: static;
                transform: none;
                flex-direction: row;
                justify-content: center;
                margin: 2rem 0;
            }
            
            .main-container {
                padding: 1rem;
            }
        }
    </style>

    <script>
        function trimDescription(description, maxLength) {
            if (description.length > maxLength) {
                return description.substring(0, maxLength - 3) + "...";
            }
            return description;
        }

        document.addEventListener("DOMContentLoaded", function () {
            var metaDescription = "{{ blog.description|escapejs }}";
            var trimmedDescription = trimDescription(metaDescription, 160);

            // Set the content for meta tags
            document.getElementById("metaDescription").setAttribute("content", trimmedDescription);
            document.getElementById("ogDescription").setAttribute("content", trimmedDescription);
            document.getElementById("twitterDescription").setAttribute("content", trimmedDescription);
        });
    </script>

    <style>
        /* Dark Mode Styles for Blog Display Page */
        body.dark-mode {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            color: white;
        }

        body.dark-mode .main-container {
            background: transparent;
        }

        body.dark-mode .breadcrumb {
            background: rgba(31, 41, 55, 0.8);
            border-color: rgba(75, 85, 99, 0.3);
        }

        body.dark-mode .breadcrumb a {
            color: rgba(255, 255, 255, 0.8);
        }

        body.dark-mode .breadcrumb a:hover {
            color: white;
        }

        body.dark-mode .blog-header h1 {
            color: white;
        }

        body.dark-mode .blog-meta {
            color: rgba(255, 255, 255, 0.7);
        }

        body.dark-mode .blog-content {
            background: rgba(31, 41, 55, 0.8);
            border-color: rgba(75, 85, 99, 0.3);
            color: white;
        }

        body.dark-mode .blog-content h1,
        body.dark-mode .blog-content h2,
        body.dark-mode .blog-content h3,
        body.dark-mode .blog-content h4,
        body.dark-mode .blog-content h5,
        body.dark-mode .blog-content h6 {
            color: white;
        }

        body.dark-mode .blog-content p {
            color: rgba(255, 255, 255, 0.9);
        }

        body.dark-mode .blog-content blockquote {
            background: rgba(17, 24, 39, 0.8);
            border-left-color: #6366f1;
            color: rgba(255, 255, 255, 0.9);
        }

        body.dark-mode .blog-content code {
            background: rgba(17, 24, 39, 0.8);
            color: #fbbf24;
        }

        body.dark-mode .blog-content pre {
            background: rgba(17, 24, 39, 0.9);
            border-color: rgba(75, 85, 99, 0.3);
        }

        body.dark-mode .related-posts {
            background: rgba(31, 41, 55, 0.8);
            border-color: rgba(75, 85, 99, 0.3);
        }

        body.dark-mode .related-post-card {
            background: rgba(17, 24, 39, 0.8);
            border-color: rgba(75, 85, 99, 0.3);
        }

        body.dark-mode .related-post-card:hover {
            background: rgba(31, 41, 55, 0.9);
        }

        body.dark-mode .related-post-title {
            color: white;
        }

        body.dark-mode .related-post-meta {
            color: rgba(255, 255, 255, 0.7);
        }

        body.dark-mode .btn-primary-modern {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
        }

        body.dark-mode .social-share {
            background: rgba(31, 41, 55, 0.8);
            border-color: rgba(75, 85, 99, 0.3);
        }

        body.dark-mode .share-button {
            background: rgba(17, 24, 39, 0.8);
            color: white;
            border-color: rgba(75, 85, 99, 0.3);
        }

        body.dark-mode .share-button:hover {
            background: rgba(99, 102, 241, 0.2);
            color: white;
        }
    </style>

    <!-- Blog Display Dark Mode Initialization -->
    <script>
        // Immediate dark mode application for blog display page
        (function() {
            const isDarkMode = localStorage.getItem('theme') === 'dark';
            if (isDarkMode) {
                document.documentElement.classList.add('dark-mode-immediate');
                document.body.classList.add('dark-mode');
            }
        })();
    </script>
</head>
<body style="padding-top: 80px;">
    <!-- Include Public Navigation Body -->
    {% with navbar_section="body" %}
        {% include "public_navbar.html" %}
    {% endwith %}

    <!-- Main Content -->
    <main class="main-container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="/" class="text-decoration-none">
                        <i class="fas fa-home me-1"></i>
                        Home
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="/blogs/p" class="text-decoration-none">Blogs</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="/blogs/p/cat/{{ blog.category }}/" class="text-decoration-none">{{ blog.category }}</a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">{{ blog.title|truncatechars:50 }}</li>
            </ol>
        </nav>

        <div class="row">
            <!-- Blog Article -->
            <div class="col-lg-8">
                <article class="blog-article">
                    <!-- Blog Header -->
                    <div class="blog-header">
                        <h1 class="blog-title">{{ blog.title }}</h1>

                        <div class="blog-meta">
                            <div class="meta-item">
                                <i class="fas fa-user text-primary"></i>
                                <span>{{ blog.author.user.first_name }} {{ blog.author.user.last_name }}</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-calendar text-success"></i>
                                <span>{{ blog.published_date|date:"M d, Y" }}</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-clock text-warning"></i>
                                <span>{{ blog.updated_at|date:"g:i A" }}</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-tag text-info"></i>
                                <span>{{ blog.category }}</span>
                            </div>
                        </div>

                        {% if blog.description %}
                        <p class="blog-description">{{ blog.description }}</p>
                        {% endif %}

                        <!-- Blog Image -->
                        {% if blog.image %}
                        <div class="blog-image-container">
                            <img src="{{ blog.image.url }}"
                                 class="blog-image"
                                 alt="{{ blog.title }}"
                                 loading="lazy">
                        </div>
                        {% endif %}
                    </div>

                    <!-- Blog Content -->
                    <div class="blog-content">
                        {{ blog.content|safe }}
                    </div>
                </article>
            </div>

            <!-- Related Articles Sidebar -->
            <div class="col-lg-4">
                <aside class="related-articles">
                    <h3>
                        <i class="fas fa-newspaper me-2"></i>
                        Related Articles
                    </h3>

                    <div id="relatedArticlesContainer">
                        {% for related_blog in blogs %}
                        <a href="/blogs/p/{{ related_blog.slug }}/" class="related-card">
                            <h4 class="related-card-title">{{ related_blog.title|truncatechars:60 }}</h4>
                            <p class="related-card-date">
                                <i class="fas fa-calendar me-1"></i>
                                {{ related_blog.date_created|date:"M d, Y" }}
                            </p>
                            <p class="text-muted">
                                {{ related_blog.description|truncatewords:15|safe }}
                            </p>
                        </a>
                        {% empty %}
                        <div class="text-center text-muted">
                            <i class="fas fa-info-circle me-2"></i>
                            No related articles found.
                        </div>
                        {% endfor %}
                    </div>
                </aside>
            </div>
        </div>
    </main>

    <!-- Social Media -->
    <div class="social-media">
        <a href="https://www.facebook.com/profile.php?id=61562707884730"
           title="Follow us on Facebook"
           target="_blank"
           rel="noopener noreferrer">
            <i class="fab fa-facebook-f"></i>
        </a>
        <a href="https://x.com/Librainian_app?t=ge9yi-sL_8SFXeWGZS9apQ&s=09"
           title="Follow us on Twitter"
           target="_blank"
           rel="noopener noreferrer">
            <i class="fab fa-twitter"></i>
        </a>
        <a href="https://www.instagram.com/librainian.app/"
           title="Follow us on Instagram"
           target="_blank"
           rel="noopener noreferrer">
            <i class="fab fa-instagram"></i>
        </a>
        <a href="https://www.linkedin.com/company/pinak-venture/"
           title="Follow us on LinkedIn"
           target="_blank"
           rel="noopener noreferrer">
            <i class="fab fa-linkedin-in"></i>
        </a>
    </div>

    <!-- Bootstrap 5.3.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add reading progress indicator
        window.addEventListener('scroll', function() {
            const article = document.querySelector('.blog-article');
            if (article) {
                const articleTop = article.offsetTop;
                const articleHeight = article.offsetHeight;
                const windowHeight = window.innerHeight;
                const scrollTop = window.pageYOffset;

                const progress = Math.min(100, Math.max(0,
                    ((scrollTop - articleTop + windowHeight) / articleHeight) * 100
                ));

                // You can use this progress value to show a reading progress bar
                console.log('Reading progress:', Math.round(progress) + '%');
            }
        });
    </script>
</body>
</html>
