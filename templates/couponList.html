{% extends "base.html" %}

{% block title %}Coupon Management - Librainian{% endblock %}

{% block page_title %}Coupon Management{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="/blogs/">Blogs</a></li>
<li class="breadcrumb-item active" aria-current="page">Coupons</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Coupon Management Styles */
    .coupon-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .coupon-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 1.5rem;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .coupon-header h2 {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .coupon-header p {
        opacity: 0.9;
        margin: 0;
    }

    .create-coupon-btn {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        color: white;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        margin-bottom: 2rem;
    }

    .create-coupon-btn:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
    }

    .coupon-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .coupon-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .coupon-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, transparent 0%, rgba(99, 102, 241, 0.05) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .coupon-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    .coupon-card:hover::before {
        opacity: 1;
    }

    .coupon-header-card {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 1;
    }

    .coupon-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    .coupon-code {
        font-size: 1.25rem;
        font-weight: 700;
        color: #1f2937;
        margin: 0;
    }

    .coupon-details {
        position: relative;
        z-index: 1;
    }

    .coupon-detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(156, 163, 175, 0.2);
    }

    .coupon-detail-item:last-child {
        border-bottom: none;
    }

    .coupon-detail-label {
        font-weight: 600;
        color: #6b7280;
        font-size: 0.875rem;
    }

    .coupon-detail-value {
        font-weight: 700;
        color: #1f2937;
    }

    .discount-value {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .coupon-actions {
        display: flex;
        gap: 0.75rem;
        margin-top: 1.5rem;
        position: relative;
        z-index: 1;
    }

    .action-btn {
        flex: 1;
        padding: 0.75rem;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        text-align: center;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .edit-btn {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        border: none;
    }

    .edit-btn:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    }

    .delete-btn {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        border: none;
    }

    .delete-btn:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #6b7280;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .empty-state h3 {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #374151;
    }

    .empty-state p {
        font-size: 1.125rem;
        margin: 0;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .coupon-container {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .coupon-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .coupon-card {
            padding: 1rem;
        }

        .coupon-actions {
            flex-direction: column;
        }

        .action-btn {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="coupon-container fade-in">
    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    {% endif %}

    <!-- Header -->
    <div class="coupon-header">
        <h2><i class="fas fa-ticket-alt me-2"></i>Coupon Management</h2>
        <p>Manage discount coupons and promotional offers</p>
    </div>

    <!-- Create Coupon Button -->
    <a href="/blogs/coupons/coupon-form/" class="create-coupon-btn">
        <i class="fas fa-plus-circle"></i>
        Create New Coupon
    </a>

    <!-- Coupon Grid -->
    <div class="coupon-grid">
        {% for coupon in coupons %}
            <div class="coupon-card">
                <div class="coupon-header-card">
                    <div class="coupon-icon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <h5 class="coupon-code">{{ coupon.code }}</h5>
                </div>

                <div class="coupon-details">
                    <div class="coupon-detail-item">
                        <span class="coupon-detail-label">Discount</span>
                        <span class="discount-value">
                            {% if coupon.discount_type == 'amount' %}
                                ₹{{ coupon.discount }}
                            {% else %}
                                {{ coupon.discount }}%
                            {% endif %}
                        </span>
                    </div>
                    <div class="coupon-detail-item">
                        <span class="coupon-detail-label">Expiry Date</span>
                        <span class="coupon-detail-value">{{ coupon.expiry_date }}</span>
                    </div>
                    <div class="coupon-detail-item">
                        <span class="coupon-detail-label">Usage Limit</span>
                        <span class="coupon-detail-value">{{ coupon.usage_limit }}</span>
                    </div>
                    <div class="coupon-detail-item">
                        <span class="coupon-detail-label">Total Discount</span>
                        <span class="coupon-detail-value">₹{{ coupon.overallDiscountAmount }}</span>
                    </div>
                </div>

                <div class="coupon-actions">
                    <a href="/blogs/coupons/coupon_detail_view/{{ coupon.id }}" class="action-btn edit-btn">
                        <i class="fas fa-edit"></i>
                        Edit
                    </a>
                    <a href="/blogs/coupons/coupon_delete/{{ coupon.id }}"
                       class="action-btn delete-btn"
                       onclick="return confirm('Are you sure you want to delete this coupon?');">
                        <i class="fas fa-trash"></i>
                        Delete
                    </a>
                </div>
            </div>
        {% empty %}
            <div class="empty-state">
                <i class="fas fa-ticket-alt"></i>
                <h3>No Coupons Available</h3>
                <p>Create your first coupon to get started with promotional offers.</p>
            </div>
        {% endfor %}
    </div>
</div>
{% endblock %}
