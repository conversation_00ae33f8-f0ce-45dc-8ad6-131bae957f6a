{% extends "base.html" %}

{% block title %}Librarian Approval - Librainian{% endblock %}

{% block page_title %}Librarian Approval{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Librarian Approval</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Librarian Approval Styles */
    .approval-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }

    .approval-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 2rem;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .approval-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .approval-header h1 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 1;
    }

    .approval-header p {
        font-size: 1.125rem;
        opacity: 0.9;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    .librarian-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .librarian-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
        pointer-events: none;
    }

    .card-header {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 16px 16px 0 0;
        margin: -2rem -2rem 2rem -2rem;
        position: relative;
        z-index: 1;
    }

    .card-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .info-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin-bottom: 2rem;
        position: relative;
        z-index: 1;
    }

    .info-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(156, 163, 175, 0.2);
        border-radius: 12px;
        transition: all 0.3s ease;
    }

    .info-item:hover {
        background: rgba(255, 255, 255, 1);
        border-color: #6366f1;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .info-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .info-icon i {
        font-size: 1.25rem;
        color: white;
    }

    .info-content {
        flex: 1;
    }

    .info-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: #6b7280;
        margin-bottom: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .info-value {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        word-break: break-word;
    }

    .approval-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 2px solid rgba(156, 163, 175, 0.2);
        position: relative;
        z-index: 1;
    }

    .btn-approve {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border: none;
        border-radius: 12px;
        padding: 1rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-approve:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }

    .btn-reject {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        border: none;
        border-radius: 12px;
        padding: 1rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-reject:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-pending {
        background: rgba(245, 158, 11, 0.2);
        color: #d97706;
        border: 1px solid rgba(245, 158, 11, 0.3);
    }

    .status-approved {
        background: rgba(16, 185, 129, 0.2);
        color: #059669;
        border: 1px solid rgba(16, 185, 129, 0.3);
    }

    .status-rejected {
        background: rgba(239, 68, 68, 0.2);
        color: #dc2626;
        border: 1px solid rgba(239, 68, 68, 0.3);
    }

    .warning-message {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        text-align: center;
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    }

    .warning-message i {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .approval-container {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .approval-header {
            padding: 1.5rem 1rem;
        }

        .approval-header h1 {
            font-size: 1.5rem;
        }

        .librarian-card {
            padding: 1rem;
        }

        .card-header {
            padding: 1rem;
            margin: -1rem -1rem 1rem -1rem;
        }

        .card-title {
            font-size: 1.25rem;
        }

        .info-item {
            padding: 0.75rem;
        }

        .info-icon {
            width: 40px;
            height: 40px;
        }

        .info-icon i {
            font-size: 1rem;
        }

        .approval-actions {
            flex-direction: column;
            gap: 0.75rem;
        }

        .btn-approve,
        .btn-reject {
            width: 100%;
            justify-content: center;
            padding: 0.875rem 1.5rem;
            font-size: 1rem;
        }
    }

    /* Animation */
    .fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="approval-container fade-in">
    <!-- Approval Header -->
    <div class="approval-header">
        <h1><i class="fas fa-user-check me-2"></i>Librarian Approval Dashboard</h1>
        <p>Review and approve librarian registration requests</p>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    {% endif %}

    <!-- Warning Message -->
    <div class="warning-message">
        <i class="fas fa-exclamation-triangle d-block"></i>
        <strong>Important:</strong> Please carefully review all librarian details before making an approval decision.
        This action cannot be undone.
    </div>

    <!-- Librarian Details Card -->
    <div class="librarian-card">
        <div class="card-header">
            <div class="card-title">
                <i class="fas fa-building"></i>
                {{ librarian.library_name }}
            </div>
        </div>

        <!-- Librarian Information -->
        <div class="info-grid">
            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-user-tie"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">Username</div>
                    <div class="info-value">{{ librarian.user.username }}</div>
                </div>
            </div>

            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-user"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">Full Name</div>
                    <div class="info-value">{{ librarian.user.first_name }} {{ librarian.user.last_name }}</div>
                </div>
            </div>

            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">Email Address</div>
                    <div class="info-value">{{ librarian.user.email }}</div>
                </div>
            </div>

            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-phone"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">Phone Number</div>
                    <div class="info-value">{{ librarian.librarian_phone_num|default:"Not provided" }}</div>
                </div>
            </div>

            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">Library Address</div>
                    <div class="info-value">{{ librarian.librarian_address|default:"Not provided" }}</div>
                </div>
            </div>

            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">Registration Date</div>
                    <div class="info-value">{{ librarian.user.date_joined|date:"F d, Y" }}</div>
                </div>
            </div>

            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">Current Status</div>
                    <div class="info-value">
                        {% if librarian.is_approved %}
                            <span class="status-badge status-approved">
                                <i class="fas fa-check-circle"></i>
                                Approved
                            </span>
                        {% elif librarian.is_rejected %}
                            <span class="status-badge status-rejected">
                                <i class="fas fa-times-circle"></i>
                                Rejected
                            </span>
                        {% else %}
                            <span class="status-badge status-pending">
                                <i class="fas fa-clock"></i>
                                Pending Approval
                            </span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Approval Actions -->
        {% if not librarian.is_approved and not librarian.is_rejected %}
        <form method="POST" id="approvalForm" class="approval-actions">
            {% csrf_token %}
            <input type="hidden" name="librarian_id" value="{{ librarian.id }}">

            <button type="submit" name="approval_action" value="approve" class="btn-approve" id="approveBtn">
                <i class="fas fa-check"></i>
                Approve Librarian
            </button>

            <button type="submit" name="approval_action" value="reject" class="btn-reject" id="rejectBtn">
                <i class="fas fa-times"></i>
                Reject Application
            </button>
        </form>
        {% else %}
        <div class="approval-actions">
            <div class="text-center">
                {% if librarian.is_approved %}
                    <p class="text-success mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        This librarian has already been approved.
                    </p>
                {% else %}
                    <p class="text-danger mb-0">
                        <i class="fas fa-times-circle me-2"></i>
                        This application has been rejected.
                    </p>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Enhanced Approval System
    class ApprovalManager {
        constructor() {
            this.form = document.getElementById('approvalForm');
            this.approveBtn = document.getElementById('approveBtn');
            this.rejectBtn = document.getElementById('rejectBtn');
            this.init();
        }

        init() {
            if (this.form) {
                this.setupFormSubmission();
            }
        }

        setupFormSubmission() {
            this.form.addEventListener('submit', (e) => {
                e.preventDefault();

                const action = e.submitter.value;
                const actionText = action === 'approve' ? 'approve' : 'reject';
                const confirmMessage = `Are you sure you want to ${actionText} this librarian application?`;

                if (confirm(confirmMessage)) {
                    this.submitForm(e.submitter, action);
                }
            });
        }

        submitForm(button, action) {
            const originalText = button.innerHTML;
            const loadingText = action === 'approve' ?
                '<i class="fas fa-spinner fa-spin me-2"></i>Approving...' :
                '<i class="fas fa-spinner fa-spin me-2"></i>Rejecting...';

            // Show loading state
            button.innerHTML = loadingText;
            button.disabled = true;

            // Disable other button
            const otherButton = action === 'approve' ? this.rejectBtn : this.approveBtn;
            if (otherButton) {
                otherButton.disabled = true;
            }

            // Submit the form
            this.form.submit();
        }

        showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    }

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        window.approvalManager = new ApprovalManager();

        // Add hover effects to info items
        document.querySelectorAll('.info-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Add animation to elements when they come into view
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe info items
        document.querySelectorAll('.info-item').forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            item.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
            observer.observe(item);
        });
    });
</script>
{% endblock %}
