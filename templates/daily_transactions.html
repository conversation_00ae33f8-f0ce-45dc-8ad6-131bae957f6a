{% extends "base.html" %}

{% block title %}Daily Transactions - Librainian{% endblock %}

{% block content %}
<div class="daily-transactions-content fade-in">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12 col-lg-12">
                <div class="row">
                    <div class="col-12 col-lg-6">
                        <div class="card">
                            <div class="card-header card_heading">
                                Daily Transaction Summary
                            </div>
                            <div class="card-body">
                                <!-- Displaying messages -->
                                {% if messages %}
                                {% for message in messages %}
                                <div class="alert alert-primary alert-dismissible fade show" role="alert">
                                    {{ message }}
                                </div>
                                {% endfor %}
                                {% endif %}

                                <!-- Form to submit daily transaction summary -->
                                <form method="post">
                                    {% csrf_token %}

                                    <!-- Opening Balance -->
                                    <div class="form-group row">
                                        <label for="opening_balance" class="col-sm-4 col-form-label">Opening
                                            Balance:</label>
                                        <div class="col-sm-8">
                                            <input type="text" id="opening_balance" name="opening_balance"
                                                class="form-control" value="{{ opening_balance }}">
                                        </div>
                                    </div>

                                    <!-- Other Income (Cash) -->
                                    <div class="form-group row">
                                        <label for="other_income_cash" class="col-sm-4 col-form-label">Other Income
                                            (Cash):</label>
                                        <div class="col-sm-8">
                                            <input type="text" id="other_income_cash" name="other_income_cash"
                                                class="form-control" value="{{ other_income_cash }}">
                                        </div>
                                    </div>

                                    <!-- Cash Deposit -->
                                    <div class="form-group row">
                                        <label for="deposit" class="col-sm-4 col-form-label">Cash Deposit:</label>
                                        <div class="col-sm-8">
                                            <input type="text" id="deposit" name="deposit" class="form-control"
                                                value="{{ deposit }}">
                                        </div>
                                    </div>

                                    <!-- Sales Return (Cash) -->
                                    <div class="form-group row">
                                        <label for="sales_return_cash" class="col-sm-4 col-form-label">Revenue Return
                                            (Cash):</label>
                                        <div class="col-sm-8">
                                            <input type="text" id="sales_return_cash" name="sales_return_cash"
                                                class="form-control" value="{{ sales_return_cash }}">
                                        </div>
                                    </div>

                                    <!-- Other Expenses (Cash) -->
                                    <div class="form-group row">
                                        <label for="other_expenses_cash" class="col-sm-4 col-form-label">Other Expenses
                                            (Cash):</label>
                                        <div class="col-sm-8">
                                            <input type="text" id="other_expenses_cash" name="other_expenses_cash"
                                                class="form-control" value="{{ other_expenses_cash }}">
                                        </div>
                                    </div>

                                    <!-- Save Button -->
                                    <div class="form-group row">
                                        <div class="col-sm-12 text-center">
                                            <button type="submit" class="btn btn-md btn-md-lg" style="background: #294282 !important; color: white;">Save</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="container my-3">
                            <div class="d-flex justify-content-center">
                                {%if role != 'sublibrarian'%}

                                <a href="/librarian/transaction-report/" class="btn btn-md btn-md-lg" style="background: #294282 !important; color: white;">Go to
                                    Transaction Report</a>
                                {%endif%}
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-lg-6">
                        <div class="card">
                            <div class="card-header card_heading">
                                Cash Transaction Summary
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Field</th>
                                            <th>Value</th>
                                        </tr>
                                    </thead>
                                    <tbody class="overflow-hidden">
                                        <!-- Opening Balance -->
                                        <tr>
                                            <td>Opening Balance</td>
                                            <td>{{ transactions.opening_balance }}</td>
                                        </tr>

                                        <!-- Other Income (Cash) -->
                                        <tr>
                                            <td>Other Income (Cash)</td>
                                            <td>{{ transactions.other_income_cash }}</td>
                                        </tr>

                                        <!-- Cash Deposit -->
                                        <tr>
                                            <td>Cash Deposit</td>
                                            <td>{{ transactions.deposit }}</td>
                                        </tr>

                                        <!-- Sales Return (Cash) -->
                                        <tr>
                                            <td>Revenue Return (Cash)</td>
                                            <td>{{ transactions.sales_return_cash }}</td>
                                        </tr>

                                        <!-- Other Expenses (Cash) -->
                                        <tr>
                                            <td>Other Expenses (Cash)</td>
                                            <td>{{ transactions.other_expenses_cash }}</td>
                                        </tr>

                                        <tr class="table_fansy_row">
                                            <td>Today Closing Balance</td>
                                            <td>{{ transactions.closing_balance_cash }}</td>
                                        </tr>
                                        <tr class="">
                                        </tr>

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-10">
                        <div class="card mt-3 mt-md-4 mt-lg-5">
                            <div class="card-header card_heading">
                                Overall Transaction Summary
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Field</th>
                                            <th>Value</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Opening Balance -->
                                        <tr>
                                            <td>Opening Balance</td>
                                            <td>{{ transactions.opening_balance }}</td>
                                        </tr>

                                        <!-- Sales Student Fees Collection (Online)-->
                                        <tr>
                                            <td>Revenue (Online)</td>
                                            <td>{{ transactions.sales_online }}</td>
                                        </tr>

                                        <tr>
                                            <td>Revenue  (Cash)</td>
                                            <td>{{ transactions.sales_cash }}</td>
                                        </tr>

                                        <!-- Other Income (Cash) -->
                                        <tr>
                                            <td>Other Income (Cash)</td>
                                            <td>{{ transactions.other_income_cash }}</td>
                                        </tr>


                                        <!-- Other Income (Online) -->
                                        <tr>
                                            <td>Other Income (Online)</td>
                                            <td>{{ transactions.other_income_online }}</td>
                                        </tr>

                                        <!-- Cash Deposit -->
                                        <tr>
                                            <td>Cash Deposit</td>
                                            <td>{{ transactions.deposit }}</td>
                                        </tr>

                                        <!-- Sales Return (Cash) -->
                                        <tr>
                                            <td>Revenue Return (Cash)</td>
                                            <td>{{ transactions.sales_return_cash }}</td>
                                        </tr>

                                        <!-- Sales Return (Online) -->
                                        <tr>
                                            <td>Revenue Return (Online)</td>
                                            <td>{{ transactions.sales_return_online }}</td>
                                        </tr>

                                        <!-- Other Expenses (Cash) -->
                                        <tr>
                                            <td>Other Expenses (Cash)</td>
                                            <td>{{ transactions.other_expenses_cash }}</td>
                                        </tr>

                                        <!-- Other Expenses (Online) -->
                                        <tr>
                                            <td>Other Expenses (Online)</td>
                                            <td>{{ transactions.sales_return_online }}</td>
                                        </tr>

                                        <tr class="table_fansy_row">
                                            <td>Today Closing Balance</td>
                                            <td>{{ transactions.closing_balance_online }}</td>
                                        </tr>

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer">
                    <!-- <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy"
                        style="max-width: 100%"> -->
                    <!-- <p>Developed with passion by Librainian</p> -->
                </div>
            </div>
        </div>
    </div>








    </div>



    <!-- JavaScript dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Show success message and hide after 10 seconds
        document.addEventListener('DOMContentLoaded', function () {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function (alert) {
                setTimeout(function () {
                    alert.style.transition = 'opacity 1s';
                    alert.style.opacity = '0';
                    setTimeout(function () {
                        alert.style.display = 'none';
                    }, 1000);
                }, 10000);
            });
        });
    </script>
    <script>
        // Get the menu icon and submenu elements
        const menuIcon = document.getElementById('menu-icon');
        const submenu = document.getElementById('submenu');

        // Add click event listener to the menu icon
        menuIcon.addEventListener('click', function () {
            submenu.classList.toggle('show');
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var menuIcon = document.getElementById('menu-icon');
            var submenu = document.getElementById('submenu');

            // Toggle submenu visibility on menu icon click
            menuIcon.addEventListener('click', function () {
                if (submenu.style.display === 'block') {
                    submenu.style.display = 'none';
                } else {
                    submenu.style.display = 'block';
                }
            });
        });

    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var footerSearch = document.getElementById('footer-search');
            var exampleModal = new bootstrap.Modal(document.getElementById('exampleModal'));

            footerSearch.addEventListener('click', function () {
                exampleModal.show();
            });
        });
    </script>
    <script>
        function updateDateTime() {
            const now = new Date();
            const date = now.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
            const time = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

            document.getElementById('date').textContent = 'Date: ' + date;
            document.getElementById('time').textContent = 'Time: ' + time;
        }

        // Update the date and time on page load
        updateDateTime();

        // Update the date and time every minute
        setInterval(updateDateTime, 60000);
    </script>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.5.0/font/bootstrap-icons.min.css">
<style>
    /* Daily Transactions Page Styling */
    .daily-transactions-content {
        min-height: calc(100vh - 160px);
        padding: 1.5rem;
    }

    /* Transparent Glass Cards */
    .card {
        background: rgba(255, 255, 255, 0.15) !important;
        backdrop-filter: blur(20px) !important;
        -webkit-backdrop-filter: blur(20px) !important;
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
        border-radius: 16px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.3s ease !important;
        overflow: hidden !important;
        margin-bottom: 1.5rem !important;
    }

    .card:hover {
        transform: translateY(-3px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
    }

    /* Card Headers */
    .card-header {
        background: rgba(255, 255, 255, 0.1) !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        font-weight: 700 !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
        font-size: 1.1rem !important;
        padding: 1.25rem 1.5rem !important;
    }

    .card_heading {
        background: rgba(255, 255, 255, 0.1) !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        font-weight: 700 !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
    }

    /* Card Body */
    .card-body {
        padding: 1.5rem !important;
        color: white !important;
    }

    /* Form Styling */
    .form-group {
        margin-bottom: 1rem !important;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.15) !important;
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
        border-radius: 12px !important;
        color: white !important;
        padding: 0.75rem !important;
        backdrop-filter: blur(10px) !important;
        -webkit-backdrop-filter: blur(10px) !important;
        transition: all 0.3s ease !important;
        font-size: 0.875rem !important;
    }

    .form-control:focus {
        background: rgba(255, 255, 255, 0.2) !important;
        border-color: rgba(255, 255, 255, 0.5) !important;
        color: white !important;
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
    }

    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.7) !important;
    }

    /* Labels */
    .col-form-label {
        color: white !important;
        font-weight: 500 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    }

    /* Tables */
    .table {
        color: white !important;
        background: transparent !important;
        --bs-table-bg: transparent !important;
        --bs-table-striped-bg: rgba(255, 255, 255, 0.05) !important;
        --bs-table-hover-bg: rgba(255, 255, 255, 0.1) !important;
        --bs-table-border-color: rgba(255, 255, 255, 0.2) !important;
    }

    .table th {
        background: rgba(255, 255, 255, 0.1) !important;
        border-color: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        font-weight: 600 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    }

    .table td {
        background: transparent !important;
        border-color: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    }

    .table tbody tr {
        background: transparent !important;
    }

    .table tbody tr:nth-of-type(odd) {
        background: rgba(255, 255, 255, 0.05) !important;
    }

    .table tbody tr:hover {
        background: rgba(255, 255, 255, 0.1) !important;
    }

    .table-bordered {
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
    }

    .table-bordered th,
    .table-bordered td {
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
    }

    /* Force override any Bootstrap table backgrounds */
    .table > :not(caption) > * > * {
        background-color: transparent !important;
    }

    .table > tbody > tr > td,
    .table > tbody > tr > th,
    .table > tfoot > tr > td,
    .table > tfoot > tr > th,
    .table > thead > tr > td,
    .table > thead > tr > th {
        background-color: transparent !important;
    }

    /* Buttons */
    .btn-primary {
        background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
        border: none !important;
        border-radius: 12px !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3) !important;
        transition: all 0.3s ease !important;
    }

    .btn-primary:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 20px rgba(var(--primary-rgb), 0.4) !important;
    }

    /* Alerts */
    .alert {
        background: rgba(59, 130, 246, 0.2) !important;
        border: 1px solid rgba(59, 130, 246, 0.3) !important;
        color: white !important;
        backdrop-filter: blur(10px) !important;
        -webkit-backdrop-filter: blur(10px) !important;
        border-radius: 12px !important;
    }

    /* Responsive Design - Better fit at 100% zoom */
    @media (max-width: 1199.98px) {
        .daily-transactions-content {
            padding: 1rem;
        }

        .card-body {
            padding: 1.25rem !important;
        }

        .form-group.row {
            margin-bottom: 0.75rem !important;
        }
    }

    @media (max-width: 991.98px) {
        .daily-transactions-content {
            padding: 0.75rem;
        }

        .card {
            margin-bottom: 1rem !important;
        }

        .col-sm-4, .col-sm-8 {
            padding-left: 0.5rem !important;
            padding-right: 0.5rem !important;
        }
    }

    @media (max-width: 767.98px) {
        .form-group.row {
            margin-bottom: 0.5rem !important;
        }

        .col-form-label {
            margin-bottom: 0.25rem !important;
        }
    }

    /* Container adjustments for better 100% zoom fit */
    .container-fluid {
        max-width: 100%;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Row spacing adjustments */
    .row {
        margin-left: -0.5rem;
        margin-right: -0.5rem;
    }

    .row > [class*="col-"] {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
</style>
{% endblock %}