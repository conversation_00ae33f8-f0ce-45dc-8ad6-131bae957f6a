{% extends "base.html" %}

{% block title %}System Logs - Librainian{% endblock %}

{% block page_title %}System Logs{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">System Logs</li>
{% endblock %}

{% block extra_css %}
<style>
    /* System Logs Styles */
    .logs-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .logs-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 2rem;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .logs-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .logs-header h1 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 1;
    }

    .logs-header p {
        font-size: 1.125rem;
        opacity: 0.9;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    .log-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 2rem;
        align-items: center;
        justify-content: space-between;
    }

    .search-box {
        flex: 1;
        min-width: 250px;
        position: relative;
    }

    .search-input {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(156, 163, 175, 0.3);
        border-radius: 12px;
        padding: 0.75rem 1rem 0.75rem 3rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        width: 100%;
    }

    .search-input:focus {
        outline: none;
        border-color: #6366f1;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        background: rgba(255, 255, 255, 1);
    }

    .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6b7280;
        font-size: 1rem;
    }

    .filter-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .filter-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(99, 102, 241, 0.3);
        color: #6366f1;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 0.875rem;
    }

    .filter-btn:hover,
    .filter-btn.active {
        background: #6366f1;
        color: white;
        border-color: #6366f1;
    }

    .logs-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .log-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .log-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    .log-card-header {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        padding: 1.5rem;
        color: white;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .log-card-header.error {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }

    .log-title {
        font-size: 1.25rem;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .log-count {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 0.25rem 0.75rem;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .log-content {
        padding: 1.5rem;
    }

    .log-viewer {
        background: #1f2937;
        border-radius: 12px;
        padding: 1rem;
        height: 300px;
        overflow-y: auto;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 0.875rem;
        line-height: 1.5;
        color: #e5e7eb;
        margin-bottom: 1rem;
        position: relative;
    }

    .log-line {
        padding: 0.25rem 0;
        border-bottom: 1px solid rgba(75, 85, 99, 0.3);
        word-wrap: break-word;
        transition: background-color 0.2s ease;
    }

    .log-line:hover {
        background-color: rgba(75, 85, 99, 0.2);
    }

    .log-line.error {
        color: #fca5a5;
        background-color: rgba(239, 68, 68, 0.1);
    }

    .log-line.warning {
        color: #fcd34d;
        background-color: rgba(245, 158, 11, 0.1);
    }

    .log-line.info {
        color: #93c5fd;
        background-color: rgba(59, 130, 246, 0.1);
    }

    .download-btn {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        color: white;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        width: 100%;
        justify-content: center;
    }

    .download-btn:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    }

    .log-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background: rgba(243, 244, 246, 0.5);
        border-radius: 8px;
        margin-bottom: 1rem;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .refresh-btn {
        background: rgba(16, 185, 129, 0.1);
        border: 2px solid #10b981;
        color: #10b981;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 0.875rem;
    }

    .refresh-btn:hover {
        background: #10b981;
        color: white;
    }

    /* Custom scrollbar */
    .log-viewer::-webkit-scrollbar {
        width: 8px;
    }

    .log-viewer::-webkit-scrollbar-track {
        background: rgba(75, 85, 99, 0.3);
        border-radius: 4px;
    }

    .log-viewer::-webkit-scrollbar-thumb {
        background: rgba(156, 163, 175, 0.5);
        border-radius: 4px;
    }

    .log-viewer::-webkit-scrollbar-thumb:hover {
        background: rgba(156, 163, 175, 0.7);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .logs-container {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .logs-header {
            padding: 1.5rem 1rem;
        }

        .logs-header h1 {
            font-size: 1.5rem;
        }

        .logs-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .log-controls {
            flex-direction: column;
            align-items: stretch;
        }

        .search-box {
            min-width: auto;
        }

        .filter-buttons {
            justify-content: center;
        }
    }

    /* Animation */
    .fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="logs-container fade-in">
    <!-- Logs Header -->
    <div class="logs-header">
        <h1><i class="fas fa-file-alt me-2"></i>System Logs Dashboard</h1>
        <p>Monitor and download system access and error logs</p>
    </div>

    <!-- Log Controls -->
    <div class="log-controls">
        <div class="search-box">
            <i class="fas fa-search search-icon"></i>
            <input type="text" class="search-input" placeholder="Search logs..." id="logSearch">
        </div>
        <div class="filter-buttons">
            <button class="filter-btn active" data-filter="all">All</button>
            <button class="filter-btn" data-filter="error">Errors</button>
            <button class="filter-btn" data-filter="warning">Warnings</button>
            <button class="filter-btn" data-filter="info">Info</button>
            <button class="refresh-btn" onclick="refreshLogs()">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Logs Grid -->
    <div class="logs-grid">
        <!-- Access Log Card -->
        <div class="log-card">
            <div class="log-card-header">
                <h3 class="log-title">
                    <i class="fas fa-eye"></i>
                    Access Log
                </h3>
                <span class="log-count">{{ access_log_tail|length|default:"0" }} lines</span>
            </div>
            <div class="log-content">
                <div class="log-stats">
                    <span>Last updated: <strong id="accessLogTime">{{ current_time|default:"Now" }}</strong></span>
                    <span>Size: <strong>{{ access_log_size|default:"N/A" }}</strong></span>
                </div>
                <div class="log-viewer" id="accessLogViewer">
                    {% for line in access_log_tail %}
                        <div class="log-line" data-line="{{ forloop.counter }}">{{ line }}</div>
                    {% empty %}
                        <div class="log-line info">No access log entries found.</div>
                    {% endfor %}
                </div>
                <a href="{% url 'download_log' 'access' %}" class="download-btn" onclick="trackDownload('access')">
                    <i class="fas fa-download"></i>
                    Download Access Log
                </a>
            </div>
        </div>

        <!-- Error Log Card -->
        <div class="log-card">
            <div class="log-card-header error">
                <h3 class="log-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    Error Log
                </h3>
                <span class="log-count">{{ error_log_tail|length|default:"0" }} lines</span>
            </div>
            <div class="log-content">
                <div class="log-stats">
                    <span>Last updated: <strong id="errorLogTime">{{ current_time|default:"Now" }}</strong></span>
                    <span>Size: <strong>{{ error_log_size|default:"N/A" }}</strong></span>
                </div>
                <div class="log-viewer" id="errorLogViewer">
                    {% for line in error_log_tail %}
                        <div class="log-line error" data-line="{{ forloop.counter }}">{{ line }}</div>
                    {% empty %}
                        <div class="log-line info">No error log entries found.</div>
                    {% endfor %}
                </div>
                <a href="{% url 'download_log' 'error' %}" class="download-btn" onclick="trackDownload('error')">
                    <i class="fas fa-download"></i>
                    Download Error Log
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Enhanced Log Management System
    class LogManager {
        constructor() {
            this.searchInput = document.getElementById('logSearch');
            this.filterButtons = document.querySelectorAll('.filter-btn');
            this.currentFilter = 'all';
            this.init();
        }

        init() {
            this.setupEventListeners();
            this.highlightLogTypes();
            this.autoRefresh();
        }

        setupEventListeners() {
            // Search functionality
            this.searchInput.addEventListener('input', (e) => {
                this.filterLogs(e.target.value);
            });

            // Filter buttons
            this.filterButtons.forEach(btn => {
                btn.addEventListener('click', (e) => {
                    this.setActiveFilter(e.target);
                    this.currentFilter = e.target.dataset.filter;
                    this.applyFilter();
                });
            });
        }

        setActiveFilter(activeBtn) {
            this.filterButtons.forEach(btn => btn.classList.remove('active'));
            activeBtn.classList.add('active');
        }

        filterLogs(searchTerm) {
            const logViewers = document.querySelectorAll('.log-viewer');

            logViewers.forEach(viewer => {
                const lines = viewer.querySelectorAll('.log-line');
                lines.forEach(line => {
                    const text = line.textContent.toLowerCase();
                    const matches = searchTerm === '' || text.includes(searchTerm.toLowerCase());
                    line.style.display = matches ? 'block' : 'none';
                });
            });
        }

        applyFilter() {
            const logViewers = document.querySelectorAll('.log-viewer');

            logViewers.forEach(viewer => {
                const lines = viewer.querySelectorAll('.log-line');
                lines.forEach(line => {
                    if (this.currentFilter === 'all') {
                        line.style.display = 'block';
                    } else {
                        const hasClass = line.classList.contains(this.currentFilter);
                        line.style.display = hasClass ? 'block' : 'none';
                    }
                });
            });
        }

        highlightLogTypes() {
            const logViewers = document.querySelectorAll('.log-viewer');

            logViewers.forEach(viewer => {
                const lines = viewer.querySelectorAll('.log-line');
                lines.forEach(line => {
                    const text = line.textContent.toLowerCase();

                    if (text.includes('error') || text.includes('exception') || text.includes('failed')) {
                        line.classList.add('error');
                    } else if (text.includes('warning') || text.includes('warn')) {
                        line.classList.add('warning');
                    } else if (text.includes('info') || text.includes('success')) {
                        line.classList.add('info');
                    }
                });
            });
        }

        autoRefresh() {
            // Auto-refresh logs every 30 seconds
            setInterval(() => {
                this.updateTimestamps();
            }, 30000);
        }

        updateTimestamps() {
            const now = new Date().toLocaleTimeString();
            document.getElementById('accessLogTime').textContent = now;
            document.getElementById('errorLogTime').textContent = now;
        }
    }

    // Global functions
    function refreshLogs() {
        const refreshBtn = document.querySelector('.refresh-btn');
        const originalText = refreshBtn.innerHTML;

        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Refreshing...';
        refreshBtn.disabled = true;

        // Simulate refresh (replace with actual refresh logic)
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    function trackDownload(logType) {
        // Track download event
        if (typeof gtag !== 'undefined') {
            gtag('event', 'download', {
                'event_category': 'logs',
                'event_label': `${logType}_log`
            });
        }

        // Show download notification
        showNotification(`${logType.charAt(0).toUpperCase() + logType.slice(1)} log download started!`, 'success');
    }

    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            <i class="fas fa-download me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        window.logManager = new LogManager();

        // Auto-scroll to bottom of log viewers
        document.querySelectorAll('.log-viewer').forEach(viewer => {
            viewer.scrollTop = viewer.scrollHeight;
        });
    });
</script>
{% endblock %}
