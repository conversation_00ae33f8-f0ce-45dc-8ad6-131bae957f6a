{% extends "base.html" %}

{% block title %}Unauthorized Access{% endblock %}

{% block page_title %}Access Denied{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Unauthorized</li>
{% endblock %}

{% block extra_css %}
<!-- WebPage Schema for Unauthorized -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Unauthorized Access | Librainian",
    "description": "Access denied. You need proper permissions to view this content. Please authenticate yourself to continue.",
    "url": "https://librainian.com/unauthorized/",
    "isPartOf": {
        "@type": "WebSite",
        "name": "Librainian",
        "url": "https://librainian.com/"
    },
    "publisher": {
        "@type": "Organization",
        "name": "Librainian",
        "logo": {
            "@type": "ImageObject",
            "url": "https://librainian.com/static/img/logo_trans_name.png",
            "width": 300,
            "height": 100
        },
        "url": "https://librainian.com/"
    },
    "mainEntity": {
        "@type": "Thing",
        "name": "401 Unauthorized Error",
        "description": "This page indicates that authentication is required to access the requested resource."
    }
}
</script>

<!-- BreadcrumbList Schema -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
        {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": "https://librainian.com/"
        },
        {
            "@type": "ListItem",
            "position": 2,
            "name": "Unauthorized Access",
            "item": "https://librainian.com/unauthorized/"
        }
    ]
}
</script>
<style>
    /* Unauthorized Page Glass Theme */
    .error-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: calc(100vh - var(--topbar-height));
        padding: 2rem;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .error-content::before {
        content: '';
        position: fixed;
        top: var(--topbar-height);
        left: 0;
        width: 100%;
        height: calc(100% - var(--topbar-height));
        background:
            radial-gradient(circle at 20% 80%, rgba(239, 68, 68, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(239, 68, 68, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    .error-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset,
            0 2px 4px rgba(255, 255, 255, 0.1) inset;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        text-align: center;
        padding: 3rem 2rem;
        max-width: 500px;
        width: 100%;
    }

    .error-card:hover {
        transform: translateY(-5px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset,
            0 2px 4px rgba(255, 255, 255, 0.15) inset;
    }

    .error-icon {
        width: 120px;
        height: 120px;
        margin: 0 auto 2rem auto;
        background: rgba(239, 68, 68, 0.2);
        border: 2px solid rgba(239, 68, 68, 0.4);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        color: #ef4444;
        animation: pulse 2s infinite;
        position: relative;
    }

    .error-icon::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        border: 2px solid rgba(239, 68, 68, 0.3);
        border-radius: 50%;
        animation: ripple 2s infinite;
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
    }

    @keyframes ripple {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        100% {
            transform: scale(1.3);
            opacity: 0;
        }
    }

    .error-title {
        font-family: 'Plus Jakarta Sans', sans-serif;
        font-weight: 700;
        font-size: 2.5rem;
        color: white;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .error-code {
        font-family: 'Plus Jakarta Sans', sans-serif;
        font-weight: 800;
        font-size: 1.25rem;
        color: #ef4444;
        margin-bottom: 1.5rem;
        text-transform: uppercase;
        letter-spacing: 2px;
    }

    .error-message {
        font-size: 1.125rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 1rem;
        line-height: 1.6;
    }

    .error-description {
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 2.5rem;
        line-height: 1.5;
    }

    .error-actions {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .action-btn {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1rem 2rem;
        color: white;
        text-decoration: none;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        justify-content: center;
        min-width: 200px;
    }

    .action-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        text-decoration: none;
    }

    .action-btn.primary {
        background: rgba(99, 102, 241, 0.3);
        border-color: rgba(99, 102, 241, 0.5);
    }

    .action-btn.primary:hover {
        background: rgba(99, 102, 241, 0.4);
    }

    .action-btn.secondary {
        background: rgba(107, 114, 128, 0.3);
        border-color: rgba(107, 114, 128, 0.5);
    }

    .action-btn.secondary:hover {
        background: rgba(107, 114, 128, 0.4);
    }

    /* Help Section */
    .help-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 1.5rem;
        margin-top: 2rem;
        text-align: left;
    }

    .help-title {
        font-weight: 600;
        color: white;
        margin-bottom: 1rem;
        font-size: 1.125rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .help-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .help-list li {
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
    }

    .help-list li::before {
        content: '•';
        color: #60a5fa;
        font-weight: bold;
        font-size: 1.2rem;
    }

    /* Mobile Responsive */
    @media (max-width: 991.98px) {
        .error-content {
            padding: 1rem;
            padding-bottom: calc(1rem + var(--bottom-menu-height));
        }
    }

    @media (max-width: 767.98px) {
        .error-content {
            padding: 0.75rem;
            padding-bottom: calc(0.75rem + var(--bottom-menu-height));
        }

        .error-card {
            padding: 2rem 1.5rem;
        }

        .error-title {
            font-size: 2rem;
        }

        .error-icon {
            width: 100px;
            height: 100px;
            font-size: 2.5rem;
        }

        .action-btn {
            width: 100%;
            max-width: 300px;
        }

        .help-section {
            text-align: center;
        }
    }

    /* Disable zoom and text selection */
    body {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        touch-action: manipulation;
        -webkit-touch-callout: none;
        -webkit-tap-highlight-color: transparent;
    }

    /* Animation for page load */
    .error-card {
        animation: slideInUp 0.8s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Floating particles animation */
    .error-content::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
            radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
            radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.4), transparent),
            radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.3), transparent),
            radial-gradient(2px 2px at 160px 30px, rgba(255, 255, 255, 0.2), transparent);
        background-repeat: repeat;
        background-size: 200px 100px;
        animation: float 20s linear infinite;
        pointer-events: none;
        opacity: 0.6;
    }

    @keyframes float {
        0% {
            transform: translateY(0px);
        }
        100% {
            transform: translateY(-100px);
        }
    }

    /* Error code styling */
    .error-code {
        position: relative;
    }

    .error-code::before {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 50px;
        height: 2px;
        background: linear-gradient(90deg, transparent, #ef4444, transparent);
    }

    /* Interactive elements */
    .action-btn {
        position: relative;
        overflow: hidden;
    }

    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .action-btn:hover::before {
        left: 100%;
    }
</style>
{% endblock %}

{% block content %}
<div class="error-content">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12">
                <!-- Main Error Card -->
                <div class="error-card">
                    <!-- Error Icon -->
                    <div class="error-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>

                    <!-- Error Code -->
                    <div class="error-code">401 - Unauthorized</div>

                    <!-- Error Title -->
                    <h1 class="error-title">Access Denied</h1>

                    <!-- Error Message -->
                    <p class="error-message">
                        Sorry, you are not authorized to access this page.
                    </p>

                    <!-- Error Description -->
                    <p class="error-description">
                        You need to be logged in with proper permissions to view this content.
                        Please authenticate yourself to continue.
                    </p>

                    <!-- Action Buttons -->
                    <div class="error-actions">
                        <a href="/{% if role %}{{ role }}{% else %}librarian{% endif %}/login/" class="action-btn primary">
                            <i class="fas fa-sign-in-alt"></i>
                            Go to Login
                        </a>

                        <a href="/" class="action-btn secondary">
                            <i class="fas fa-home"></i>
                            Back to Home
                        </a>
                    </div>

                    <!-- Help Section -->
                    <div class="help-section">
                        <h3 class="help-title">
                            <i class="fas fa-question-circle"></i>
                            Need Help?
                        </h3>
                        <ul class="help-list">
                            <li>Make sure you are logged in with the correct account</li>
                            <li>Check if you have the necessary permissions for this page</li>
                            <li>Try refreshing the page or clearing your browser cache</li>
                            <li>Contact your administrator if the problem persists</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Unauthorized Page Functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize page animations
        initializeAnimations();

        // Add interactive effects
        initializeInteractiveEffects();

        // Removed page load error message

        // Auto-redirect after 30 seconds (optional)
        setTimeout(() => {
            if (confirm('Would you like to be redirected to the login page?')) {
                window.location.href = '/{% if role %}{{ role }}{% else %}librarian{% endif %}/login/';
            }
        }, 30000);
    });

    function initializeAnimations() {
        // Add entrance animation to error card
        const errorCard = document.querySelector('.error-card');
        if (errorCard) {
            errorCard.style.opacity = '0';
            errorCard.style.transform = 'translateY(50px)';

            setTimeout(() => {
                errorCard.style.transition = 'all 0.8s ease-out';
                errorCard.style.opacity = '1';
                errorCard.style.transform = 'translateY(0)';
            }, 100);
        }

        // Staggered animation for help items
        const helpItems = document.querySelectorAll('.help-list li');
        helpItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(-20px)';

            setTimeout(() => {
                item.style.transition = 'all 0.5s ease-out';
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            }, 1000 + (index * 200));
        });
    }

    function initializeInteractiveEffects() {
        // Add click tracking for analytics
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                const action = this.textContent.trim();
                console.log(`User clicked: ${action} from unauthorized page`);

                // Add loading state
                this.style.opacity = '0.7';
                this.style.pointerEvents = 'none';

                // You can add analytics tracking here
                // Example: gtag('event', 'click', { 'event_category': 'unauthorized', 'event_label': action });
            });
        });

        // Add hover effect for error icon
        const errorIcon = document.querySelector('.error-icon');
        if (errorIcon) {
            errorIcon.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.1) rotate(5deg)';
            });

            errorIcon.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1) rotate(0deg)';
            });
        }

        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                const primaryBtn = document.querySelector('.action-btn.primary');
                if (primaryBtn && document.activeElement === primaryBtn) {
                    primaryBtn.click();
                }
            }

            if (e.key === 'Escape') {
                const homeBtn = document.querySelector('.action-btn.secondary');
                if (homeBtn) {
                    homeBtn.click();
                }
            }
        });
    }

    // Add some interactive particles on mouse move
    document.addEventListener('mousemove', function(e) {
        if (Math.random() > 0.95) { // Only occasionally create particles
            createParticle(e.clientX, e.clientY);
        }
    });

    function createParticle(x, y) {
        const particle = document.createElement('div');
        particle.style.position = 'fixed';
        particle.style.left = x + 'px';
        particle.style.top = y + 'px';
        particle.style.width = '4px';
        particle.style.height = '4px';
        particle.style.background = 'rgba(255, 255, 255, 0.6)';
        particle.style.borderRadius = '50%';
        particle.style.pointerEvents = 'none';
        particle.style.zIndex = '1000';
        particle.style.transition = 'all 1s ease-out';

        document.body.appendChild(particle);

        // Animate particle
        setTimeout(() => {
            particle.style.transform = 'translateY(-50px) scale(0)';
            particle.style.opacity = '0';
        }, 10);

        // Remove particle
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 1000);
    }

    // Prevent right-click context menu on error page
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });

    // Add focus management for accessibility
    document.addEventListener('DOMContentLoaded', function() {
        const primaryBtn = document.querySelector('.action-btn.primary');
        if (primaryBtn) {
            primaryBtn.focus();
        }
    });
</script>
{% endblock %}