{% extends "email_base.html" %}

{% block email_title %}New Feedback Received - Librainian{% endblock %}

{% block email_subject %}New Feedback Received{% endblock %}

{% block email_description %}Notification about new feedback submitted through the Librainian platform.{% endblock %}

{% block preview_text %}New feedback from {{ feedback.name }} about "{{ feedback.subject }}". Rating: {% for i in feedback.get_rating_display_stars %}{{ i }}{% endfor %}{% endblock %}

{% block header_icon %}📝{% endblock %}

{% block email_header_title %}New Feedback Received{% endblock %}

{% block email_header_subtitle %}A user has submitted feedback through the platform{% endblock %}

{% block email_styles %}
<style>
    .feedback-notification {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border-radius: 16px;
        padding: 25px;
        margin: 25px 0;
        border: 2px solid #f59e0b;
        position: relative;
        overflow: hidden;
    }

    .feedback-notification::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(245, 158, 11, 0.1), transparent);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(30deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(30deg); }
    }

    .feedback-icon {
        font-size: 48px;
        text-align: center;
        margin-bottom: 20px;
        position: relative;
        z-index: 1;
    }

    .notification-title {
        font-size: 20px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 20px 0;
        text-align: center;
        position: relative;
        z-index: 1;
    }

    .feedback-details {
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 12px;
        padding: 20px;
        margin: 20px 0;
        position: relative;
        z-index: 1;
    }

    .detail-item {
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid rgba(245, 158, 11, 0.2);
    }

    .detail-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .detail-label {
        font-weight: 600;
        color: #f59e0b;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 5px;
    }

    .detail-value {
        color: #2c3e50;
        font-size: 16px;
        line-height: 1.5;
        margin: 0;
    }

    .rating-stars {
        color: #fbbf24;
        font-size: 24px;
        letter-spacing: 2px;
    }

    .message-content {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #f59e0b;
        white-space: pre-line;
        font-style: italic;
    }

    .meta-section {
        background-color: #f3f4f6;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
        border-left: 4px solid #6b7280;
    }

    .meta-title {
        font-size: 16px;
        font-weight: 600;
        color: #6b7280;
        margin: 0 0 10px 0;
        display: flex;
        align-items: center;
    }

    .meta-title::before {
        content: "ℹ️";
        margin-right: 8px;
        font-size: 18px;
    }

    .action-required {
        background-color: #dbeafe;
        border: 1px solid #3b82f6;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
        border-left: 4px solid #3b82f6;
    }

    .action-title {
        font-size: 16px;
        font-weight: 600;
        color: #1e40af;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
    }

    .action-title::before {
        content: "👀";
        margin-right: 8px;
        font-size: 18px;
    }

    .action-text {
        font-size: 14px;
        color: #1e40af;
        margin: 0;
        line-height: 1.5;
    }

    @media only screen and (max-width: 600px) {
        .feedback-notification {
            padding: 20px !important;
            margin: 20px 0 !important;
        }

        .feedback-details {
            padding: 15px !important;
            margin: 15px 0 !important;
        }

        .rating-stars {
            font-size: 20px !important;
        }
    }
</style>
{% endblock %}

{% block email_content %}
<h2 class="greeting">Hello Admin!</h2>
<p class="message">
    A new feedback has been submitted through the registration success page. Please review the details below and take appropriate action if needed.
</p>

<!-- Feedback Notification Section -->
<div class="feedback-notification">
    <div class="feedback-icon">📝</div>
    <h3 class="notification-title">New Feedback Details</h3>

    <div class="feedback-details">
        <div class="detail-item">
            <div class="detail-label">Name</div>
            <div class="detail-value">{{ feedback.name }}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Email Address</div>
            <div class="detail-value">{{ feedback.email }}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Phone Number</div>
            <div class="detail-value">{{ feedback.phone }}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Subject</div>
            <div class="detail-value">{{ feedback.subject }}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Rating</div>
            <div class="detail-value rating-stars">
                {% for i in feedback.get_rating_display_stars %}{{ i }}{% endfor %}
            </div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Message</div>
            <div class="detail-value message-content">{{ feedback.message }}</div>
        </div>

        {% if feedback.library %}
        <div class="detail-item">
            <div class="detail-label">Associated Library</div>
            <div class="detail-value">{{ feedback.library.library_name }}</div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Meta Information -->
<div class="meta-section">
    <h4 class="meta-title">Submission Details</h4>
    <div class="detail-item">
        <div class="detail-label">Submitted On</div>
        <div class="detail-value">{{ feedback.created_at|date:"F j, Y, g:i a" }}</div>
    </div>
    <div class="detail-item">
        <div class="detail-label">IP Address</div>
        <div class="detail-value">{{ feedback.ip_address }}</div>
    </div>
</div>

<!-- Action Required -->
<div class="action-required">
    <h4 class="action-title">Review Required</h4>
    <p class="action-text">
        Please review this feedback and consider responding to the user if necessary. User feedback helps improve our services and user experience.
    </p>
</div>

<p class="message">
    This is an automated notification. Please log into the admin panel to view more details and manage feedback responses.
</p>
{% endblock %}
