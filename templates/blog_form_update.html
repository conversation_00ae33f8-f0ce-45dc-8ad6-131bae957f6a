{% extends "base.html" %}

{% block title %}{% if blog %}Edit Blog - {{ blog.title }}{% else %}Create New Blog{% endif %} - Librainian{% endblock %}

{% block page_title %}{% if blog %}Edit Blog{% else %}Create Blog{% endif %}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="/blogs/">Blogs</a></li>
<li class="breadcrumb-item active" aria-current="page">{% if blog %}Edit Blog{% else %}Create Blog{% endif %}</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced Blog Form Glass Design */
    .blog-form-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius-lg);
        box-shadow: 
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset,
            0 2px 4px rgba(255, 255, 255, 0.1) inset;
        transition: var(--transition-slow);
        position: relative;
        overflow: hidden;
    }

    .blog-form-card:hover {
        transform: translateY(-3px);
        box-shadow: 
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset,
            0 2px 4px rgba(255, 255, 255, 0.15) inset;
    }

    .blog-form-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            transparent 100%);
        pointer-events: none;
        opacity: 0.8;
    }

    .form-group-modern {
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 2;
    }

    .form-label-modern {
        display: block;
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    .form-control-modern {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius);
        font-size: 0.875rem;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        color: var(--gray-900);
        resize: vertical;
    }

    .form-control-modern:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
        background: white;
    }

    .form-control-modern::placeholder {
        color: var(--gray-400);
    }

    .form-row-modern {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .file-input-modern {
        position: relative;
        display: inline-block;
        width: 100%;
    }

    .file-input-modern input[type="file"] {
        position: absolute;
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }

    .file-input-label {
        display: block;
        padding: 0.875rem 1rem;
        border: 2px dashed rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        text-align: center;
        cursor: pointer;
        transition: var(--transition);
        color: var(--gray-600);
        font-size: 0.875rem;
    }

    .file-input-label:hover {
        border-color: var(--primary);
        background: rgba(255, 255, 255, 1);
        color: var(--primary);
    }

    .file-input-label i {
        margin-right: 0.5rem;
        color: var(--gray-400);
    }

    .submit-btn {
        width: 100%;
        padding: 1rem 2rem;
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        border: none;
        border-radius: var(--border-radius);
        color: white;
        font-weight: 600;
        font-size: 0.875rem;
        cursor: pointer;
        transition: var(--transition-slow);
        box-shadow: var(--shadow-md);
        position: relative;
        overflow: hidden;
        z-index: 2;
        margin-bottom: 1rem;
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
        color: white;
    }

    .submit-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .submit-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .submit-btn:hover::before {
        left: 100%;
    }

    .back-btn {
        width: 100%;
        padding: 1rem 2rem;
        background: linear-gradient(135deg, var(--gray-600) 0%, var(--gray-700) 100%);
        border: none;
        border-radius: var(--border-radius);
        color: white;
        font-weight: 600;
        font-size: 0.875rem;
        cursor: pointer;
        transition: var(--transition-slow);
        box-shadow: var(--shadow-md);
        text-decoration: none;
        display: inline-block;
        text-align: center;
        position: relative;
        overflow: hidden;
        z-index: 2;
    }

    .back-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
        color: white;
        text-decoration: none;
    }

    .back-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .back-btn:hover::before {
        left: 100%;
    }

    .char-counter {
        font-size: 0.75rem;
        color: var(--gray-500);
        text-align: right;
        margin-top: 0.25rem;
    }

    .char-counter.warning {
        color: var(--warning);
    }

    .char-counter.danger {
        color: var(--danger);
    }

    .required-field::after {
        content: ' *';
        color: var(--danger);
        font-weight: bold;
    }

    .form-help-text {
        font-size: 0.75rem;
        color: var(--gray-500);
        margin-top: 0.25rem;
    }

    @media (max-width: 768px) {
        .form-row-modern {
            grid-template-columns: 1fr;
            gap: 0;
        }
        
        .blog-form-card {
            margin: 1rem;
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-content fade-in">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="modern-card">
            <div class="modern-card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="page-title mb-2">
                            <i class="fas fa-{% if blog %}edit{% else %}plus{% endif %} me-2 text-primary"></i>
                            {% if blog %}Edit Blog Post{% else %}Create New Blog Post{% endif %}
                        </h2>
                        <p class="page-subtitle text-muted mb-0">
                            {% if blog %}Update your blog content and settings{% else %}Write and publish a new blog article{% endif %}
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="page-actions">
                            <span class="badge bg-{% if blog %}warning{% else %}success{% endif %}-subtle text-{% if blog %}warning{% else %}success{% endif %} px-3 py-2">
                                <i class="fas fa-{% if blog %}edit{% else %}plus{% endif %} me-1"></i>
                                {% if blog %}Edit Mode{% else %}Create Mode{% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show mb-4" role="alert">
                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' or message.tags == 'danger' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-circle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Blog Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="blog-form-card">
                <div class="modern-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-blog me-2"></i>
                        Blog Information
                    </h5>
                    <p class="card-subtitle mb-0">Fill in the details for your blog post</p>
                </div>
                <div class="modern-card-body">
                    <form method="post" enctype="multipart/form-data" id="blogForm">
                        {% csrf_token %}

                        <!-- Title -->
                        <div class="form-group-modern">
                            <label for="title" class="form-label-modern required-field">
                                <i class="fas fa-heading me-1"></i>
                                Blog Title
                            </label>
                            <input type="text" 
                                   id="title" 
                                   name="title" 
                                   class="form-control-modern" 
                                   placeholder="Enter blog title (50-60 characters recommended)"
                                   value="{% if blog %}{{ blog.title }}{% endif %}"
                                   maxlength="100"
                                   required>
                            <div class="char-counter" id="titleCounter">0/100 characters</div>
                            <div class="form-help-text">Keep it concise and engaging for better SEO</div>
                        </div>

                        <!-- Category and Keywords -->
                        <div class="form-row-modern">
                            <div class="form-group-modern">
                                <label for="category" class="form-label-modern required-field">
                                    <i class="fas fa-tags me-1"></i>
                                    Category
                                </label>
                                <input type="text" 
                                       id="category" 
                                       name="category" 
                                       class="form-control-modern" 
                                       placeholder="Enter category"
                                       value="{% if blog %}{{ blog.category }}{% endif %}"
                                       required>
                            </div>
                            <div class="form-group-modern">
                                <label for="keywords" class="form-label-modern">
                                    <i class="fas fa-key me-1"></i>
                                    Keywords
                                </label>
                                <input type="text" 
                                       id="keywords" 
                                       name="keywords" 
                                       class="form-control-modern" 
                                       placeholder="Enter keywords (comma separated)"
                                       value="{% if blog %}{{ blog.keyword }}{% endif %}">
                                <div class="form-help-text">10-15 relevant keywords for SEO</div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="form-group-modern">
                            <label for="description" class="form-label-modern required-field">
                                <i class="fas fa-info-circle me-1"></i>
                                Description
                            </label>
                            <textarea id="description"
                                      name="description"
                                      class="form-control-modern"
                                      placeholder="Enter blog description (150-160 characters recommended)"
                                      rows="3"
                                      maxlength="200"
                                      required>{% if blog %}{{ blog.description }}{% endif %}</textarea>
                            <div class="char-counter" id="descriptionCounter">0/200 characters</div>
                            <div class="form-help-text">This will be used as meta description for SEO</div>
                        </div>

                        <!-- Content -->
                        <div class="form-group-modern">
                            <label for="content" class="form-label-modern required-field">
                                <i class="fas fa-file-alt me-1"></i>
                                Blog Content
                            </label>
                            <textarea id="content"
                                      name="content"
                                      class="form-control-modern"
                                      placeholder="Write your blog content here..."
                                      rows="12"
                                      required>{% if blog %}{{ blog.content }}{% endif %}</textarea>
                            <div class="char-counter" id="contentCounter">0 characters</div>
                            <div class="form-help-text">You can use HTML tags for formatting</div>
                        </div>

                        <!-- Blog Image -->
                        <div class="form-group-modern">
                            <label for="image" class="form-label-modern">
                                <i class="fas fa-image me-1"></i>
                                Featured Image
                            </label>
                            <div class="file-input-modern">
                                <input type="file"
                                       id="image"
                                       name="image"
                                       accept="image/*">
                                <label for="image" class="file-input-label">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    Choose image file or drag and drop
                                </label>
                            </div>
                            <div class="form-help-text">Recommended size: 1200x630px for optimal social media sharing</div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-group-modern">
                            <button type="submit" class="submit-btn" id="submitBtn">
                                <i class="fas fa-{% if blog %}save{% else %}plus{% endif %} me-2"></i>
                                {% if blog %}Update Blog Post{% else %}Create Blog Post{% endif %}
                            </button>
                            <a href="/blogs/" class="back-btn">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Blog List
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('blogForm');
        const submitBtn = document.getElementById('submitBtn');
        const fileInput = document.getElementById('image');
        const fileLabel = document.querySelector('.file-input-label');

        // Character counters
        const titleInput = document.getElementById('title');
        const descriptionInput = document.getElementById('description');
        const contentInput = document.getElementById('content');

        const titleCounter = document.getElementById('titleCounter');
        const descriptionCounter = document.getElementById('descriptionCounter');
        const contentCounter = document.getElementById('contentCounter');

        // Add staggered animation delays
        const cards = document.querySelectorAll('.modern-card, .blog-form-card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${(index + 1) * 0.1}s`;
            card.classList.add('slide-up');
        });

        // Character counter functions
        function updateCounter(input, counter, maxLength) {
            const currentLength = input.value.length;
            counter.textContent = `${currentLength}/${maxLength || 'unlimited'} characters`;

            if (maxLength) {
                if (currentLength > maxLength * 0.9) {
                    counter.className = 'char-counter danger';
                } else if (currentLength > maxLength * 0.8) {
                    counter.className = 'char-counter warning';
                } else {
                    counter.className = 'char-counter';
                }
            }
        }

        // Initialize counters
        updateCounter(titleInput, titleCounter, 100);
        updateCounter(descriptionInput, descriptionCounter, 200);
        updateCounter(contentInput, contentCounter);

        // Add event listeners for character counting
        titleInput.addEventListener('input', () => updateCounter(titleInput, titleCounter, 100));
        descriptionInput.addEventListener('input', () => updateCounter(descriptionInput, descriptionCounter, 200));
        contentInput.addEventListener('input', () => updateCounter(contentInput, contentCounter));

        // File input handling
        fileInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const fileName = this.files[0].name;
                const fileSize = (this.files[0].size / 1024 / 1024).toFixed(2);

                if (this.files[0].size > 10 * 1024 * 1024) { // 10MB limit
                    alert('File size must be less than 10MB');
                    this.value = '';
                    fileLabel.innerHTML = '<i class="fas fa-cloud-upload-alt"></i> Choose image file or drag and drop';
                    return;
                }

                fileLabel.innerHTML = `<i class="fas fa-check-circle text-success"></i> ${fileName} (${fileSize}MB)`;
                fileLabel.style.borderColor = 'var(--success)';
                fileLabel.style.color = 'var(--success)';
            } else {
                fileLabel.innerHTML = '<i class="fas fa-cloud-upload-alt"></i> Choose image file or drag and drop';
                fileLabel.style.borderColor = '';
                fileLabel.style.color = '';
            }
        });

        // Form validation
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.style.borderColor = 'var(--danger)';
                    field.focus();
                } else {
                    field.style.borderColor = '';
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields.');
                return;
            }

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% if blog %}Updating...{% else %}Creating...{% endif %}';
        });

        // Show welcome notification
        setTimeout(() => {
            if (window.modernDashboard) {
                window.modernDashboard.showToast(
                    'Blog Form Ready!',
                    '{% if blog %}Edit your blog content and save changes.{% else %}Create your new blog post.{% endif %}',
                    'info'
                );
            }
        }, 1000);
    });
</script>
{% endblock %}
