{% load static %}

{% if navbar_section == "head" %}
<!-- ========== HEAD SECTION (Include in <head>) ========== -->

<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5L8WZWCC');</script>
<!-- End Google Tag Manager -->

<!-- Bootstrap 5.3.3 -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">

{% else %}
<!-- ========== BODY SECTION (Include in <body>) ========== -->
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5L8WZWCC"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

<!-- Modern Glassmorphism Navigation -->
<nav class="navbar navbar-expand-lg navbar-glass" id="mainNav">
    <div class="container">
        <a class="navbar-brand" href="/">
            <img id="navbarLogo" src="{% static 'img/logo_trans_name.png' %}" alt="Librainian - Library Management System" loading="eager">
        </a>

        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav w-100">
                <li class="nav-item">
                    <a class="nav-link" href="/">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/blogs/p/">Blog</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/services/">Services</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/librarian/library-list/">Libraries</a>
                </li>

                <!-- Dark Mode Toggle -->
                <li class="nav-item navbar-right-item">
                    <button id="darkModeToggle" class="theme-toggle-btn" title="Toggle Dark/Light Mode">
                        <i class="fas fa-sun theme-icon-light"></i>
                        <i class="fas fa-moon theme-icon-dark"></i>
                    </button>
                </li>

                <li class="nav-item dropdown navbar-right-item">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i> Login
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/librarian/login/">Librarian Login</a></li>
                        <li><a class="dropdown-item" href="/sublibrarian/login/">Sub-Librarian Login</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/librarian/signup/">Create Account</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<style>
/* Modern Navigation Styles */
.navbar-glass {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1030;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(135, 134, 134, 0.2);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 1rem 0;
}

.navbar-glass.scrolled {
    background: rgba(255, 255, 255, 0.95);
    padding: 0.75rem 0;
}

.navbar {
    padding: 0.5rem 0;
    min-height: 70px;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.navbar-brand {
    display: flex;
    align-items: center;
    padding: 0;
    margin: 0;
    height: 50px;
}

.navbar-brand img {
    height: 40px;
    width: auto;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    object-fit: contain;
}

.navbar-collapse {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-grow: 1;
}

.navbar-nav {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
    width: 100%;
}

.navbar-nav .nav-item {
    margin: 0 0.25rem;
}

.navbar-right-item {
    margin-left: auto !important;
}

.navbar-right-item:first-of-type {
    margin-left: auto !important;
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
    background: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    width: 1.5em;
    height: 1.5em;
}

.nav-link {
    font-weight: 600;
    color: #334155 !important;
    padding: 0.75rem 1.25rem !important;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.nav-link:hover {
    color: #6366f1 !important;
    background: rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

/* Dark Mode Toggle Button Styles */
.theme-toggle-btn {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    margin: 0 0.5rem;
}

.theme-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.theme-icon-light,
.theme-icon-dark {
    font-size: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: absolute;
}

.theme-icon-light {
    color: #f59e0b;
    opacity: 1;
}

.theme-icon-dark {
    color: #e5e7eb;
    opacity: 0;
}

body.dark-mode .theme-icon-light {
    opacity: 0;
}

body.dark-mode .theme-icon-dark {
    opacity: 1;
    color: #fbbf24;
}

/* Dropdown Styles */
.dropdown-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    margin-top: 0.5rem;
}

.dropdown-item {
    color: #334155;
    padding: 0.75rem 1.25rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-item:hover {
    background: rgba(99, 102, 241, 0.1);
    color: #6366f1;
}

/* Mobile responsive fixes */
@media (max-width: 991.98px) {
    .navbar-nav {
        flex-direction: column !important;
        align-items: flex-start !important;
        justify-content: flex-start !important;
        width: 100%;
    }

    .navbar-nav .nav-item {
        width: 100%;
        margin: 0.25rem 0;
    }

    .navbar-right-item {
        margin-left: 0 !important;
    }

    .navbar-right-item:first-of-type {
        margin-left: 0 !important;
    }

    .theme-toggle-btn {
        margin: 0.5rem 1.25rem;
        width: 35px;
        height: 35px;
    }

    .navbar-nav .nav-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
    }
}

/* Dark Mode Styles */
body.dark-mode .navbar-glass {
    background: rgba(17, 24, 39, 0.9);
}

body.dark-mode .navbar-glass.scrolled {
    background: rgba(17, 24, 39, 0.95);
}

body.dark-mode .nav-link {
    color: rgba(255, 255, 255, 0.8) !important;
}

body.dark-mode .nav-link:hover {
    color: white !important;
    background: rgba(99, 102, 241, 0.2);
}

body.dark-mode .dropdown-menu {
    background: rgba(17, 24, 39, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .dropdown-item {
    color: rgba(255, 255, 255, 0.9) !important;
}

body.dark-mode .dropdown-item:hover {
    background: rgba(99, 102, 241, 0.2);
    color: white !important;
}

body.dark-mode .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

body.dark-mode .navbar-brand img {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

body.dark-mode .theme-toggle-btn {
    border-color: rgba(255, 255, 255, 0.3);
}

body.dark-mode .theme-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}
</style>

<script>
// Dark mode functionality
function applyDarkModeStyles(isDarkMode) {
    const navbarLogo = document.getElementById('navbarLogo');

    if (isDarkMode) {
        // Switch to white logo
        if (navbarLogo) {
            navbarLogo.src = navbarLogo.src.replace('logo_trans_name.png', 'librainian-logo-white-trans.png');
        }
    } else {
        // Switch back to original logo
        if (navbarLogo) {
            navbarLogo.src = navbarLogo.src.replace('librainian-logo-white-trans.png', 'logo_trans_name.png');
        }
    }
}

// Initialize dark mode
document.addEventListener('DOMContentLoaded', function() {
    const darkModeToggle = document.getElementById('darkModeToggle');
    // Use same localStorage key as other pages for consistency
    const isDarkMode = localStorage.getItem('theme') === 'dark';

    // Set initial state
    if (isDarkMode) {
        document.body.classList.add('dark-mode');
        applyDarkModeStyles(true);
    }

    // Toggle functionality
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            if (document.body.classList.contains('dark-mode')) {
                document.body.classList.remove('dark-mode');
                localStorage.setItem('theme', 'light');
                applyDarkModeStyles(false);
            } else {
                document.body.classList.add('dark-mode');
                localStorage.setItem('theme', 'dark');
                applyDarkModeStyles(true);
            }
        });
    }
});

// Navbar scroll effect
window.addEventListener('scroll', function() {
    const navbar = document.getElementById('mainNav');
    if (navbar) {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }
});
</script>

{% endif %}
