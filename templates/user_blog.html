{% extends "base.html" %}

{% block title %}{{ blog.title }}{% endblock %}

{% block page_title %}Blog{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="/blogs/">Blogs</a></li>
<li class="breadcrumb-item active" aria-current="page">{{ blog.title|truncatechars:30 }}</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Blog Display Glass Theme */
    .blog-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: calc(100vh - var(--topbar-height));
        padding: 2rem;
        position: relative;
    }

    .blog-content::before {
        content: '';
        position: fixed;
        top: var(--topbar-height);
        left: 0;
        width: 100%;
        height: calc(100% - var(--topbar-height));
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    .blog-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset,
            0 2px 4px rgba(255, 255, 255, 0.1) inset;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .blog-card:hover {
        transform: translateY(-2px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset,
            0 2px 4px rgba(255, 255, 255, 0.15) inset;
    }

    .blog-header {
        padding: 2rem 2rem 1rem 2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        text-align: center;
    }

    .blog-title {
        font-family: 'Plus Jakarta Sans', sans-serif;
        font-weight: 700;
        font-size: 2.5rem;
        color: white;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        line-height: 1.2;
    }

    .blog-meta {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1.5rem;
        flex-wrap: wrap;
        margin-bottom: 1rem;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.875rem;
        font-weight: 500;
    }

    .meta-icon {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        font-size: 0.75rem;
    }

    .blog-body {
        padding: 2rem;
    }

    .blog-content-text {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1.125rem;
        line-height: 1.8;
        font-weight: 400;
        text-align: justify;
        margin-bottom: 2rem;
    }

    .blog-content-text h1,
    .blog-content-text h2,
    .blog-content-text h3,
    .blog-content-text h4,
    .blog-content-text h5,
    .blog-content-text h6 {
        color: white;
        font-weight: 600;
        margin: 2rem 0 1rem 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .blog-content-text p {
        margin-bottom: 1.5rem;
    }

    .blog-content-text a {
        color: #60a5fa;
        text-decoration: underline;
        font-weight: 500;
        transition: color 0.3s ease;
    }

    .blog-content-text a:hover {
        color: #93c5fd;
    }

    .blog-content-text ul,
    .blog-content-text ol {
        margin: 1.5rem 0;
        padding-left: 2rem;
    }

    .blog-content-text li {
        margin-bottom: 0.5rem;
    }

    .blog-content-text blockquote {
        background: rgba(255, 255, 255, 0.1);
        border-left: 4px solid rgba(99, 102, 241, 0.5);
        padding: 1rem 1.5rem;
        margin: 2rem 0;
        border-radius: 0 12px 12px 0;
        font-style: italic;
    }

    .blog-content-text code {
        background: rgba(0, 0, 0, 0.3);
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        font-size: 0.875rem;
    }

    .blog-content-text pre {
        background: rgba(0, 0, 0, 0.3);
        padding: 1rem;
        border-radius: 8px;
        overflow-x: auto;
        margin: 1.5rem 0;
    }

    .blog-content-text img {
        max-width: 100%;
        height: auto;
        border-radius: 12px;
        margin: 1.5rem 0;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    /* Related Articles Section */
    .related-articles {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .related-articles:hover {
        transform: translateY(-2px);
        box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
    }

    .related-title {
        font-family: 'Plus Jakarta Sans', sans-serif;
        font-weight: 700;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .related-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
    }

    .related-article-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: block;
    }

    .related-article-card:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
        text-decoration: none;
    }

    .related-article-title {
        color: white;
        font-weight: 600;
        font-size: 1rem;
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }

    .related-article-excerpt {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.875rem;
        line-height: 1.5;
    }

    /* Blog Actions */
    .blog-actions {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 2rem;
        flex-wrap: wrap;
    }

    .action-btn {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        color: white;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .action-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .action-btn.primary {
        background: rgba(99, 102, 241, 0.3);
        border-color: rgba(99, 102, 241, 0.5);
    }

    .action-btn.secondary {
        background: rgba(107, 114, 128, 0.3);
        border-color: rgba(107, 114, 128, 0.5);
    }

    /* Reading Progress Bar */
    .reading-progress {
        position: fixed;
        top: var(--topbar-height);
        left: 0;
        width: 0%;
        height: 3px;
        background: linear-gradient(90deg, #60a5fa, #a78bfa);
        z-index: 1000;
        transition: width 0.3s ease;
    }

    /* Mobile Responsive */
    @media (max-width: 991.98px) {
        .blog-content {
            padding: 1rem;
            padding-bottom: calc(1rem + var(--bottom-menu-height));
        }
    }

    @media (max-width: 767.98px) {
        .blog-content {
            padding: 0.75rem;
            padding-bottom: calc(0.75rem + var(--bottom-menu-height));
        }

        .blog-header,
        .blog-body {
            padding: 1.5rem;
        }

        .blog-title {
            font-size: 1.75rem;
        }

        .blog-meta {
            flex-direction: column;
            gap: 0.75rem;
        }

        .blog-content-text {
            font-size: 1rem;
        }

        .related-grid {
            grid-template-columns: 1fr;
        }

        .blog-actions {
            flex-direction: column;
            align-items: center;
        }

        .action-btn {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }
    }

    /* Disable zoom and text selection for UI elements */
    .blog-header,
    .blog-actions,
    .related-articles {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        touch-action: manipulation;
        -webkit-touch-callout: none;
        -webkit-tap-highlight-color: transparent;
    }

    /* Allow text selection for blog content */
    .blog-content-text {
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;
    }

    /* Animation for page load */
    .blog-card,
    .related-articles {
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}
{% block content %}
<!-- Reading Progress Bar -->
<div class="reading-progress" id="readingProgress"></div>

<div class="blog-content">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-10 col-xl-8">
                <!-- Main Blog Card -->
                <div class="blog-card">
                    <!-- Blog Header -->
                    <div class="blog-header">
                        <h1 class="blog-title">{{ blog.title }}</h1>

                        <!-- Blog Metadata -->
                        <div class="blog-meta">
                            {% if blog.created_at %}
                            <div class="meta-item">
                                <div class="meta-icon">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <span>{{ blog.created_at|date:"F d, Y" }}</span>
                            </div>
                            {% endif %}

                            {% if blog.author %}
                            <div class="meta-item">
                                <div class="meta-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                <span>{{ blog.author.get_full_name|default:blog.author.username }}</span>
                            </div>
                            {% endif %}

                            {% if blog.category %}
                            <div class="meta-item">
                                <div class="meta-icon">
                                    <i class="fas fa-tag"></i>
                                </div>
                                <span>{{ blog.category }}</span>
                            </div>
                            {% endif %}

                            <div class="meta-item">
                                <div class="meta-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <span id="readingTime">5 min read</span>
                            </div>
                        </div>
                    </div>

                    <!-- Blog Body -->
                    <div class="blog-body">
                        <div class="blog-content-text" id="blogContent">
                            {{ blog.content|safe }}
                        </div>

                        <!-- Blog Actions -->
                        <div class="blog-actions">
                            <a href="/blogs/" class="action-btn secondary">
                                <i class="fas fa-arrow-left"></i>
                                Back to Blogs
                            </a>

                            <button onclick="shareArticle()" class="action-btn primary">
                                <i class="fas fa-share-alt"></i>
                                Share Article
                            </button>

                            <button onclick="printArticle()" class="action-btn secondary">
                                <i class="fas fa-print"></i>
                                Print
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Related Articles Section -->
                {% if blog.related_articles %}
                <div class="related-articles">
                    <h2 class="related-title">Related Articles</h2>
                    <div class="related-grid">
                        {% for article in blog.related_articles %}
                        <a href="{{ article.get_absolute_url }}" class="related-article-card" title="{{ article.title }}">
                            <h3 class="related-article-title">{{ article.title }}</h3>
                            {% if article.excerpt %}
                            <p class="related-article-excerpt">{{ article.excerpt|truncatechars:120 }}</p>
                            {% endif %}
                        </a>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Blog Page Functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize reading progress
        initializeReadingProgress();

        // Calculate reading time
        calculateReadingTime();

        // Initialize page animations
        initializeAnimations();

        // Removed page load success message
    });

    function initializeReadingProgress() {
        const progressBar = document.getElementById('readingProgress');
        const blogContent = document.getElementById('blogContent');

        if (!progressBar || !blogContent) return;

        window.addEventListener('scroll', function() {
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight - windowHeight;
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollPercentage = (scrollTop / documentHeight) * 100;

            progressBar.style.width = Math.min(scrollPercentage, 100) + '%';
        });
    }

    function calculateReadingTime() {
        const content = document.getElementById('blogContent');
        const readingTimeElement = document.getElementById('readingTime');

        if (!content || !readingTimeElement) return;

        const text = content.textContent || content.innerText || '';
        const wordsPerMinute = 200; // Average reading speed
        const wordCount = text.trim().split(/\s+/).length;
        const readingTime = Math.ceil(wordCount / wordsPerMinute);

        readingTimeElement.textContent = `${readingTime} min read`;
    }

    function initializeAnimations() {
        // Add staggered animation to blog elements
        const blogElements = document.querySelectorAll('.blog-card, .related-articles');
        blogElements.forEach((element, index) => {
            element.style.animationDelay = `${index * 0.2}s`;
        });
    }

    function shareArticle() {
        if (navigator.share) {
            navigator.share({
                title: document.title,
                text: 'Check out this article',
                url: window.location.href
            }).then(() => {
                if (window.modernDashboard) {
                    window.modernDashboard.showToast(
                        'Shared!',
                        'Article shared successfully.',
                        'success'
                    );
                }
            }).catch((error) => {
                console.log('Error sharing:', error);
                fallbackShare();
            });
        } else {
            fallbackShare();
        }
    }

    function fallbackShare() {
        // Copy URL to clipboard as fallback
        navigator.clipboard.writeText(window.location.href).then(() => {
            if (window.modernDashboard) {
                window.modernDashboard.showToast(
                    'Link Copied!',
                    'Article link copied to clipboard.',
                    'success'
                );
            } else {
                alert('Article link copied to clipboard!');
            }
        }).catch(() => {
            // Final fallback - show URL in prompt
            prompt('Copy this link to share:', window.location.href);
        });
    }

    function printArticle() {
        // Create a print-friendly version
        const printWindow = window.open('', '_blank');
        const blogTitle = document.querySelector('.blog-title').textContent;
        const blogContent = document.getElementById('blogContent').innerHTML;

        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>${blogTitle}</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        line-height: 1.6;
                        max-width: 800px;
                        margin: 0 auto;
                        padding: 20px;
                        color: #333;
                    }
                    h1, h2, h3, h4, h5, h6 {
                        color: #2c3e50;
                        margin-top: 2rem;
                        margin-bottom: 1rem;
                    }
                    p {
                        margin-bottom: 1rem;
                    }
                    img {
                        max-width: 100%;
                        height: auto;
                    }
                    @media print {
                        body { margin: 0; }
                    }
                </style>
            </head>
            <body>
                <h1>${blogTitle}</h1>
                ${blogContent}
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.focus();

        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);

        if (window.modernDashboard) {
            window.modernDashboard.showToast(
                'Print Ready',
                'Print dialog opened.',
                'info'
            );
        }
    }

    // Smooth scrolling for anchor links within the article
    document.addEventListener('click', function(e) {
        if (e.target.tagName === 'A' && e.target.getAttribute('href').startsWith('#')) {
            e.preventDefault();
            const targetId = e.target.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }
    });
</script>
{% endblock %}