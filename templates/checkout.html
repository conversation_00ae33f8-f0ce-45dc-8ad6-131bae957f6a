<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Librainian Membership Checkout</title>
  

  
    <!-- Disable Right click -->

        
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
    

    <style>

        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
        }
    </style>
</head>
<body>


  <div id="modal" style="display: flex; justify-content: center; align-items: center; height: 100vh; background-color: rgba(0, 0, 0, 0.5);">
    <div style="background: white; padding: 20px; border-radius: 8px; width: 300px; text-align: center;">
      <img src="/static/img/figma_files/razorpay.JPG" alt="Razorpay Logo" style="width: 150px; margin-bottom: 20px;">
      <form action="{% url 'verify_payment' %}" method="post" id="razorpay-form">
        {% csrf_token %}
        <input type="hidden" name="plan_id" value="{{ plan.id }}">
        <input type="hidden" name="num_months" id="num-months-hidden" value="{{ num_months }}">
        <input type="hidden" name="razorpay_order_id" id="razorpay_order_id">
        <input type="hidden" name="razorpay_payment_id" id="razorpay_payment_id">
        <input type="hidden" name="razorpay_signature" id="razorpay_signature">
        <button id="pay-button" type="button" style="padding: 10px 20px; margin: 0rem 2rem 1rem 2rem; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
          Pay using Razorpay
        </button>
      </form>
    </div>
  </div>

  <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
  <script>
    document.getElementById('pay-button').onclick = function(e) {
      var options = {
        "key": "{{ razorpay_key }}",
        "amount": "{{ amount }}",
        "currency": "{{ currency }}",
        "name": "Librainian Membership",
        "description": "Membership Plan",
        "image": "/static/img/figma_files/logo 1.png",
        "order_id": "{{ order_id }}",
        "handler": function(response) {
          document.getElementById('razorpay_order_id').value = response.razorpay_order_id;
          document.getElementById('razorpay_payment_id').value = response.razorpay_payment_id;
          document.getElementById('razorpay_signature').value = response.razorpay_signature;
          document.getElementById('razorpay-form').submit();
        }
      };

      var rzp = new Razorpay(options);
      rzp.open();
      e.preventDefault();
    };
  </script>
</body>
</html>
