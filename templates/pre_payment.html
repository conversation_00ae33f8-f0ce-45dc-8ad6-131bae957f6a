<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Complete your Librainian membership checkout - Choose your plan and secure payment processing">
    <meta name="keywords" content="library membership, checkout, payment, subscription">
    <meta name="author" content="Librainian">

    <title>Membership Checkout - Librainian</title>

    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Import master CSS variables from code_temp.html -->
    {% include "code_temp.html" %}

    <style>
        :root {
            /* Pre-payment specific variable aliases for compatibility */
            --primary-color: var(--primary);
            --primary-dark: var(--primary-dark);
            --secondary-color: var(--secondary);
            --accent-color: var(--accent);
            --success-color: var(--success);
            --danger-color: var(--danger);
            --warning-color: var(--warning);
            --light-bg: var(--gray-50);
            --dark-text: var(--gray-800);
            --box-shadow: var(--shadow-lg);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Comfortaa', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--secondary-color) 0%, #a8d5d1 50%, #b8e0dc 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: var(--dark-text);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        /* Header Styles */
        .page-header {
            text-align: center;
            margin-bottom: 3rem;
            animation: fadeInDown 0.8s ease-out;
        }

        .page-header h1 {
            font-family: 'Comfortaa', sans-serif;
            font-weight: 700;
            font-size: 3.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .page-header p {
            font-size: 1.3rem;
            color: #6c757d;
            margin: 0;
        }

        /* Card Styles */
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            border: none;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.6s;
        }

        .card-header:hover::before {
            transform: translateX(100%);
        }

        .card-header h4 {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            margin: 0;
            font-size: 1.8rem;
            position: relative;
            z-index: 1;
        }

        .card-body {
            padding: 2.5rem;
        }

        .card-footer {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            border: none;
            padding: 1.5rem;
            text-align: center;
        }

        /* Plan Section */
        .plan-section {
            animation: fadeInLeft 0.8s ease-out;
        }

        .plan-title {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .plan-badge {
            background: var(--accent-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .features-list li {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid #f0f0f0;
            transition: var(--transition);
            font-size: 1.1rem;
        }

        .features-list li:hover {
            background: rgba(var(--accent-color), 0.05);
            padding-left: 1rem;
        }

        .features-list li:last-child {
            border-bottom: none;
        }

        .feature-icon {
            color: var(--success-color);
            font-size: 1.1rem;
            min-width: 20px;
        }

        .price-section {
            text-align: right;
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: var(--border-radius);
            margin-left: 1rem;
        }

        .price-display {
            font-family: 'Comfortaa', sans-serif;
            font-weight: 700;
            font-size: 3rem;
            color: var(--primary-color);
            margin: 0;
            line-height: 1;
        }

        .price-currency {
            font-size: 1.5rem;
            vertical-align: top;
        }

        .price-period {
            font-size: 1rem;
            color: #6c757d;
            font-weight: 400;
        }

        /* Form Controls */
        .form-label {
            font-weight: 600;
            color: var(--dark-text);
            margin-bottom: 0.75rem;
            font-size: 1.2rem;
        }

        .form-select,
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem 1.25rem;
            font-size: 1.1rem;
            transition: var(--transition);
            background: white;
            min-height: 50px;
        }

        .form-select:focus,
        .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 212, 170, 0.25);
            outline: none;
        }

        /* Buttons */
        .btn {
            border-radius: 12px;
            padding: 1rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: var(--transition);
            border: none;
            position: relative;
            overflow: hidden;
            font-size: 1.1rem;
            min-height: 50px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(41, 66, 130, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c82333 100%);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .page-header h1 {
                font-size: 2rem;
            }

            .plan-title {
                font-size: 1.5rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .price-section {
                text-align: left;
                margin-left: 0;
                margin-top: 1rem;
            }

            .price-display {
                font-size: 2rem;
            }

            .container {
                padding: 1rem;
            }

            .card-body {
                padding: 1.5rem;
            }
        }

        @media (max-width: 576px) {
            .page-header h1 {
                font-size: 1.75rem;
            }

            .plan-title {
                font-size: 1.25rem;
            }

            .price-display {
                font-size: 1.75rem;
            }

            .card-body {
                padding: 1rem;
            }
        }

        /* Coupon Section Styles */
        .coupon-section {
            margin-top: 2rem;
        }

        .coupon-trigger {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px dashed var(--primary-color);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            color: var(--primary-color);
            font-weight: 600;
        }

        .coupon-trigger:hover {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            transform: translateY(-2px);
        }

        .coupon-form-container {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }

        .coupon-form-container.show {
            max-height: 200px;
            margin-top: 1rem;
        }

        .coupon-form-card {
            background: white;
            border: 2px solid var(--accent-color);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 212, 170, 0.1);
        }

        /* Order Summary Styles */
        .order-summary {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 2rem;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.25rem;
            padding: 0.75rem 0;
            font-size: 1.1rem;
        }

        .summary-row:last-child {
            margin-bottom: 0;
        }

        .summary-label {
            color: #6c757d;
            font-weight: 500;
        }

        .summary-value {
            font-weight: 600;
            color: var(--dark-text);
            font-size: 1.2rem;
        }

        .total-row {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 0 -0.5rem;
            border: 3px solid var(--accent-color);
            box-shadow: 0 4px 15px rgba(0, 212, 170, 0.2);
        }

        .total-row .summary-label {
            font-size: 1.3rem;
        }

        .total-row .summary-value {
            font-size: 1.8rem;
        }

        .discount-row {
            background: rgba(40, 167, 69, 0.1);
            border-radius: 8px;
            padding: 0.5rem;
            margin: 0 -0.5rem;
        }

        /* Security and Payment Styles */
        .security-badges {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
        }

        .payment-methods i {
            font-size: 1.5rem;
            color: #6c757d;
            transition: var(--transition);
        }

        .payment-methods i:hover {
            color: var(--primary-color);
            transform: scale(1.1);
        }

        .tax-info {
            background: rgba(255, 193, 7, 0.1);
            border-radius: 8px;
            padding: 1rem;
        }

        /* Loading States */
        .btn.loading {
            position: relative;
            color: transparent;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid transparent;
            border-top-color: currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Dark Mode Loading States */
        body.dark-mode .btn.loading::after {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top-color: rgba(255, 255, 255, 0.9);
        }

        /* Animation Classes */
        .animate-fade-in {
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Success/Error Message Styles */
        .message-success {
            color: var(--success-color);
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid var(--success-color);
            border-radius: 8px;
            padding: 0.75rem;
            font-weight: 500;
        }

        .message-error {
            color: var(--danger-color);
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid var(--danger-color);
            border-radius: 8px;
            padding: 0.75rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="animate__animated animate__fadeInDown">
            <i class="fas fa-shopping-cart me-3 mt-5"></i>Membership Checkout
        </h1>
        <p class="animate__animated animate__fadeInUp animate__delay-1s">
            Complete your subscription and unlock premium features
        </p>
    </div>

    <div class="container">
        <div class="row g-4">
            <!-- Main Checkout Section -->
            <div class="col-lg-8">
                <div class="card plan-section animate__animated animate__fadeInLeft">
                    <div class="card-header">
                        <h4>
                            <i class="fas fa-crown me-2"></i>
                            Your Selected Plan
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Plan Details -->
                        <div class="d-flex flex-column flex-md-row align-items-start justify-content-between mb-4">
                            <div class="flex-grow-1">
                                <div class="plan-title">
                                    {{plan.name}}
                                    <span class="plan-badge">Popular</span>
                                </div>

                                <ul class="features-list">
                                    <li>
                                        <i class="fas fa-check-circle feature-icon"></i>
                                        <span>{{plan.description_line_01}}</span>
                                    </li>
                                    <li>
                                        <i class="fas fa-check-circle feature-icon"></i>
                                        <span>{{plan.description_line_02}}</span>
                                    </li>
                                    <li>
                                        <i class="fas fa-check-circle feature-icon"></i>
                                        <span>{{plan.description_line_03}}</span>
                                    </li>
                                </ul>
                            </div>

                            <div class="price-section">
                                <div class="price-display">
                                    <span class="price-currency">₹</span><span id="unit-price">{{plan.discount_price}}</span>
                                    <div class="price-period">per month</div>
                                </div>
                                <button class="btn btn-danger btn-sm mt-3">
                                    <a href="/membership/plans/" class="text-white text-decoration-none">
                                        <i class="fas fa-times me-1"></i>Remove
                                    </a>
                                </button>
                            </div>
                        </div>
                        <!-- Duration Selection -->
                        <div class="mb-4">
                            <label for="num-months" class="form-label">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Select Subscription Duration
                            </label>
                            <select id="num-months" class="form-select" onchange="updateTotal()" name="num_months">
                                <option value="1" {% if num_months == 1 %}selected{% endif %}>1 Month</option>
                                <option value="2" {% if num_months == 2 %}selected{% endif %}>2 Months</option>
                                <option value="3" {% if num_months == 3 %}selected{% endif %}>3 Months - Save 5%</option>
                                <option value="4" {% if num_months == 4 %}selected{% endif %}>4 Months</option>
                                <option value="5" {% if num_months == 5 %}selected{% endif %}>5 Months</option>
                                <option value="6" {% if num_months == 6 %}selected{% endif %}>6 Months - Save 10%</option>
                                <option value="7" {% if num_months == 7 %}selected{% endif %}>7 Months</option>
                                <option value="8" {% if num_months == 8 %}selected{% endif %}>8 Months</option>
                                <option value="9" {% if num_months == 9 %}selected{% endif %}>9 Months</option>
                                <option value="10" {% if num_months == 10 %}selected{% endif %}>10 Months</option>
                                <option value="11" {% if num_months == 11 %}selected{% endif %}>11 Months</option>
                                <option value="12" {% if num_months == 12 %}selected{% endif %}>12 Months - Save 20%</option>
                            </select>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Longer subscriptions offer better savings
                            </div>
                        </div>

                        <!-- Coupon Section -->
                        <div class="coupon-section">
                            <div id="showCouponForm" class="coupon-trigger" role="button" tabindex="0">
                                <i class="fas fa-tag me-2"></i>
                                <span>Have a coupon code?</span>
                                <i class="fas fa-chevron-down ms-auto"></i>
                            </div>

                            <div id="coupon-form-container" class="coupon-form-container">
                                <div class="coupon-form-card">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0 fw-bold">
                                            <i class="fas fa-percent me-2"></i>
                                            Apply Coupon Code
                                        </h6>
                                        <button type="button" class="btn-close" id="closeCouponForm" aria-label="Close"></button>
                                    </div>

                                    <form id="coupon-form" method="POST">
                                        {% csrf_token %}
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="coupon-code"
                                                   placeholder="Enter your coupon code"
                                                   style="border-top-right-radius: 0; border-bottom-right-radius: 0;">
                                            <button class="btn btn-primary" type="button" onclick="applyCoupon(event)">
                                                <i class="fas fa-check me-1"></i>Apply
                                            </button>
                                        </div>
                                        <div id="coupon-message" class="mt-2"></div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-shopping-bag me-2"></i>
                                <span class="fw-bold">Items in Cart: 1</span>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold fs-5">
                                    Total: ₹<span id="total-price">{{final_amount|floatformat:0}}</span>
                                </div>
                                <div class="small text-muted">
                                    Duration: <span id="cart-duration">{{num_months}} month{{num_months|pluralize}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Summary Section -->
            <div class="col-lg-4">
                <div class="card animate__animated animate__fadeInRight">
                    <div class="card-header">
                        <h4>
                            <i class="fas fa-receipt me-2"></i>
                            Order Summary
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="order-summary">
                            <!-- Subtotal -->
                            <div class="summary-row">
                                <span class="summary-label">
                                    <i class="fas fa-calculator me-2"></i>Subtotal:
                                </span>
                                <span class="summary-value">₹<span id="total-amount">{{base_amount|floatformat:0}}</span></span>
                            </div>

                            <!-- Duration Discount Row -->
                            <div class="summary-row discount-row" id="discount-row" {% if discount_amount == 0 %}style="display: none;"{% endif %}>
                                <span class="summary-label text-success">
                                    <i class="fas fa-percent me-2"></i>Duration Discount ({{discount_percentage}}%):
                                </span>
                                <span class="summary-value text-success">-₹<span id="discount-amount">{{discount_amount|floatformat:0}}</span></span>
                            </div>

                            <!-- Coupon Discount Row (hidden by default) -->
                            <div class="summary-row discount-row" id="coupon-discount-row" style="display: none;">
                                <span class="summary-label text-success">
                                    <i class="fas fa-tag me-2"></i>Coupon Discount:
                                </span>
                                <span class="summary-value text-success">-₹<span id="coupon-discount-amount">0</span></span>
                            </div>

                            <!-- Tax Information -->
                            <div class="summary-row">
                                <span class="summary-label">
                                    <i class="fas fa-info-circle me-2"></i>Tax:
                                </span>
                                <span class="summary-value">₹0</span>
                            </div>

                            <hr class="my-3">

                            <!-- Grand Total -->
                            <div class="summary-row total-row">
                                <span class="summary-label fw-bold fs-5">
                                    <i class="fas fa-rupee-sign me-2"></i>Grand Total:
                                </span>
                                <span class="summary-value fw-bold fs-4 text-primary">
                                    ₹<span id="grand-total-display">{{final_amount|floatformat:0}}</span>
                                </span>
                            </div>
                        </div>

                        <!-- Checkout Button -->
                        <div class="checkout-section mt-4">
                            <button id="pay-button" type="button" class="btn btn-success w-100 btn-lg" onclick="initiatePayment()">
                                <i class="fas fa-lock me-2"></i>
                                Pay Now
                                <i class="fas fa-arrow-right ms-2"></i>
                            </button>

                            <!-- Hidden form for payment verification -->
                            <form action="{% url 'verify_payment' %}" method="post" id="payment-form" style="display: none;">
                                {% csrf_token %}
                                <input type="hidden" name="razorpay_order_id" id="razorpay_order_id">
                                <input type="hidden" name="razorpay_payment_id" id="razorpay_payment_id">
                                <input type="hidden" name="razorpay_signature" id="razorpay_signature">
                                <input type="hidden" name="plan_id" value="{{ plan.id }}">
                                <input type="hidden" name="num_months" id="num-months-hidden" value="{{ num_months }}">
                                <input type="hidden" name="coupon_code" id="coupon-code-hidden" value="">
                            </form>

                            <!-- Security Badges -->
                            <div class="security-badges mt-3 text-center">
                                <small class="text-muted d-block mb-2">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    Secured by 256-bit SSL encryption
                                </small>
                                <div class="payment-methods">
                                    <i class="fab fa-cc-visa me-2"></i>
                                    <i class="fab fa-cc-mastercard me-2"></i>
                                    <i class="fab fa-cc-paypal me-2"></i>
                                    <i class="fas fa-university"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tax Information Card -->
                <div class="card mt-4 animate__animated animate__fadeInUp animate__delay-1s">
                    <div class="card-body">
                        <div class="tax-info">
                            <h6 class="text-warning mb-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Tax Information
                            </h6>
                            <p class="small text-muted mb-0" style="line-height: 1.6;">
                                <i class="fas fa-info-circle me-2"></i>
                                Our business turnover does not exceed the specified limit for GST registration,
                                hence GST is not applicable to our products/services.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Bootstrap 5.3.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Razorpay JS -->
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>

    <script>
        // Enhanced Coupon form functionality
        document.addEventListener('DOMContentLoaded', function() {
            const showCouponForm = document.getElementById('showCouponForm');
            const couponContainer = document.getElementById('coupon-form-container');
            const closeCouponForm = document.getElementById('closeCouponForm');
            const couponCode = document.getElementById('coupon-code');
            const couponMessage = document.getElementById('coupon-message');

            // Show coupon form with animation
            showCouponForm.addEventListener('click', function() {
                couponContainer.classList.add('show');
                showCouponForm.style.display = 'none';

                // Focus on input after animation
                setTimeout(() => {
                    couponCode.focus();
                }, 300);
            });

            // Hide coupon form with animation
            function hideCouponForm() {
                couponContainer.classList.remove('show');
                showCouponForm.style.display = 'flex';

                // Clear form and messages
                couponCode.value = '';
                couponMessage.innerHTML = '';
                couponMessage.className = 'mt-2';
            }

            closeCouponForm.addEventListener('click', hideCouponForm);

            // Handle Enter key in coupon input
            couponCode.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    applyCoupon(e);
                }
            });

            // Add loading state to checkout button
            const checkoutBtn = document.getElementById('pay-button');
            const checkoutForm = document.getElementById('razorpay-form');

            checkoutForm.addEventListener('submit', function() {
                checkoutBtn.classList.add('loading');
                checkoutBtn.disabled = true;

                // Re-enable after 5 seconds as fallback
                setTimeout(() => {
                    checkoutBtn.classList.remove('loading');
                    checkoutBtn.disabled = false;
                }, 5000);
            });

            // Initialize tooltips if Bootstrap is available
            if (typeof bootstrap !== 'undefined') {
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }
        });
    </script>
    <script>
        // Enhanced total calculation with animations
        function updateTotal() {
            console.log('updateTotal() called');

            const monthsElement = document.getElementById('num-months');
            const unitPriceElement = document.getElementById('unit-price');

            if (!monthsElement || !unitPriceElement) {
                console.error('Required elements not found:', {
                    monthsElement: !!monthsElement,
                    unitPriceElement: !!unitPriceElement
                });
                return;
            }

            const months = parseInt(monthsElement.value) || 1;
            const unitPrice = parseFloat(unitPriceElement.textContent) || 0;

            console.log(`Input values: months=${months}, unitPrice=${unitPrice}`);

            // Calculate subtotal (unit price × number of months)
            const subtotal = unitPrice * months;
            let finalPrice = subtotal;

            console.log(`Subtotal calculation: ${unitPrice} × ${months} = ${subtotal}`);

            // Apply duration-based discounts only for specific months
            let discountPercentage = 0;
            switch(months) {
                case 3:
                    discountPercentage = 5;
                    break;
                case 6:
                    discountPercentage = 10;
                    break;
                case 12:
                    discountPercentage = 20;
                    break;
            }

            let durationDiscountAmount = 0;
            const discountRow = document.getElementById('discount-row');
            const discountAmountSpan = document.getElementById('discount-amount');
            const discountLabel = discountRow?.querySelector('.summary-label');

            if (discountPercentage > 0) {
                durationDiscountAmount = Math.round(subtotal * discountPercentage / 100);
                finalPrice = subtotal - durationDiscountAmount;

                console.log(`Discount applied: ${discountPercentage}% of ${subtotal} = ${durationDiscountAmount}`);

                // Show duration discount row
                if (discountRow && discountAmountSpan && discountLabel) {
                    discountRow.style.display = 'flex';
                    discountAmountSpan.textContent = durationDiscountAmount;
                    discountLabel.innerHTML = `<i class="fas fa-percent me-2"></i>Duration Discount (${discountPercentage}%):`;
                    discountRow.classList.add('animate-fade-in');
                }
            } else {
                console.log('No duration discount applied');
                // Hide duration discount row
                if (discountRow) {
                    discountRow.style.display = 'none';
                }
            }

            // Apply any existing coupon discount
            const couponDiscountRow = document.getElementById('coupon-discount-row');
            let couponDiscountAmount = 0;
            if (couponDiscountRow && couponDiscountRow.style.display !== 'none') {
                couponDiscountAmount = parseFloat(document.getElementById('coupon-discount-amount').textContent) || 0;
                finalPrice = Math.max(0, finalPrice - couponDiscountAmount);
                console.log(`Coupon discount applied: ${couponDiscountAmount}`);
            }

            console.log(`Final calculation: ${subtotal} - ${durationDiscountAmount} - ${couponDiscountAmount} = ${finalPrice}`);

            // Update all price displays immediately (no animation for debugging)
            const totalAmountElement = document.getElementById('total-amount');
            const grandTotalElement = document.getElementById('grand-total-display');
            const totalPriceElement = document.getElementById('total-price');

            if (totalAmountElement) totalAmountElement.textContent = Math.round(subtotal);
            if (grandTotalElement) grandTotalElement.textContent = Math.round(finalPrice);
            if (totalPriceElement) totalPriceElement.textContent = Math.round(finalPrice);

            // Update cart duration display
            const cartDuration = document.getElementById('cart-duration');
            if (cartDuration) {
                cartDuration.textContent = `${months} month${months > 1 ? 's' : ''}`;
            }

            // Update hidden form values
            const grandTotalInput = document.getElementById('grand-total');
            const numMonthsInput = document.getElementById('num-months-hidden');

            if (grandTotalInput) grandTotalInput.value = finalPrice.toFixed(2);
            if (numMonthsInput) numMonthsInput.value = months;

            console.log(`Updated form values: grand_total=${finalPrice.toFixed(2)}, num_months=${months}`);
        }

        // Animate number changes
        function animateValue(elementId, start, end, duration = 500) {
            const element = document.getElementById(elementId);
            if (!element) return;

            const range = end - start;
            const increment = range / (duration / 16);
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if ((increment > 0 && current >= end) || (increment < 0 && current <= end)) {
                    current = end;
                    clearInterval(timer);
                }
                element.textContent = Math.round(current);
            }, 16);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Wait for all elements to be loaded
            setTimeout(() => {
                updateTotal();
            }, 100);

            // Add change listener to months selector
            const monthsSelect = document.getElementById('num-months');
            if (monthsSelect) {
                monthsSelect.addEventListener('change', function() {
                    // Add visual feedback
                    this.style.transform = 'scale(1.05)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);

                    updateTotal();
                });
            }
        });
    </script>
    <script>
        // Enhanced coupon application with better UX
        function applyCoupon(event) {
            event.preventDefault();

            const couponCode = document.getElementById("coupon-code").value.trim();
            const messageDiv = document.getElementById("coupon-message");
            const applyBtn = event.target.closest('button');

            // Validation
            if (!couponCode) {
                showMessage(messageDiv, "Please enter a coupon code", "error");
                return;
            }

            // Show loading state
            const originalText = applyBtn.innerHTML;
            applyBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Applying...';
            applyBtn.disabled = true;

            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

            fetch('/membership/validate-coupon', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    coupon_code: couponCode
                })
            })
            .then(response => response.json())
            .then(data => {
                const currentGrandTotal = parseFloat(document.getElementById('grand-total-display').textContent);

                if (data.valid) {
                    let couponDiscountAmount;

                    if (data.discount_type === 'percentage') {
                        const subtotal = parseFloat(document.getElementById('total-amount').textContent);
                        couponDiscountAmount = Math.round(subtotal * parseFloat(data.discount) / 100);
                    } else {
                        couponDiscountAmount = parseFloat(data.discount);
                    }

                    const newGrandTotal = Math.max(0, currentGrandTotal - couponDiscountAmount);

                    // Show coupon discount row
                    const couponDiscountRow = document.getElementById('coupon-discount-row');
                    const couponDiscountAmountSpan = document.getElementById('coupon-discount-amount');

                    if (couponDiscountRow && couponDiscountAmountSpan) {
                        couponDiscountRow.style.display = 'flex';
                        couponDiscountAmountSpan.textContent = couponDiscountAmount;
                        couponDiscountRow.classList.add('animate-fade-in');
                    }

                    // Update prices with animation
                    animateValue('grand-total-display', currentGrandTotal, newGrandTotal);
                    animateValue('total-price', parseFloat(document.getElementById('total-price').textContent), newGrandTotal);

                    document.getElementById('grand-total').value = newGrandTotal.toFixed(2);
                    document.getElementById('coupon-code-hidden').value = couponCode;

                    // Show success message
                    showMessage(messageDiv, `Coupon applied! You saved ₹${couponDiscountAmount}`, "success");

                    // Disable the input and button to prevent reapplication
                    document.getElementById("coupon-code").disabled = true;
                    applyBtn.innerHTML = '<i class="fas fa-check me-1"></i>Applied';
                    applyBtn.classList.remove('btn-primary');
                    applyBtn.classList.add('btn-success');

                    console.log(`Coupon applied: ${couponCode}, Discount: ${couponDiscountAmount}, New Total: ${newGrandTotal}`);

                } else {
                    showMessage(messageDiv, data.message || "Invalid coupon code", "error");

                    // Reset button
                    applyBtn.innerHTML = originalText;
                    applyBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error("Error:", error);
                showMessage(messageDiv, "An error occurred. Please try again.", "error");

                // Reset button
                applyBtn.innerHTML = originalText;
                applyBtn.disabled = false;
            });
        }

        // Enhanced message display function
        function showMessage(element, message, type) {
            element.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-1"></i>${message}`;
            element.className = `mt-2 message-${type}`;
            element.classList.add('animate-fade-in');

            // Remove animation class after animation completes
            setTimeout(() => {
                element.classList.remove('animate-fade-in');
            }, 500);
        }

        // Add global error handling
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
        });

        // Add performance monitoring
        window.addEventListener('load', function() {
            if ('performance' in window) {
                const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                console.log(`Page loaded in ${loadTime}ms`);
            }
        });

        // Payment initiation function
        function initiatePayment() {
            const payButton = document.getElementById('pay-button');
            const months = parseInt(document.getElementById('num-months').value) || 1;
            const grandTotal = parseFloat(document.getElementById('grand-total-display').textContent) || 0;
            const couponCode = document.getElementById('coupon-code-hidden').value || '';

            // Show loading state
            payButton.classList.add('loading');
            payButton.disabled = true;
            payButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';

            // Create Razorpay order
            fetch(`/membership/create_order/{{ plan.id }}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: new URLSearchParams({
                    'plan_id': '{{ plan.id }}',
                    'num_months': months,
                    'grand_total': grandTotal,
                    'coupon_code': couponCode
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Initialize Razorpay payment
                    const options = {
                        "key": data.razorpay_key,
                        "amount": data.amount,
                        "currency": data.currency,
                        "name": "Librainian",
                        "description": `${data.plan_name} - ${months} month${months > 1 ? 's' : ''}`,
                        "order_id": data.order_id,
                        "handler": function (response) {
                            // Payment successful
                            console.log('Payment successful, response:', response);

                            document.getElementById('razorpay_order_id').value = response.razorpay_order_id;
                            document.getElementById('razorpay_payment_id').value = response.razorpay_payment_id;
                            document.getElementById('razorpay_signature').value = response.razorpay_signature;

                            console.log('Form values set:', {
                                order_id: response.razorpay_order_id,
                                payment_id: response.razorpay_payment_id,
                                signature: response.razorpay_signature,
                                plan_id: '{{ plan.id }}',
                                num_months: document.getElementById('num-months-hidden').value
                            });

                            // Submit the verification form
                            console.log('Submitting verification form...');
                            document.getElementById('payment-form').submit();
                        },
                        "prefill": {
                            "name": "{{ user.get_full_name|default:user.username }}",
                            "email": "{{ user.email }}"
                        },
                        "theme": {
                            "color": "#294282"
                        },
                        "modal": {
                            "ondismiss": function() {
                                // Reset button state if payment is cancelled
                                payButton.classList.remove('loading');
                                payButton.disabled = false;
                                payButton.innerHTML = '<i class="fas fa-lock me-2"></i>Pay Now<i class="fas fa-arrow-right ms-2"></i>';
                            }
                        }
                    };

                    const rzp = new Razorpay(options);
                    rzp.open();
                } else {
                    alert('Error creating payment order: ' + data.message);
                    // Reset button state
                    payButton.classList.remove('loading');
                    payButton.disabled = false;
                    payButton.innerHTML = '<i class="fas fa-lock me-2"></i>Pay Now<i class="fas fa-arrow-right ms-2"></i>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while processing payment. Please try again.');
                // Reset button state
                payButton.classList.remove('loading');
                payButton.disabled = false;
                payButton.innerHTML = '<i class="fas fa-lock me-2"></i>Pay Now<i class="fas fa-arrow-right ms-2"></i>';
            });
        }
    </script>
</body>
</html>
