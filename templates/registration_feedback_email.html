<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>New Registration Feedback - {{librarian.library_name}}</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }

        /* Email client specific styles */
        .ReadMsgBody { width: 100%; }
        .ExternalClass { width: 100%; }
        .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
            line-height: 100%;
        }

        /* Main container */
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333333;
        }

        /* Header styles */
        .email-header {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            padding: 30px 20px;
            text-align: center;
            color: white;
        }

        .header-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }

        .email-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .email-subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }

        /* Content area */
        .email-content {
            padding: 30px 20px;
        }

        .greeting {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
        }

        .message {
            font-size: 16px;
            color: #4b5563;
            margin-bottom: 25px;
            line-height: 1.7;
        }

        /* Feedback notification cards */
        .feedback-notification {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .feedback-icon {
            font-size: 24px;
            margin-bottom: 15px;
            display: inline-block;
        }

        .notification-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        /* Detail items */
        .feedback-details {
            display: block;
        }

        .detail-item {
            margin-bottom: 15px;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .detail-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .detail-label {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .detail-value {
            font-size: 16px;
            color: #1f2937;
            font-weight: 500;
        }

        .message-content {
            background: #ffffff;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 15px;
            font-style: italic;
            color: #4b5563;
            margin-top: 8px;
        }

        /* Rating displays */
        .rating-display {
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .rating-stars {
            color: #fbbf24;
            font-size: 18px;
        }

        .rating-number {
            background: #6366f1;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
        }

        /* Status indicators */
        .status-positive {
            color: #10b981;
            font-weight: 600;
        }

        .status-negative {
            color: #ef4444;
            font-weight: 600;
        }

        .status-neutral {
            color: #6b7280;
            font-weight: 500;
        }

        /* Meta section */
        .meta-section {
            background: #f9fafb;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }

        .meta-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 15px;
        }

        /* Action required section */
        .action-required {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-left: 4px solid #f59e0b;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }

        .action-title {
            font-size: 18px;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 10px;
        }

        .action-text {
            color: #78350f;
            font-size: 15px;
            line-height: 1.6;
        }

        .action-text ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .action-text li {
            margin-bottom: 5px;
        }

        /* Footer */
        .email-footer {
            background: #f8fafc;
            padding: 25px 20px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }

        .email-footer p {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .email-footer a {
            color: #6366f1;
            text-decoration: none;
            font-weight: 500;
        }

        .email-footer a:hover {
            text-decoration: underline;
        }

        /* Responsive styles */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
                max-width: 100% !important;
            }

            .email-header {
                padding: 25px 15px !important;
            }

            .email-title {
                font-size: 24px !important;
            }

            .email-content {
                padding: 20px 15px !important;
            }

            .feedback-notification {
                padding: 20px 15px !important;
                margin-bottom: 20px !important;
            }

            .notification-title {
                font-size: 18px !important;
            }

            .greeting {
                font-size: 20px !important;
            }

            .detail-item {
                padding: 10px 0 !important;
            }

            .detail-label {
                font-size: 13px !important;
            }

            .detail-value {
                font-size: 15px !important;
            }

            .meta-section,
            .action-required {
                padding: 15px !important;
                margin: 20px 0 !important;
            }

            .email-footer {
                padding: 20px 15px !important;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .email-container {
                background-color: #1f2937 !important;
                color: #f9fafb !important;
            }

            .feedback-notification {
                background: #374151 !important;
                border-color: #4b5563 !important;
            }

            .meta-section {
                background: #374151 !important;
            }

            .message-content {
                background: #4b5563 !important;
                border-color: #6b7280 !important;
                color: #d1d5db !important;
            }

            .email-footer {
                background: #374151 !important;
                border-color: #4b5563 !important;
            }
        }

        /* Print styles */
        @media print {
            .email-container {
                box-shadow: none !important;
                max-width: none !important;
            }

            .email-header {
                background: #6366f1 !important;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <span class="header-icon">📝</span>
            <h1 class="email-title">New Registration Feedback</h1>
            <p class="email-subtitle">A user has submitted feedback about their registration experience</p>
        </div>

        <!-- Content -->
        <div class="email-content">
            <h2 class="greeting">Hello Admin!</h2>
            <p class="message">
                A new feedback has been submitted through the registration process at <strong>{{librarian.library_name}}</strong>. Please review the details below to understand the user experience and identify areas for improvement.
            </p>

            <!-- Student Information Section -->
            <div class="feedback-notification">
                <span class="feedback-icon">👤</span>
                <h3 class="notification-title">Student Information</h3>

                <div class="feedback-details">
                    <div class="detail-item">
                        <div class="detail-label">Name</div>
                        <div class="detail-value">{{temp_student.name}}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">Email</div>
                        <div class="detail-value">
                            <a href="mailto:{{temp_student.email}}" style="color: #6366f1; text-decoration: none;">{{temp_student.email}}</a>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">Mobile</div>
                        <div class="detail-value">
                            <a href="tel:{{temp_student.mobile}}" style="color: #6366f1; text-decoration: none;">{{temp_student.mobile}}</a>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">Library</div>
                        <div class="detail-value">{{librarian.library_name}}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">Registration Date</div>
                        <div class="detail-value">{{temp_student.registration_date|date:"F j, Y"}}</div>
                    </div>
                </div>
            </div>

            <!-- Feedback Responses Section -->
            <div class="feedback-notification">
                <span class="feedback-icon">📊</span>
                <h3 class="notification-title">Feedback Responses</h3>

                <div class="feedback-details">
                    <div class="detail-item">
                        <div class="detail-label">1. Ease of Registration</div>
                        <div class="detail-value">
                            <div class="rating-display">
                                <span class="rating-number">{{feedback.ease_rating}}/10</span>
                                <span class="status-{% if feedback.ease_rating >= 7 %}positive{% elif feedback.ease_rating >= 4 %}neutral{% else %}negative{% endif %}">
                                    {{feedback.get_ease_rating_display_text}}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">2. Time Taken</div>
                        <div class="detail-value">
                            <span style="background: #f3f4f6; padding: 4px 12px; border-radius: 20px; font-size: 14px;">
                                ⏱️ {{feedback.get_time_taken_display}}
                            </span>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">3. Issues Encountered</div>
                        <div class="detail-value">
                            {% if feedback.faced_issues %}
                                <span class="status-negative">⚠️ Yes - Issues Reported</span>
                                {% if feedback.issue_description %}
                                    <div class="message-content">{{feedback.issue_description}}</div>
                                {% endif %}
                            {% else %}
                                <span class="status-positive">✅ No Issues</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">4. Design Rating</div>
                        <div class="detail-value">
                            <div class="rating-display">
                                <span class="rating-stars">{{feedback.get_design_rating_stars}}</span>
                                <span class="rating-number">{{feedback.design_rating}}/5</span>
                            </div>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">5. Device Used</div>
                        <div class="detail-value">
                            <span style="background: #f3f4f6; padding: 4px 12px; border-radius: 20px; font-size: 14px;">
                                {% if feedback.device_used == 'mobile' %}📱 Mobile
                                {% elif feedback.device_used == 'tablet' %}📱 Tablet
                                {% elif feedback.device_used == 'laptop_desktop' %}💻 Laptop/Desktop
                                {% else %}🔧 Other{% endif %}
                            </span>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">6. QR Code Accessibility</div>
                        <div class="detail-value">
                            {% if feedback.qr_easy_to_scan %}
                                <span class="status-positive">✅ Easy to Scan</span>
                            {% else %}
                                <span class="status-negative">❌ Scanning Issues</span>
                                {% if feedback.qr_problem_description %}
                                    <div class="message-content">{{feedback.qr_problem_description}}</div>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>

                    {% if feedback.suggestions %}
                    <div class="detail-item">
                        <div class="detail-label">7. Improvement Suggestions</div>
                        <div class="detail-value">
                            <div class="message-content">💡 {{feedback.suggestions}}</div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="detail-item">
                        <div class="detail-label">8. System Recommendation</div>
                        <div class="detail-value">
                            {% if feedback.would_recommend %}
                                <span class="status-positive">👍 Would Recommend</span>
                            {% else %}
                                <span class="status-negative">👎 Would Not Recommend</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technical Information -->
            <div class="meta-section">
                <h4 class="meta-title">📋 Technical Details</h4>
                <div class="feedback-details">
                    <div class="detail-item">
                        <div class="detail-label">Submitted On</div>
                        <div class="detail-value">{{feedback.submitted_at|date:"F j, Y, g:i a"}}</div>
                    </div>
                    {% if feedback.ip_address %}
                    <div class="detail-item">
                        <div class="detail-label">IP Address</div>
                        <div class="detail-value">{{feedback.ip_address}}</div>
                    </div>
                    {% endif %}
                    {% if feedback.user_agent %}
                    <div class="detail-item">
                        <div class="detail-label">Browser/Device Info</div>
                        <div class="detail-value" style="font-size: 13px; color: #6b7280;">{{feedback.user_agent|truncatechars:120}}</div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Summary and Action Required -->
            <div class="action-required">
                <h4 class="action-title">📈 Feedback Summary & Action Items</h4>
                <p class="action-text">
                    <strong>Overall Experience Assessment:</strong>
                    {% if feedback.ease_rating >= 8 %}
                        <span style="color: #059669;">🟢 Excellent</span> - User had a smooth registration experience.
                    {% elif feedback.ease_rating >= 6 %}
                        <span style="color: #0891b2;">🔵 Good</span> - User had a generally positive experience with minor issues.
                    {% elif feedback.ease_rating >= 4 %}
                        <span style="color: #d97706;">🟡 Fair</span> - User experienced some difficulties that should be addressed.
                    {% else %}
                        <span style="color: #dc2626;">🔴 Poor</span> - User had significant difficulties that require immediate attention.
                    {% endif %}
                </p>

                {% if feedback.faced_issues or not feedback.qr_easy_to_scan or not feedback.would_recommend %}
                <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 15px; margin-top: 15px;">
                    <p class="action-text" style="color: #dc2626; margin-bottom: 10px;">
                        <strong>⚠️ Immediate Action Required:</strong> This feedback indicates critical issues:
                    </p>
                    <ul style="margin: 0; padding-left: 20px; color: #dc2626;">
                        {% if feedback.faced_issues %}
                            <li><strong>Registration Issues:</strong> User encountered problems during the registration process</li>
                        {% endif %}
                        {% if not feedback.qr_easy_to_scan %}
                            <li><strong>QR Code Problems:</strong> User had difficulty scanning or accessing the QR code</li>
                        {% endif %}
                        {% if not feedback.would_recommend %}
                            <li><strong>Poor User Satisfaction:</strong> User would not recommend the system to others</li>
                        {% endif %}
                    </ul>
                </div>
                {% else %}
                <div style="background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px; padding: 15px; margin-top: 15px;">
                    <p style="color: #166534; margin: 0;">
                        <strong>✅ Positive Feedback:</strong> No critical issues reported. User experience appears satisfactory.
                    </p>
                </div>
                {% endif %}
            </div>

            <!-- Quick Stats Summary -->
            <div class="feedback-notification" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
                <span class="feedback-icon">📊</span>
                <h3 class="notification-title">Quick Stats Summary</h3>
                <div style="display: flex; flex-wrap: wrap; gap: 15px; justify-content: space-around; text-align: center;">
                    <div style="flex: 1; min-width: 120px;">
                        <div style="font-size: 24px; font-weight: bold; color: #6366f1;">{{feedback.ease_rating}}/10</div>
                        <div style="font-size: 12px; color: #6b7280; text-transform: uppercase;">Ease Rating</div>
                    </div>
                    <div style="flex: 1; min-width: 120px;">
                        <div style="font-size: 24px; font-weight: bold; color: #f59e0b;">{{feedback.design_rating}}/5</div>
                        <div style="font-size: 12px; color: #6b7280; text-transform: uppercase;">Design Rating</div>
                    </div>
                    <div style="flex: 1; min-width: 120px;">
                        <div style="font-size: 24px; font-weight: bold; color: {% if feedback.would_recommend %}#10b981{% else %}#ef4444{% endif %};">
                            {% if feedback.would_recommend %}👍{% else %}👎{% endif %}
                        </div>
                        <div style="font-size: 12px; color: #6b7280; text-transform: uppercase;">Recommendation</div>
                    </div>
                </div>
            </div>

            <p class="message" style="background: #f8fafc; padding: 20px; border-radius: 8px; border-left: 4px solid #6366f1;">
                💡 <strong>Next Steps:</strong> This feedback helps us understand user experience and identify areas for improvement. Please consider following up with the user if necessary and implementing any suggested improvements to enhance the registration process.
            </p>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p><strong>Librainian Registration System</strong></p>
            <p>This is an automated notification from the Librainian Registration System.</p>
            <p>
                For support, contact:
                <a href="mailto:<EMAIL>"><EMAIL></a> |
                <a href="https://librainian.com">Visit Website</a>
            </p>
            <p style="font-size: 12px; margin-top: 15px;">
                © {{feedback.submitted_at|date:"Y"}} Librainian. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
