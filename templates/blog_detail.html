{% extends "base.html" %}

{% block title %}{{ blog.title }} - Librainian{% endblock %}

{% block page_title %}Blog Detail{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="/blogs/">Blogs</a></li>
<li class="breadcrumb-item active" aria-current="page">{{ blog.title|truncatechars:30 }}</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Blog Detail Specific Styles - using unified glass system */

    .blog-header {
        text-align: center;
        padding: 2rem;
        position: relative;
        z-index: 2;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .blog-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .blog-meta {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1.5rem;
        color: var(--gray-600);
        font-size: 0.875rem;
        margin-bottom: 1.5rem;
    }

    .blog-meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.8);
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .blog-image-container {
        text-align: center;
        margin: 2rem 0;
        position: relative;
        z-index: 2;
    }

    .blog-image {
        max-width: 100%;
        height: auto;
        max-height: 400px;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-lg);
        transition: var(--transition-slow);
        object-fit: cover;
    }

    .blog-image:hover {
        transform: scale(1.02);
        box-shadow: var(--shadow-xl);
    }

    .blog-content {
        padding: 2rem;
        position: relative;
        z-index: 2;
    }

    .blog-description {
        font-size: 1.125rem;
        color: var(--gray-700);
        line-height: 1.7;
        margin-bottom: 2rem;
        font-weight: 500;
        text-align: justify;
    }

    .blog-content-body {
        color: var(--gray-800);
        line-height: 1.8;
        font-size: 1rem;
    }

    .blog-content-body h1,
    .blog-content-body h2,
    .blog-content-body h3,
    .blog-content-body h4,
    .blog-content-body h5,
    .blog-content-body h6 {
        color: var(--gray-900);
        margin-top: 2rem;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .blog-content-body p {
        margin-bottom: 1.5rem;
        text-align: justify;
    }

    .blog-content-body img {
        max-width: 100%;
        height: auto;
        border-radius: var(--border-radius);
        margin: 1.5rem 0;
        box-shadow: var(--shadow-md);
    }

    .blog-content-body blockquote {
        background: rgba(99, 102, 241, 0.1);
        border-left: 4px solid var(--primary);
        padding: 1rem 1.5rem;
        margin: 1.5rem 0;
        border-radius: 0 var(--border-radius) var(--border-radius) 0;
        font-style: italic;
    }

    .blog-content-body ul,
    .blog-content-body ol {
        margin-bottom: 1.5rem;
        padding-left: 2rem;
    }

    .blog-content-body li {
        margin-bottom: 0.5rem;
    }

    .blog-actions {
        padding: 2rem;
        text-align: center;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 2;
    }

    .back-btn {
        background: linear-gradient(135deg, var(--gray-600) 0%, var(--gray-700) 100%);
        border: none;
        border-radius: var(--border-radius);
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 0.875rem;
        color: white;
        text-decoration: none;
        transition: var(--transition-slow);
        box-shadow: var(--shadow-md);
        position: relative;
        overflow: hidden;
        display: inline-block;
    }

    .back-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
        color: white;
        text-decoration: none;
    }

    .back-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .back-btn:hover::before {
        left: 100%;
    }

    .category-badge {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: var(--shadow-md);
    }

    @media (max-width: 768px) {
        .blog-title {
            font-size: 1.875rem;
        }
        
        .blog-meta {
            flex-direction: column;
            gap: 1rem;
        }
        
        .blog-header,
        .blog-content,
        .blog-actions {
            padding: 1.5rem;
        }
        
        .blog-description {
            font-size: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-content fade-in">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="modern-card">
            <div class="modern-card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="page-title mb-2">
                            <i class="fas fa-blog me-2 text-primary"></i>
                            Blog Article
                        </h2>
                        <p class="page-subtitle text-muted mb-0">
                            Read and explore detailed blog content
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="page-actions">
                            <span class="badge bg-info-subtle text-info px-3 py-2">
                                <i class="fas fa-eye me-1"></i>
                                Reading Mode
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Blog Detail Content -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="glass-card">
                <!-- Blog Header -->
                <div class="blog-header">
                    <h1 class="blog-title">{{ blog.title }}</h1>
                    <div class="blog-meta">
                        <div class="blog-meta-item">
                            <i class="fas fa-tag text-primary"></i>
                            <span class="category-badge">{{ blog.category }}</span>
                        </div>
                        <div class="blog-meta-item">
                            <i class="fas fa-calendar text-success"></i>
                            <span>{{ blog.date_created|date:"M d, Y" }}</span>
                        </div>
                        <div class="blog-meta-item">
                            <i class="fas fa-clock text-warning"></i>
                            <span>{{ blog.date_created|date:"g:i A" }}</span>
                        </div>
                    </div>
                    
                    <!-- Blog Image -->
                    {% if blog.image %}
                    <div class="blog-image-container">
                        <img src="{{ blog.image.url }}" 
                             class="blog-image" 
                             alt="{{ blog.title }}" 
                             loading="lazy">
                    </div>
                    {% endif %}
                </div>

                <!-- Blog Content -->
                <div class="blog-content">
                    {% if blog.description %}
                    <div class="blog-description">
                        {{ blog.description }}
                    </div>
                    {% endif %}
                    
                    <div class="blog-content-body">
                        {{ blog.content|safe }}
                    </div>
                </div>

                <!-- Blog Actions -->
                <div class="blog-actions">
                    <a href="/blogs/" class="back-btn">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Blog List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add staggered animation delays
        const cards = document.querySelectorAll('.modern-card, .glass-card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${(index + 1) * 0.1}s`;
            card.classList.add('slide-up');
        });

        // Show welcome notification
        setTimeout(() => {
            if (window.modernDashboard) {
                window.modernDashboard.showToast(
                    'Blog Article Loaded!',
                    'Enjoy reading the content.',
                    'info'
                );
            }
        }, 1000);
    });
</script>
{% endblock %}
