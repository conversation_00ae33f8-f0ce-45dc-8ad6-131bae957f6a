{% extends "base.html" %}

{% block title %}Add Payment - {{ student.name }}{% endblock %}

{% block extra_css %}
<style>
    .payment-container {
        max-width: 1000px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .glass-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .dark-mode .glass-card {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .payment-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .payment-header h1 {
        color: #6366f1;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .dark-mode .payment-header h1 {
        color: #818cf8;
    }

    .student-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dark-mode .info-item {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.05);
    }

    .info-label {
        font-weight: 600;
        color: #64748b;
    }

    .dark-mode .info-label {
        color: #94a3b8;
    }

    .info-value {
        font-weight: 700;
        color: #1e293b;
    }

    .dark-mode .info-value {
        color: #f1f5f9;
    }

    .amount-due {
        color: #dc2626 !important;
    }

    .dark-mode .amount-due {
        color: #f87171 !important;
    }

    .payment-form {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 2rem;
    }

    .dark-mode .payment-form {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        display: block;
    }

    .dark-mode .form-label {
        color: #d1d5db;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .dark-mode .form-control {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #f1f5f9;
    }

    .form-control:focus {
        outline: none;
        border-color: #6366f1;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        background: rgba(255, 255, 255, 0.95);
    }

    .dark-mode .form-control:focus {
        border-color: #818cf8;
        box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.1);
        background: rgba(255, 255, 255, 0.15);
    }

    .calculation-display {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .dark-mode .calculation-display {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .calc-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .dark-mode .calc-row {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .calc-row:last-child {
        border-bottom: none;
        font-weight: 700;
        font-size: 1.1rem;
    }

    .calc-row.highlight {
        background: rgba(99, 102, 241, 0.1);
        border-radius: 8px;
        padding: 0.75rem;
        margin-top: 0.5rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        border: none;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
        background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%);
    }

    .btn-secondary {
        background: rgba(107, 114, 128, 0.1);
        border: 1px solid rgba(107, 114, 128, 0.3);
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        color: #6b7280;
        transition: all 0.3s ease;
    }

    .dark-mode .btn-secondary {
        color: #9ca3af;
        border-color: rgba(156, 163, 175, 0.3);
    }

    .btn-secondary:hover {
        background: rgba(107, 114, 128, 0.2);
        transform: translateY(-1px);
    }

    .payment-history {
        margin-top: 2rem;
    }

    .history-item {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .dark-mode .history-item {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.05);
    }

    .history-amount {
        font-weight: 700;
        color: #059669;
    }

    .dark-mode .history-amount {
        color: #34d399;
    }

    @media (max-width: 768px) {
        .payment-container {
            margin: 1rem auto;
            padding: 0 0.5rem;
        }

        .glass-card {
            padding: 1.5rem;
            border-radius: 16px;
        }

        .student-info {
            grid-template-columns: 1fr;
        }

        .calc-row {
            font-size: 0.9rem;
        }

        .btn-primary, .btn-secondary {
            width: 100%;
            margin-bottom: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="payment-container">
    <!-- Header -->
    <div class="glass-card">
        <div class="payment-header">
            <h1><i class="fas fa-coins me-2"></i>Add Payment</h1>
            <p class="text-muted">Record additional payment for {{ student.name }}</p>
        </div>

        <!-- Student & Invoice Information -->
        <div class="student-info">
            <div class="info-item">
                <span class="info-label">Student Name:</span>
                <span class="info-value">{{ student.name }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Invoice ID:</span>
                <span class="info-value">#{{ invoice.invoice_id }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Total Amount:</span>
                <span class="info-value">₹{{ invoice.total_amount|floatformat:0 }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Amount Paid:</span>
                <span class="info-value">₹{{ invoice.total_paid|floatformat:0 }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Amount Due:</span>
                <span class="info-value amount-due">₹{{ invoice.remaining_due|floatformat:0 }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Payment Status:</span>
                <span class="info-value">{{ invoice.payment_status }}</span>
            </div>
        </div>
    </div>

    <!-- Payment Form -->
    <div class="glass-card">
        <h3 class="mb-4"><i class="fas fa-credit-card me-2"></i>Payment Details</h3>
        
        <form method="POST" action="{% url 'record_payment' invoice.slug %}" class="payment-form">
            {% csrf_token %}
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="amount_paid" class="form-label">Payment Amount (₹) *</label>
                        <input type="number" 
                               class="form-control" 
                               id="amount_paid" 
                               name="amount_paid" 
                               min="1" 
                               max="{{ invoice.remaining_due }}"
                               placeholder="Enter amount"
                               required
                               oninput="updateCalculations()">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="payment_mode" class="form-label">Payment Mode</label>
                        <select class="form-control" id="payment_mode" name="payment_mode">
                            <option value="Cash">Cash</option>
                            <option value="UPI">UPI</option>
                            <option value="Card">Card</option>
                            <option value="Bank Transfer">Bank Transfer</option>
                            <option value="Cheque">Cheque</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="next_payment_date" class="form-label">Next Commitment Date</label>
                        <input type="date" 
                               class="form-control" 
                               id="next_payment_date" 
                               name="next_payment_date"
                               min="{{ today|date:'Y-m-d' }}">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="notes" class="form-label">Notes (Optional)</label>
                        <input type="text" 
                               class="form-control" 
                               id="notes" 
                               name="notes" 
                               placeholder="Additional notes">
                    </div>
                </div>
            </div>

            <!-- Calculation Display -->
            <div class="calculation-display">
                <div class="calc-row">
                    <span>Amount Due:</span>
                    <span id="calcAmountDue">₹{{ invoice.remaining_due|floatformat:0 }}</span>
                </div>
                <div class="calc-row">
                    <span>Payment Amount:</span>
                    <span id="calcPaymentAmount">₹0</span>
                </div>
                <div class="calc-row highlight">
                    <span>Remaining After Payment:</span>
                    <span id="calcRemaining">₹{{ invoice.remaining_due|floatformat:0 }}</span>
                </div>
            </div>

            <div class="d-flex gap-3 justify-content-end">
                <a href="{% url 'partial_payments_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to List
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>Submit Payment
                </button>
            </div>
        </form>
    </div>

    <!-- Payment History -->
    {% if payments %}
    <div class="glass-card">
        <h3 class="mb-4"><i class="fas fa-history me-2"></i>Payment History</h3>
        
        {% for payment in payments %}
        <div class="history-item">
            <div>
                <div class="fw-bold">₹{{ payment.amount_paid|floatformat:0 }}</div>
                <small class="text-muted">{{ payment.payment_date|date:"M d, Y" }} • {{ payment.payment_mode }}</small>
                {% if payment.notes %}
                <div><small class="text-muted">{{ payment.notes }}</small></div>
                {% endif %}
            </div>
            <div class="text-end">
                {% if payment.next_commitment_date %}
                <small class="text-muted">Next: {{ payment.next_commitment_date|date:"M d, Y" }}</small>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}
</div>

<script>
    const amountDue = {{ invoice.remaining_due }};
    
    function updateCalculations() {
        const paymentAmount = parseFloat(document.getElementById('amount_paid').value) || 0;
        const remaining = Math.max(0, amountDue - paymentAmount);
        
        document.getElementById('calcPaymentAmount').textContent = `₹${paymentAmount.toLocaleString()}`;
        document.getElementById('calcRemaining').textContent = `₹${remaining.toLocaleString()}`;
    }
    
    // Set minimum date to today
    document.getElementById('next_payment_date').min = new Date().toISOString().split('T')[0];
</script>
{% endblock %}
