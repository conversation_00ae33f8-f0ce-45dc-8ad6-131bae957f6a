<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="google" content="notranslate">
  <title>Upgrade Your Subscription Plan</title>

  <!-- Bootstrap 5.3.3 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">
  
  <style>
    body {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      margin: 0;
      font-family: 'Comfortaa', 'Arial', 'Helvetica', sans-serif;
      line-height: 1.6;
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      background-color: #f4f5f7;
      color: #333;
    }

    .container {
      flex: 1;
      margin-top: 50px;
      margin-bottom: 200px;
      max-width: 800px;
      background-color: #e8e8eb;
      padding: 20px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      border-radius: 4px;
    }

    @media (min-width: 768px) {
      .container {
        padding: 40px;
      }
    }

    .header {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid #e0e0e0;
    }

    .header h2 {
      color: #2c3e50;
      font-weight: 600;
      font-size: 24px;
      margin-bottom: 10px;
    }

    .header p {
      color: #7f8c8d;
      font-size: 14px;
    }

    .plan-details {
      background-color: #ffffff;
      border: 1px solid #e0e0e0;
      padding: 15px;
      border-radius: 14px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.075);
      margin-bottom: 30px;
    }

    @media (min-width: 768px) {
      .plan-details {
        padding: 25px;
      }
    }

    .plan-details h5 {
      color: #2c3e50;
      font-weight: 600;
      margin-bottom: 20px;
      font-size: 18px;
    }

    .list-group-item {
      background-color: transparent;
      border: none;
      padding: 8px 0;
      color: #495057;
      font-size: 14px;
      display: flex;
      align-items: center;
      word-break: break-word;
    }

    .list-group-item i {
      margin-right: 10px;
      color: #3498db;
      width: 20px;
    }

    .btn-custom {
      padding: 10px 20px;
      font-weight: 500;
      font-size: 14px;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
      border-radius: 4px;
    }

    .btn-primary {
      background-color: #3498db;
      border-color: #3498db;
    }

    .btn-primary:hover {
      background-color: #2980b9;
      border-color: #2980b9;
    }

    .btn-outline-secondary {
      color: #7f8c8d;
      border-color: #7f8c8d;
    }

    .btn-outline-secondary:hover {
      background-color: #7f8c8d;
      color: #ffffff;
    }

    .footer {
      padding: 20px;
      background-color: #2c3e50;
      color: #ffffff;
      text-align: center;
    }

    .footer p {
      margin: 0;
      font-size: 14px;
    }

    .footer a {
      color: #3498db;
      text-decoration: none;
    }

    .footer a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>

  <div class="container">
    <div class="header">
      <h2>Upgrade Your Subscription Plan</h2>
      <p>Review your current plan and explore premium options to enhance your experience.</p>
    </div>

    <div class="plan-details">
      <h5>Current Plan Details</h5>
      <ul class="list-group">
        <li class="list-group-item"><i class="fas fa-box"></i> Plan Name: {{membership.plan.name}}</li>
        <li class="list-group-item"><i class="fas fa-tag"></i> Price: {{membership.plan.price}}</li>
        <li class="list-group-item"><i class="fas fa-calendar-alt"></i> Start Date: {{membership.start_date}}</li>
        <li class="list-group-item"><i class="fas fa-calendar-check"></i> Expiry Date: {{membership.expiry_date}}</li>
        <li class="list-group-item"><i class="fas fa-list"></i> Features: {{membership.plan.description_line_01}}, {{membership.plan.description_line_02}}, {{membership.plan.description_line_03}}</li>
      </ul>
    </div>

    <div class="d-flex justify-content-between flex-wrap">
      <a href="/membership/plans/" class="btn btn-primary btn-custom mb-2">Upgrade Plan</a>
      <a href="/librarian/dashboard/" class="btn btn-outline-secondary btn-custom mb-2">Return to Dashboard</a>
    </div>
  </div>

  <!-- Bootstrap 5.3.3 JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
