<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thank You - {{librarian.library_name}}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Light mode colors */
            --primary-color: #6366f1;
            --primary-light: #818cf8;
            --success-color: #10b981;
            --surface-color: rgba(255, 255, 255, 0.25);
            --surface-hover: rgba(255, 255, 255, 0.35);
            --background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: rgba(255, 255, 255, 0.2);
            --shadow-light: rgba(255, 255, 255, 0.25);
            --shadow-dark: rgba(0, 0, 0, 0.1);
        }

        /* Dark mode detection and variables */
        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #818cf8;
                --primary-light: #a5b4fc;
                --success-color: #34d399;
                --surface-color: rgba(17, 24, 39, 0.25);
                --surface-hover: rgba(17, 24, 39, 0.35);
                --background-color: linear-gradient(135deg, #1e293b 0%, #334155 100%);
                --text-primary: #f9fafb;
                --text-secondary: #d1d5db;
                --border-color: rgba(255, 255, 255, 0.1);
                --shadow-light: rgba(255, 255, 255, 0.1);
                --shadow-dark: rgba(0, 0, 0, 0.3);
            }
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-primary);
            line-height: 1.6;
            background: var(--background-color) fixed;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            backdrop-filter: blur(10px);
        }

        .success-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .success-container {
            background: var(--surface-color);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 24px;
            box-shadow: 
                0 8px 32px var(--shadow-dark),
                inset 0 1px 0 var(--shadow-light);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        /* Glassmorphism accent bar */
        .success-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--success-color), var(--primary-color));
            border-radius: 24px 24px 0 0;
        }

        .success-icon {
            font-size: 4rem;
            color: var(--success-color);
            margin-bottom: 24px;
            animation: bounce 1s ease-in-out;
        }

        .success-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 16px;
            text-shadow: 0 2px 4px var(--shadow-dark);
        }

        .success-message {
            font-size: 1.125rem;
            color: var(--text-secondary);
            margin-bottom: 32px;
            line-height: 1.7;
        }

        .feedback-summary {
            background: var(--surface-hover);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 24px;
            margin: 24px 0;
            text-align: left;
        }

        .summary-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
            text-align: center;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .summary-item:last-child {
            border-bottom: none;
        }

        .summary-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .summary-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-top: 32px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border: 1px solid var(--border-color);
            padding: 12px 24px;
            font-weight: 600;
            border-radius: 12px;
            color: white;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-secondary {
            background: var(--surface-hover);
            border: 1px solid var(--border-color);
            padding: 12px 24px;
            font-weight: 500;
            border-radius: 12px;
            color: var(--text-primary);
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-secondary:hover {
            background: var(--surface-color);
            transform: translateY(-1px);
            color: var(--text-primary);
            text-decoration: none;
        }

        /* Animations */
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @keyframes fadeInUp {
            from { 
                opacity: 0; 
                transform: translateY(30px) scale(0.95); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0) scale(1); 
            }
        }

        .success-container {
            animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .success-wrapper {
                padding: 16px;
            }

            .success-container {
                padding: 24px 20px;
                border-radius: 20px;
            }

            .success-title {
                font-size: 1.5rem;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn-primary,
            .btn-secondary {
                width: 100%;
                justify-content: center;
            }
        }

        /* Confetti animation */
        .confetti {
            position: fixed;
            top: -10px;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        }

        .confetti-piece {
            position: absolute;
            width: 10px;
            height: 10px;
            background: var(--primary-color);
            animation: confetti-fall 3s linear infinite;
        }

        @keyframes confetti-fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(720deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="success-wrapper">
        <div class="success-container">
            <div class="success-icon">🎉</div>
            <h1 class="success-title">Thank You!</h1>
            <p class="success-message">
                Your feedback has been successfully submitted. We truly appreciate you taking the time to share your experience with us at <strong>{{librarian.library_name}}</strong>.
            </p>

            <div class="feedback-summary">
                <div class="summary-title">Your Feedback Summary</div>
                <div class="summary-item">
                    <span class="summary-label">Registration Ease</span>
                    <span class="summary-value">{{feedback.ease_rating}}/10</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Design Rating</span>
                    <span class="summary-value">{{feedback.get_design_rating_stars}}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Would Recommend</span>
                    <span class="summary-value">
                        {% if feedback.would_recommend %}👍 Yes{% else %}👎 No{% endif %}
                    </span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Submitted</span>
                    <span class="summary-value">{{feedback.submitted_at|date:"M j, Y g:i A"}}</span>
                </div>
            </div>

            <p class="success-message" style="font-size: 1rem; margin-bottom: 16px;">
                Your input helps us continuously improve our services and provide a better experience for all our students.
            </p>

            <div class="action-buttons">
                <a href="/" class="btn-primary">
                    <span class="material-icons">home</span>
                    Go to Homepage
                </a>
                <a href="mailto:{{librarian.user.email}}" class="btn-secondary">
                    <span class="material-icons">email</span>
                    Contact Library
                </a>
            </div>
        </div>
    </div>

    <!-- Confetti Animation -->
    <div class="confetti" id="confetti"></div>

    <script>
        // Create confetti animation
        function createConfetti() {
            const confettiContainer = document.getElementById('confetti');
            const colors = ['#6366f1', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
            
            for (let i = 0; i < 50; i++) {
                const confettiPiece = document.createElement('div');
                confettiPiece.className = 'confetti-piece';
                confettiPiece.style.left = Math.random() * 100 + '%';
                confettiPiece.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confettiPiece.style.animationDelay = Math.random() * 3 + 's';
                confettiPiece.style.animationDuration = (Math.random() * 3 + 2) + 's';
                confettiContainer.appendChild(confettiPiece);
            }

            // Remove confetti after animation
            setTimeout(() => {
                confettiContainer.innerHTML = '';
            }, 6000);
        }

        // Start confetti animation on page load
        window.addEventListener('load', createConfetti);
    </script>
</body>
</html>
