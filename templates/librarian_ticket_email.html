{% extends "email_base.html" %}

{% block email_title %}New Complaint Ticket - Librainian{% endblock %}

{% block email_subject %}New Complaint Ticket #{{ ticket_number }}{% endblock %}

{% block email_description %}Notification about a new complaint ticket that requires attention from the librarian.{% endblock %}

{% block preview_text %}New complaint ticket #{{ ticket_number }} requires your attention. Issue: {{ type }}{% endblock %}

{% block header_icon %}🎫{% endblock %}

{% block email_header_title %}New Complaint Ticket{% endblock %}

{% block email_header_subtitle %}Ticket #{{ ticket_number }} requires your attention{% endblock %}

{% block email_styles %}
<style>
    .ticket-details {
        background: linear-gradient(135deg, #f8f9ff 0%, #e8eaff 100%);
        border-radius: 16px;
        padding: 25px;
        margin: 25px 0;
        border: 2px solid #6366f1;
        position: relative;
        overflow: hidden;
    }

    .ticket-details::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.1), transparent);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(30deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(30deg); }
    }

    .ticket-icon {
        font-size: 48px;
        text-align: center;
        margin-bottom: 20px;
        position: relative;
        z-index: 1;
    }

    .ticket-title {
        font-size: 20px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 20px 0;
        text-align: center;
        position: relative;
        z-index: 1;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 12px 0;
        border-bottom: 1px solid rgba(99, 102, 241, 0.2);
        position: relative;
        z-index: 1;
    }

    .detail-row:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 600;
        color: #6366f1;
        font-size: 14px;
        min-width: 120px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .detail-value {
        font-weight: 500;
        color: #2c3e50;
        font-size: 16px;
        text-align: right;
        flex: 1;
        margin-left: 15px;
        word-break: break-word;
    }

    .priority-section {
        background-color: #fef3c7;
        border: 1px solid #fbbf24;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
        border-left: 4px solid #f59e0b;
    }

    .priority-title {
        font-size: 16px;
        font-weight: 600;
        color: #92400e;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
    }

    .priority-title::before {
        content: "⚠️";
        margin-right: 8px;
        font-size: 18px;
    }

    .priority-text {
        font-size: 14px;
        color: #92400e;
        margin: 0;
        line-height: 1.5;
    }

    @media only screen and (max-width: 600px) {
        .detail-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
        }

        .detail-value {
            text-align: left !important;
            margin-left: 0;
        }

        .ticket-details {
            padding: 20px !important;
            margin: 20px 0 !important;
        }
    }
</style>
{% endblock %}

{% block email_content %}
<h2 class="greeting">Hello {{ librarian }}!</h2>
<p class="message">
    A new complaint ticket has been registered in the system and requires your attention. Please review the details below and take appropriate action.
</p>

<!-- Ticket Details Section -->
<div class="ticket-details">
    <div class="ticket-icon">🎫</div>
    <h3 class="ticket-title">Ticket Details</h3>

    <div class="detail-row">
        <span class="detail-label">Ticket Number</span>
        <span class="detail-value">#{{ ticket_number }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Issue Type</span>
        <span class="detail-value">{{ type }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Subject</span>
        <span class="detail-value">{{ subject }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Description</span>
        <span class="detail-value">{{ description }}</span>
    </div>
</div>

<!-- Priority Notice -->
<div class="priority-section">
    <h4 class="priority-title">Action Required</h4>
    <p class="priority-text">
        Please log into the system to review this ticket and provide a response to the user. Timely resolution helps maintain user satisfaction and system efficiency.
    </p>
</div>

<p class="message">
    Thank you for your prompt attention to this matter. If you need any assistance, please contact the system administrator.
</p>
{% endblock %}