<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">

    <!-- Primary Meta Tags -->
    <title>{{ librarian.library_name }} QR Code | Student Registration | Librainian</title>
    <meta name="description" content="Access the official QR code for {{ librarian.library_name }} located in {{ librarian.librarian_address }}. Scan to register as a student, access library services, and view detailed information. Download the QR code for easy access to this library's resources.">
    <meta name="keywords" content="{{ librarian.library_name }}, QR code registration, library QR code, student registration QR, {{ librarian.library_name }} registration, library in {{ librarian.librarian_address }}, scan QR code library, digital library access, library management system, Librainian app, student library registration, library services access, {{ librarian.library_name }} student portal, library digital pass, contactless library registration, library membership QR, quick library registration, mobile library access, library check-in QR code">
    <meta name="author" content="Librainian">
    <meta name="robots" content="index, follow, max-image-preview:large">
    <meta http-equiv="content-language" content="en">
    <meta name="geo.region" content="IN">
    <link rel="canonical" href="https://www.librainian.com/librarian/qr-code/{{ librarian.slug }}/">

    <!-- Browser/Mobile Configuration -->
    <meta name="theme-color" content="#667eea">
    <meta name="google" content="notranslate">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="{{ librarian.library_name }} QR Code">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Librainian">
    <meta property="og:url" content="https://www.librainian.com/librarian/qr-code/{{ librarian.slug }}/">
    <meta property="og:title" content="{{ librarian.library_name }} QR Code | Student Registration">
    <meta property="og:description" content="Access the official QR code for {{ librarian.library_name }} located in {{ librarian.librarian_address }}. Scan to register as a student and access library services.">
    <meta property="og:image" content="https://www.librainian.com/static/img/qr-code-preview.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="en_US">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@librainian_app">
    <meta name="twitter:creator" content="@librainian_app">
    <meta name="twitter:title" content="{{ librarian.library_name }} QR Code | Student Registration">
    <meta name="twitter:description" content="Access the official QR code for {{ librarian.library_name }}. Scan to register as a student and access library services.">
    <meta name="twitter:image" content="https://www.librainian.com/static/img/qr-code-preview.jpg">

    <!-- Structured Data - QR Code -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "ImageObject",
      "contentUrl": "data:image/png;base64,{{ qr_code }}",
      "name": "{{ librarian.library_name }} Registration QR Code",
      "description": "QR Code for student registration at {{ librarian.library_name }} in {{ librarian.librarian_address }}",
      "encodingFormat": "image/png",
      "uploadDate": "{% now 'c' %}",
      "copyrightHolder": {
        "@type": "Organization",
        "name": "Librainian",
        "url": "https://www.librainian.com/"
      }
    }
    </script>

    <!-- Structured Data - Library -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "{{ librarian.library_name }}",
      "url": "https://www.librainian.com/librarian/library-details/{{ librarian.slug }}/",
      "logo": "https://www.librainian.com/static/img/librainian-logo-black-transparent.png",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "{{ librarian.librarian_address }}",
        "addressCountry": "IN"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "{{ librarian.librarian_phone_num }}",
        "contactType": "customer service"
      },
      "sameAs": [
        "https://www.librainian.com/",
        "https://www.facebook.com/librainian",
        "https://twitter.com/librainian_app"
      ]
    }
    </script>

    <!-- Breadcrumb Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://www.librainian.com/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Libraries",
          "item": "https://www.librainian.com/librarian/library-list/"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "{{ librarian.library_name }}",
          "item": "https://www.librainian.com/librarian/library-details/{{ librarian.slug }}/"
        },
        {
          "@type": "ListItem",
          "position": 4,
          "name": "QR Code",
          "item": "https://www.librainian.com/librarian/qr-code/{{ librarian.slug }}/"
        }
      ]
    }
    </script>

    <!-- Stylesheets -->
    <link rel="apple-touch-icon" href="/static/img/librainian-logo-black-transparent.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="manifest" href="/static/js/manifest.json">
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">

    <style>
        /* Base Styles */
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --text-color: #1e293b;
            --text-light: #6b7280;
            --text-white: #ffffff;
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 10px 20px rgba(0, 0, 0, 0.2);
            --shadow-glass: 0 8px 32px rgba(31, 38, 135, 0.37);
            --border-radius: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            box-sizing: border-box;
        }

        .qr_body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background: var(--bg-gradient);
            color: var(--text-white);
            font-family: 'Comfortaa', -apple-system, BlinkMacSystemFont, sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        /* Header Styles */
        header {
            padding: 2rem 0 1rem;
            position: relative;
            z-index: 10;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .logo-container img {
            height: 50px;
            transition: var(--transition);
        }

        .logo-container img:hover {
            transform: scale(1.05);
        }

        .breadcrumb {
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border: 1px solid var(--glass-border);
            border-radius: 50px;
            padding: 0.75rem 1.5rem;
            margin: 0 auto;
            max-width: fit-content;
            box-shadow: var(--shadow-glass);
        }

        .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            transition: var(--transition);
            font-weight: 500;
        }

        .breadcrumb-item a:hover {
            color: var(--text-white);
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
        }

        .breadcrumb-item.active {
            color: rgba(255, 255, 255, 0.7);
        }

        /* Main Content Styles */
        .qr-code-container {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            flex: 1;
            padding: 2rem 1rem;
            position: relative;
        }

        .qr-card {
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-radius: var(--border-radius);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-glass);
            padding: 2.5rem;
            text-align: center;
            max-width: 450px;
            width: 100%;
            position: relative;
            transition: var(--transition);
            animation: slideUp 0.8s ease-out;
        }

        .qr-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            border-radius: var(--border-radius);
            pointer-events: none;
        }

        .qr-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(31, 38, 135, 0.5);
        }

        .qr-card h1 {
            font-size: 1.6rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-white);
            position: relative;
            z-index: 1;
        }

        .qr-card .subtitle {
            font-size: 0.95rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        .qr-image-container {
            margin: 2rem 0;
            position: relative;
            z-index: 1;
        }

        .qr-card img {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            max-width: 100%;
            height: auto;
            transition: var(--transition);
            background: white;
            padding: 0.75rem;
            box-shadow: var(--shadow-md);
        }

        .qr-card img:hover {
            transform: scale(1.05);
            border-color: rgba(255, 255, 255, 0.5);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
        }

        .qr-card p {
            font-size: 0.95rem;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.6;
            position: relative;
            z-index: 1;
        }

        .qr-card h2 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-white);
            position: relative;
            z-index: 1;
        }

        .qr-card ol {
            text-align: left;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 12px;
            backdrop-filter: blur(8px);
        }

        .qr-card ol li {
            margin-bottom: 0.75rem;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
        }

        /* Button Styles */
        .download-btn {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--text-white);
            border: none;
            border-radius: 50px;
            padding: 0.875rem 2rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--shadow-sm);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            position: relative;
            z-index: 1;
            margin: 0.5rem;
        }

        .download-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
            background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
        }

        .download-btn:active {
            transform: translateY(-1px);
        }

        .download-btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
        }

        .btn-outline-primary {
            color: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(8px);
            transition: var(--transition);
            border-radius: 50px;
            padding: 0.6rem 1.5rem;
            font-weight: 500;
            position: relative;
            z-index: 1;
        }

        .btn-outline-primary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            color: var(--text-white);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 255, 255, 0.2);
        }

        /* Library Info Section */
        .library-info {
            margin-top: 3rem;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .library-info h2 {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-white);
        }

        .library-info p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        /* Footer Styles */
        footer {
            margin-top: auto;
            padding: 2rem 0;
            text-align: center;
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(8px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        footer .container {
            max-width: 800px;
        }

        footer p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        footer a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: var(--transition);
            margin: 0 0.5rem;
        }

        footer a:hover {
            color: var(--text-white);
            text-decoration: underline;
        }

        /* Animations */
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            header {
                padding: 1.5rem 0 1rem;
            }

            .qr-card {
                max-width: 380px;
                padding: 2rem;
            }

            .qr-card h1 {
                font-size: 1.4rem;
            }

            .download-btn {
                padding: 0.75rem 1.5rem;
                font-size: 0.95rem;
            }

            .library-info {
                margin-top: 2rem;
            }
        }

        @media (max-width: 576px) {
            .qr-code-container {
                padding: 1rem 0.5rem;
            }

            .qr-card {
                max-width: 100%;
                margin: 0;
                padding: 1.5rem;
            }

            .qr-card h1 {
                font-size: 1.2rem;
            }

            .qr-card img {
                border-width: 2px;
                padding: 0.5rem;
            }

            .breadcrumb {
                display: none;
            }

            .download-btn {
                width: 100%;
                margin: 0.25rem 0;
            }

            .btn-outline-primary {
                width: 100%;
                margin: 0.25rem 0;
            }
        }

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Focus indicators */
        .download-btn:focus-visible,
        .btn-outline-primary:focus-visible {
            outline: 2px solid var(--text-white);
            outline-offset: 2px;
        }
    </style>
</head>
<body class="qr_body">
    <header class="container">
        <div class="logo-container">
            <a href="/" aria-label="Return to Librainian homepage">
                <img src="/static/img/librainian-logo-white-trans.png"
                     alt="Librainian Logo"
                     class="img-fluid">
            </a>
        </div>
        <nav aria-label="breadcrumb" class="d-none d-md-block">
            <ol class="breadcrumb justify-content-center">
                <li class="breadcrumb-item">
                    <a href="/">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="/librarian/library-list/">
                        <i class="fas fa-building me-1"></i>Libraries
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="/librarian/library-details/{{ librarian.slug }}/">
                        <i class="fas fa-info-circle me-1"></i>{{ librarian.library_name }}
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                    <i class="fas fa-qrcode me-1"></i>QR Code
                </li>
            </ol>
        </nav>
    </header>

    <main class="container qr-code-container">
        <section class="qr-card">
            <h1>
                <i class="fas fa-qrcode me-2"></i>
                QR Code for {{ librarian.library_name }}
            </h1>
            <p class="subtitle">
                Use this QR code to register as a student at {{ librarian.library_name }} in {{ librarian.librarian_address }}
            </p>

            <div class="qr-image-container">
                <img id="qrImage"
                     src="data:image/png;base64,{{ qr_code }}"
                     alt="QR Code for {{ librarian.library_name }} student registration"
                     class="img-fluid"
                     width="280"
                     height="280"
                     loading="lazy">
            </div>

            <div class="instructions-section">
                <h2>
                    <i class="fas fa-mobile-alt me-2"></i>
                    How to use this QR code:
                </h2>
                <ol>
                    <li>
                        <i class="fas fa-camera me-2"></i>
                        Open your camera app or Google Lens
                    </li>
                    <li>
                        <i class="fas fa-crosshairs me-2"></i>
                        Point it at the QR code above
                    </li>
                    <li>
                        <i class="fas fa-hand-pointer me-2"></i>
                        Tap the URL notification that appears
                    </li>
                    <li>
                        <i class="fas fa-user-plus me-2"></i>
                        Complete your student registration
                    </li>
                </ol>
            </div>

            <div class="action-buttons">
                <button class="download-btn"
                        onclick="downloadQR()"
                        aria-label="Download QR Code"
                        type="button">
                    <i class="fas fa-download"></i>
                    Download QR Code
                </button>

                <a href="/librarian/library-details/{{ librarian.slug }}/"
                   class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-1"></i>
                    Back to Library Details
                </a>
            </div>
        </section>

        <section class="library-info">
            <h2>{{ librarian.library_name }}</h2>
            <p>
                <i class="fas fa-map-marker-alt"></i>
                {{ librarian.librarian_address }}
            </p>
            <p>
                <i class="fas fa-phone"></i>
                {{ librarian.librarian_phone_num }}
            </p>
            <p>
                <i class="fas fa-envelope"></i>
                {{ librarian.user.email }}
            </p>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>
                <i class="fas fa-shield-alt me-2"></i>
                &copy; {% now "Y" %} Librainian - The #1 Library Management System. All Rights Reserved.
            </p>
            <div>
                <a href="/privacy-policy/">Privacy Policy</a>
                <a href="/terms-of-service/">Terms of Service</a>
                <a href="/contact/">Contact Us</a>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Enhanced QR Code Script -->
    <script>
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast-notification toast-${type}`;
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
            `;

            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : '#ef4444'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                z-index: 9999;
                animation: slideInRight 0.3s ease-out;
                font-size: 0.9rem;
                max-width: 320px;
                backdrop-filter: blur(16px);
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (toast.parentNode) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // Enhanced download function
        function downloadQR() {
            try {
                const qrImage = document.getElementById('qrImage');
                if (!qrImage || !qrImage.src) {
                    throw new Error('QR code image not found');
                }

                const link = document.createElement('a');
                link.href = qrImage.src;
                link.download = '{{ librarian.library_name|slugify }}_qr_code.png';
                link.style.display = 'none';

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showToast('QR Code downloaded successfully!', 'success');

                // Track download event
                if (typeof gtag === 'function') {
                    gtag('event', 'download_qr_code', {
                        'event_category': 'Conversion',
                        'event_label': '{{ librarian.library_name }} QR Code Download'
                    });
                }
            } catch (error) {
                console.error('Download failed:', error);
                showToast('Download failed. Please try again.', 'error');
            }
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Focus management for accessibility
            const downloadBtn = document.querySelector('.download-btn');
            if (downloadBtn) {
                setTimeout(() => downloadBtn.focus(), 500);
            }

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    if (e.target.classList.contains('download-btn')) {
                        e.preventDefault();
                        downloadQR();
                    }
                }
            });

            // Track QR code view
            if (typeof gtag === 'function') {
                gtag('event', 'view_qr_code', {
                    'event_category': 'Engagement',
                    'event_label': '{{ librarian.library_name }} QR Code',
                    'library_name': '{{ librarian.library_name }}',
                    'library_location': '{{ librarian.librarian_address }}'
                });
            }

            // Add loading animation
            const qrImage = document.getElementById('qrImage');
            if (qrImage) {
                qrImage.addEventListener('load', function() {
                    this.style.animation = 'slideUp 0.6s ease-out';
                });
            }
        });
    </script>
</body>
</html>
