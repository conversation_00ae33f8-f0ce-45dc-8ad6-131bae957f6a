<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>List of Advertisements</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.8.1/font/bootstrap-icons.min.css"
        rel="stylesheet">
       


  
        
        <!-- disable Print Screen for Windows -->
    
          
        
        <!-- disable print screen for mac -->
    
          
    
        <!-- disabling print screen for Linux -->
    
  
    
        <!-- disabling inspection tool -->
    
          
        
    
        <style>
    
            body {
                -webkit-user-select: none; /* Disable text selection */
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
                margin: 0;
            }
            
            .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 20%;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed {
            width: 0;
            overflow: hidden;
            padding: 0;
        }

        .sidebar.collapsed .sidebar-content {
            display: none;
        }

        .sidebar .sidebar-content {
            padding: 1rem;
        }

        .sidebar a {
            text-decoration: none;
        }

        .sidebar a:hover {
            text-decoration: none;
        }

        .content {
            margin-left: 20%;
            transition: margin-left 0.3s ease;
        }

        .content.expanded {
            margin-left: 0;
        }

        .sidebar-toggle {
            position: fixed;
            /* bottom: 0; */
            top: 0;
            left: 0;
            z-index: 1000;
            cursor: pointer;
            border: none;
            /* right: 2rem; */
            /* left: 14rem; */
            /* background-color: aqua; */
        }
        h2{
            margin-top: 30px;
        }
        .card {
            width: 50%;
            /* Ensure cards take full width of their container */
            margin: 20px auto;
            padding: 0;
        }
        .advertisement-line {
            border: 1px solid white;
            width: 100%;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        .card-img-desktop {
            width: 400px;
            height: auto;
            /* Maintain aspect ratio */
            object-fit: cover;
            display: block;
            margin: 0 auto;
        }

        .card-img-mobile {
            width: 100%;
            height: auto;
            /* Maintain aspect ratio */
            object-fit: cover;
            display: none;
        }

        @media (max-width: 767.98px) {
            .card-img-desktop {
                display: none;
            }

            .card-img-mobile {
                display: block;
            }
            .card {
            width: 100%;
            /* Ensure cards take full width of their container */
            margin: 20px auto;
            padding: 0;
        }
        }

        .btn-container {
            text-align: right;
            margin-top: 20px;
        }

        .btn-container .btn {
            margin-left: 10px;
        }
        .btn-primary{
            background-color: #0A6A6D;
        }
        .btn-primary:hover{
            background-color: #0A6A6D;
        }
    </style>
</head>

<body>

    <div class="sidebar bg-dark text-white">
        <div class="sidebar-content">
            <h2 class="heading">Advertisement</h2>
            <hr class="advertisement-line">
            <ul class="list-unstyled mt-5">
                <li class="mb-4"><a href="#" class="text-white"><i class="bi bi-speedometer2"></i> Dashboard</a></li>
                <li class="mb-4"><a href="create-advertisement.html" class="text-white"><i class="bi bi-plus-circle"></i> Create New Advertisement</a></li>
                <li class="mb-4"><a href="list-advertisements.html" class="text-white"><i class="bi bi-list-ul"></i> List of Advertisements</a></li>
                <li class="mb-4"><a href="#" class="text-white"><i class="bi bi-credit-card"></i> Payment History</a></li>
                <li class="mb-4"><a href="#" class="text-white"><i class="bi bi-question-circle"></i> Help</a></li>
            </ul>
        </div>
    </div>

    <div class="content flex-grow-1 p-4">
        <div class="sidebar-toggle">
            <button class="btn btn-primary" onclick="toggleSidebar()"><i class="bi bi-list"></i></button>
        </div>
        <h1>List of Advertisements</h1>

        <!-- Advertisement Cards will be injected here by JavaScript -->
        <div id="advertisements-container"></div>
    </div>


    <!-- Edit Advertisement Modal -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">Edit Advertisement</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <div class="form-group">
                            <label for="editName">Name</label>
                            <input type="text" class="form-control" id="editName" required>
                        </div>
                        <div class="form-group">
                            <label for="editPriority">Priority</label>
                            <input type="text" class="form-control" id="editPriority" required>
                        </div>
                        <div class="form-group">
                            <label for="editAmount">Amount</label>
                            <input type="number" class="form-control" id="editAmount" required>
                        </div>
                        <div class="form-group">
                            <label for="editStartDate">Start Date</label>
                            <input type="date" class="form-control" id="editStartDate" required>
                        </div>
                        <div class="form-group">
                            <label for="editEndDate">End Date</label>
                            <input type="date" class="form-control" id="editEndDate" required>
                        </div>
                        <div class="form-group">
                            <label for="editSubject">Subject</label>
                            <input type="text" class="form-control" id="editSubject" required>
                        </div>
                        <div class="form-group">
                            <label for="editAge">Age</label>
                            <input type="number" class="form-control" id="editAge" required>
                        </div>
                        <div class="form-group">
                            <label for="editGender">Gender</label>
                            <input type="text" class="form-control" id="editGender" required>
                        </div>
                        <div class="form-group">
                            <label for="editLocation">Location</label>
                            <input type="text" class="form-control" id="editLocation" required>
                        </div>
                        <div class="form-group">
                            <label for="editBidding">Bidding</label>
                            <input type="number" class="form-control" id="editBidding" required>
                        </div>
                        <div class="form-group">
                            <label for="editImgDesktop">Desktop Image URL</label>
                            <input type="text" class="form-control" id="editImgDesktop">
                        </div>
                        <div class="form-group">
                            <label for="editImgMobile">Mobile Image URL</label>
                            <input type="text" class="form-control" id="editImgMobile">
                        </div>
                        <div class="form-group">
                            <label for="editImgUploadDesktop">Upload Desktop Image</label>
                            <input type="file" class="form-control-file" id="editImgUploadDesktop">
                        </div>
                        <div class="form-group">
                            <label for="editImgUploadMobile">Upload Mobile Image</label>
                            <input type="file" class="form-control-file" id="editImgUploadMobile">
                        </div>
                        <input type="hidden" id="editAdId">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="saveEdit()">Save changes</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Retrieve data from localStorage or initialize with sample data
        const advertisements = JSON.parse(localStorage.getItem('advertisements')) || [
            { id: 1, name: 'Ad 1', priority: 'High', amount: 500, startDate: '2024-08-21', endDate: '2024-09-21', subject: 'Example Subject', age: 30, gender: 'Male', location: 'Example City', bidding: 100, imgDesktop: 'https://via.placeholder.com/720x360', imgMobile: 'https://via.placeholder.com/360x720' },
            { id: 2, name: 'Ad 2', priority: 'Medium', amount: 300, startDate: '2024-08-22', endDate: '2024-09-22', subject: 'Another Subject', age: 25, gender: 'Female', location: 'Another City', bidding: 50, imgDesktop: 'https://via.placeholder.com/720x360', imgMobile: 'https://via.placeholder.com/360x720' },
            // New cards
            { id: 3, name: 'Ad 3', priority: 'Low', amount: 150, startDate: '2024-08-25', endDate: '2024-09-25', subject: 'New Subject', age: 22, gender: 'Non-Binary', location: 'New City', bidding: 75, imgDesktop: 'https://via.placeholder.com/720x360', imgMobile: 'https://via.placeholder.com/360x720' },
            { id: 4, name: 'Ad 4', priority: 'High', amount: 600, startDate: '2024-08-30', endDate: '2024-09-30', subject: 'Special Subject', age: 35, gender: 'Female', location: 'Special City', bidding: 120, imgDesktop: 'https://via.placeholder.com/720x360', imgMobile: 'https://via.placeholder.com/360x720' }
        ];
    
        // Function to render advertisements
        function renderAdvertisements() {
            const container = document.getElementById('advertisements-container');
            container.innerHTML = '';
            advertisements.forEach(ad => {
                const card = document.createElement('div');
                card.classList.add('card');
                card.dataset.adId = ad.id;
                card.innerHTML =
                    `<img src="${ad.imgDesktop}" class="card-img-top card-img-desktop" alt="Desktop Image">
                    <img src="${ad.imgMobile}" class="card-img-top card-img-mobile" alt="Mobile Image">
                    <div class="card-body">
                        <h5 class="card-title">${ad.name}</h5>
                        <p class="card-text">
                            <strong>Priority:</strong> ${ad.priority}<br>
                            <strong>Amount:</strong> ${ad.amount}<br>
                            <strong>Start Date:</strong> ${ad.startDate}<br>
                            <strong>End Date:</strong> ${ad.endDate}<br>
                            <strong>Subject:</strong> ${ad.subject}<br>
                            <strong>Age:</strong> ${ad.age}<br>
                            <strong>Gender:</strong> ${ad.gender}<br>
                            <strong>Location:</strong> ${ad.location}<br>
                            <strong>Bidding:</strong> ${ad.bidding}
                        </p>
                        <div class="btn-container">
                            <button class="btn btn-warning" onclick="editAd(${ad.id})"><i class="bi bi-pencil"></i> Edit</button>
                            <button class="btn btn-danger" onclick="deleteAd(${ad.id})"><i class="bi bi-trash"></i> Delete</button>
                        </div>
                    </div>`;
                container.appendChild(card);
            });
        }
    
        // Function to toggle sidebar visibility
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const content = document.querySelector('.content');
            sidebar.classList.toggle('collapsed');
            content.classList.toggle('expanded');
        }
    
        // Initial rendering of advertisements
        renderAdvertisements();
    </script>
    
    
    <script>
        // Function to edit an advertisement
        function editAd(id) {
            const ad = advertisements.find(ad => ad.id === id);
            if (ad) {
                document.getElementById('editAdId').value = ad.id;
                document.getElementById('editName').value = ad.name;
                document.getElementById('editPriority').value = ad.priority;
                document.getElementById('editAmount').value = ad.amount;
                document.getElementById('editStartDate').value = ad.startDate;
                document.getElementById('editEndDate').value = ad.endDate;
                document.getElementById('editSubject').value = ad.subject;
                document.getElementById('editAge').value = ad.age;
                document.getElementById('editGender').value = ad.gender;
                document.getElementById('editLocation').value = ad.location;
                document.getElementById('editBidding').value = ad.bidding;
                document.getElementById('editImgDesktop').value = ad.imgDesktop;
                document.getElementById('editImgMobile').value = ad.imgMobile;
                const editModal = new bootstrap.Modal(document.getElementById('editModal'));
                editModal.show();
            }
        }
    
        // Function to save edited advertisement
        function saveEdit() {
            const id = document.getElementById('editAdId').value;
            const ad = advertisements.find(ad => ad.id == id);
            if (ad) {
                ad.name = document.getElementById('editName').value;
                ad.priority = document.getElementById('editPriority').value;
                ad.amount = document.getElementById('editAmount').value;
                ad.startDate = document.getElementById('editStartDate').value;
                ad.endDate = document.getElementById('editEndDate').value;
                ad.subject = document.getElementById('editSubject').value;
                ad.age = document.getElementById('editAge').value;
                ad.gender = document.getElementById('editGender').value;
                ad.location = document.getElementById('editLocation').value;
                ad.bidding = document.getElementById('editBidding').value;
                ad.imgDesktop = document.getElementById('editImgDesktop').value;
                ad.imgMobile = document.getElementById('editImgMobile').value;
    
                // Update localStorage
                localStorage.setItem('advertisements', JSON.stringify(advertisements));
    
                // Close modal and re-render advertisements
                const editModal = bootstrap.Modal.getInstance(document.getElementById('editModal'));
                editModal.hide();
                renderAdvertisements();
            }
        }
    
        // Function to delete an advertisement
        function deleteAd(id) {
            const adIndex = advertisements.findIndex(ad => ad.id === id);
            if (adIndex !== -1) {
                advertisements.splice(adIndex, 1);
    
                // Update localStorage
                localStorage.setItem('advertisements', JSON.stringify(advertisements));
    
                // Re-render advertisements
                renderAdvertisements();
            }
        }
    
        // Event listeners for file uploads
        document.getElementById('editImgUploadDesktop').addEventListener('change', function (event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    document.getElementById('editImgDesktop').value = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    
        document.getElementById('editImgUploadMobile').addEventListener('change', function (event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    document.getElementById('editImgMobile').value = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
    


    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>