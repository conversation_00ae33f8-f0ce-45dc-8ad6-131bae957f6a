<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Payment failed - Librainian membership checkout unsuccessful">

    <title>Payment Failed - Librainian</title>

    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Import master CSS variables from code_temp.html -->
    {% include "code_temp.html" %}

    <style>
        :root {
            /* Failure page specific variable aliases for compatibility */
            --primary-color: var(--primary);
            --primary-dark: var(--primary-dark);
            --secondary-color: var(--secondary);
            --danger-color: var(--danger);
            --danger-dark: var(--danger);
            --success-color: var(--success);
            --warning-color: var(--warning);
            --light-bg: #f8f9fa;
            --dark-text: #2c3e50;
            --border-radius: 16px;
            --box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Comfortaa', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 25%, #dc3545 50%, #c82333 75%, #b02a37 100%);
            min-height: 100vh;
            margin: 0;
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-text);
            line-height: 1.6;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated background elements */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(10px) rotate(-1deg); }
        }

        /* Main container */
        .failure-container {
            max-width: 600px;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 3rem 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            animation: slideInUp 0.8s ease-out;
        }

        .failure-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--danger-color), var(--danger-dark));
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Icon styles */
        .failure-icon {
            font-size: 5rem;
            color: var(--danger-color);
            margin-bottom: 1.5rem;
            animation: pulse 2s ease-in-out infinite;
            text-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Typography */
        .failure-title {
            font-family: 'Comfortaa', sans-serif;
            font-weight: 600;
            font-size: 2.5rem;
            color: var(--danger-color);
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            animation: fadeInDown 0.8s ease-out 0.2s both;
        }

        .failure-subtitle {
            font-size: 1.2rem;
            color: #6c757d;
            margin-bottom: 2rem;
            font-weight: 500;
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        .failure-description {
            font-size: 1.1rem;
            color: var(--dark-text);
            margin-bottom: 2rem;
            line-height: 1.7;
            animation: fadeInUp 0.8s ease-out 0.6s both;
        }

        /* Error details section */
        .error-details {
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
            border: 1px solid rgba(220, 53, 69, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
            animation: fadeInUp 0.8s ease-out 0.8s both;
        }

        .error-code {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: var(--danger-color);
            background: rgba(220, 53, 69, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            display: inline-block;
            margin-bottom: 1rem;
        }

        /* Contact information */
        .contact-section {
            background: linear-gradient(135deg, var(--light-bg) 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
            animation: fadeInUp 0.8s ease-out 1s both;
        }

        .contact-title {
            font-weight: 600;
            color: var(--dark-text);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            color: #6c757d;
            transition: var(--transition);
        }

        .contact-item:hover {
            color: var(--primary-color);
            transform: translateY(-2px);
        }

        .contact-item a {
            color: inherit;
            text-decoration: none;
            font-weight: 500;
        }

        .contact-item a:hover {
            color: var(--primary-color);
        }

        /* Buttons */
        .btn {
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: var(--transition);
            border: none;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(41, 66, 130, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(41, 66, 130, 0.4);
            color: white;
        }

        .btn-outline-secondary {
            background: transparent;
            color: #6c757d;
            border: 2px solid #6c757d;
        }

        .btn-outline-secondary:hover {
            background: #6c757d;
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
        }

        /* Action buttons container */
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 2rem;
            animation: fadeInUp 0.8s ease-out 1.2s both;
        }

        /* Responsive design */
        @media (min-width: 576px) {
            .action-buttons {
                flex-direction: row;
                justify-content: center;
            }
        }

        @media (max-width: 768px) {
            .failure-container {
                margin: 1rem;
                padding: 2rem 1.5rem;
            }

            .failure-title {
                font-size: 2rem;
            }

            .failure-icon {
                font-size: 4rem;
            }

            .contact-info {
                text-align: left;
            }
        }

        @media (max-width: 576px) {
            .failure-container {
                padding: 1.5rem 1rem;
            }

            .failure-title {
                font-size: 1.75rem;
            }

            .failure-subtitle {
                font-size: 1.1rem;
            }

            .failure-description {
                font-size: 1rem;
            }
        }

        /* Animation utilities */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Loading state for buttons */
        .btn.loading {
            position: relative;
            color: transparent;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid transparent;
            border-top-color: currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="failure-container">
        <!-- Failure Icon -->
        <div class="failure-icon animate__animated animate__bounceIn">
            <i class="fas fa-exclamation-triangle"></i>
        </div>

        <!-- Main Content -->
        <h1 class="failure-title">Payment Failed</h1>
        <p class="failure-subtitle">We couldn't process your payment</p>
        <p class="failure-description">
            Don't worry! This happens sometimes. Your payment was not charged, and you can try again safely.
        </p>

        <!-- Error Details -->
        <div class="error-details">
            <div class="error-code">
                <i class="fas fa-code me-2"></i>
                Error Code: PAYMENT_FAILED
            </div>
            <p class="mb-0">
                <strong>Common reasons:</strong> Insufficient funds, expired card, network timeout, or bank security restrictions.
            </p>
        </div>

        <!-- Contact Support Section -->
        <div class="contact-section">
            <h6 class="contact-title">
                <i class="fas fa-headset"></i>
                Need Help?
            </h6>
            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>24/7 Customer Support</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-clock"></i>
                    <span>Average response time: 2 hours</span>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="/membership/plans/" class="btn btn-primary" onclick="handleRetry(this)">
                <i class="fas fa-redo me-2"></i>
                Try Again
            </a>
            <a href="/" class="btn btn-outline-secondary">
                <i class="fas fa-home me-2"></i>
                Go Home
            </a>
        </div>

        <!-- Additional Help -->
        <div class="mt-4 text-muted small">
            <p class="mb-1">
                <i class="fas fa-shield-alt me-1"></i>
                Your payment information is secure and was not stored
            </p>
            <p class="mb-0">
                <i class="fas fa-info-circle me-1"></i>
                Transaction ID: <span id="transaction-id">TXN-{{ request.session.session_key|slice:":8"|upper }}</span>
            </p>
        </div>
    </div>

    <!-- Bootstrap 5.3.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced user experience features
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-focus on retry button for keyboard users
            const retryBtn = document.querySelector('.btn-primary');
            if (retryBtn) {
                retryBtn.focus();
            }

            // Add subtle animation to error details
            const errorDetails = document.querySelector('.error-details');
            if (errorDetails) {
                setTimeout(() => {
                    errorDetails.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        errorDetails.style.transform = 'scale(1)';
                    }, 200);
                }, 1000);
            }

            // Generate a more realistic transaction ID
            const transactionId = document.getElementById('transaction-id');
            if (transactionId) {
                const timestamp = Date.now().toString().slice(-6);
                const random = Math.random().toString(36).substr(2, 4).toUpperCase();
                transactionId.textContent = `TXN-${timestamp}${random}`;
            }

            // Add copy functionality for transaction ID
            transactionId?.addEventListener('click', function() {
                navigator.clipboard.writeText(this.textContent).then(() => {
                    const originalText = this.textContent;
                    this.textContent = 'Copied!';
                    this.style.color = '#28a745';
                    setTimeout(() => {
                        this.textContent = originalText;
                        this.style.color = '';
                    }, 2000);
                });
            });

            // Track page analytics (if analytics is available)
            if (typeof gtag !== 'undefined') {
                gtag('event', 'payment_failed', {
                    'event_category': 'ecommerce',
                    'event_label': 'membership_checkout'
                });
            }

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    if (document.activeElement === retryBtn) {
                        e.preventDefault();
                        retryBtn.click();
                    }
                }

                // ESC to go home
                if (e.key === 'Escape') {
                    window.location.href = '/';
                }
            });
        });

        // Handle retry button with loading state
        function handleRetry(button) {
            button.classList.add('loading');
            button.style.pointerEvents = 'none';

            // Add a small delay to show loading state
            setTimeout(() => {
                window.location.href = button.href;
            }, 500);

            return false;
        }

        // Add smooth scroll behavior for any internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Performance monitoring
        window.addEventListener('load', function() {
            if ('performance' in window) {
                const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                console.log(`Failure page loaded in ${loadTime}ms`);
            }
        });

        // Add error reporting (optional)
        window.addEventListener('error', function(e) {
            console.error('Page error:', e.error);
            // Could send to error tracking service here
        });
    </script>
</body>
</html>
