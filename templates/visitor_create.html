{% extends "base.html" %}

{% block title %}{% if visitor %}Edit Visitor{% else %}Create Visitor{% endif %}{% endblock %}

{% block page_title %}{% if visitor %}Edit Visitor{% else %}Create Visitor{% endif %}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="/visitors/">Visitors</a></li>
<li class="breadcrumb-item active" aria-current="page">{% if visitor %}Edit{% else %}Create{% endif %}</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Visitor Form Glass Theme */
    .visitor-form-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: calc(100vh - var(--topbar-height));
        padding: 2rem;
        position: relative;
    }

    .visitor-form-content::before {
        content: '';
        position: fixed;
        top: var(--topbar-height);
        left: 0;
        width: 100%;
        height: calc(100% - var(--topbar-height));
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    .visitor-form-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset,
            0 2px 4px rgba(255, 255, 255, 0.1) inset;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .visitor-form-card:hover {
        transform: translateY(-2px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset,
            0 2px 4px rgba(255, 255, 255, 0.15) inset;
    }

    .form-header {
        padding: 2rem 2rem 1rem 2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        text-align: center;
    }

    .form-title {
        font-family: 'Plus Jakarta Sans', sans-serif;
        font-weight: 700;
        font-size: 2rem;
        color: white;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .form-subtitle {
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
    }

    .form-body {
        padding: 2rem;
    }

    .modern-form-group {
        margin-bottom: 2rem;
        position: relative;
    }

    .form-label-modern {
        font-size: 0.875rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-icon {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        font-size: 0.75rem;
    }

    .form-control-modern {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1rem 1.25rem;
        color: white;
        font-size: 1rem;
        font-weight: 500;
        width: 100%;
        transition: all 0.3s ease;
    }

    .form-control-modern::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    .form-control-modern:focus {
        outline: none;
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(99, 102, 241, 0.5);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        transform: translateY(-1px);
    }

    .form-control-modern:hover {
        background: rgba(255, 255, 255, 0.12);
        border-color: rgba(255, 255, 255, 0.3);
    }

    /* Checkbox Styling */
    .checkbox-group {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 0.75rem;
    }

    .modern-checkbox {
        position: relative;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        cursor: pointer;
        padding: 0.75rem 1rem;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        transition: all 0.3s ease;
        min-width: 120px;
    }

    .modern-checkbox:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-1px);
    }

    .modern-checkbox input[type="checkbox"] {
        position: absolute;
        opacity: 0;
        cursor: pointer;
    }

    .checkbox-custom {
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.4);
        border-radius: 6px;
        position: relative;
        transition: all 0.3s ease;
        background: transparent;
    }

    .modern-checkbox input[type="checkbox"]:checked + .checkbox-custom {
        background: rgba(99, 102, 241, 0.8);
        border-color: rgba(99, 102, 241, 1);
    }

    .modern-checkbox input[type="checkbox"]:checked + .checkbox-custom::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 12px;
        font-weight: bold;
    }

    .checkbox-label {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
        font-size: 0.875rem;
    }

    /* Textarea Styling */
    .form-control-modern.textarea {
        min-height: 120px;
        resize: vertical;
        font-family: inherit;
    }

    /* Select Styling */
    .form-control-modern.select {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='rgba(255,255,255,0.6)' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 1rem center;
        background-repeat: no-repeat;
        background-size: 1rem;
        padding-right: 3rem;
        cursor: pointer;
    }

    .form-control-modern.select option {
        background: #1f2937;
        color: white;
        padding: 0.5rem;
    }

    /* Button Styling */
    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
        flex-wrap: wrap;
    }

    .btn-modern {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1rem 2rem;
        color: white;
        text-decoration: none;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        min-width: 160px;
        justify-content: center;
    }

    .btn-modern:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .btn-modern.btn-primary {
        background: rgba(99, 102, 241, 0.3);
        border-color: rgba(99, 102, 241, 0.5);
    }

    .btn-modern.btn-success {
        background: rgba(16, 185, 129, 0.3);
        border-color: rgba(16, 185, 129, 0.5);
    }

    .btn-modern.btn-secondary {
        background: rgba(107, 114, 128, 0.3);
        border-color: rgba(107, 114, 128, 0.5);
    }

    /* Form Grid Layout */
    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .form-grid-full {
        grid-column: 1 / -1;
    }

    /* Validation Styling */
    .form-control-modern.is-invalid {
        border-color: rgba(239, 68, 68, 0.5);
        background: rgba(239, 68, 68, 0.1);
    }

    .form-control-modern.is-valid {
        border-color: rgba(16, 185, 129, 0.5);
        background: rgba(16, 185, 129, 0.1);
    }

    .invalid-feedback {
        color: rgba(239, 68, 68, 0.9);
        font-size: 0.875rem;
        margin-top: 0.5rem;
        display: block;
    }

    .valid-feedback {
        color: rgba(16, 185, 129, 0.9);
        font-size: 0.875rem;
        margin-top: 0.5rem;
        display: block;
    }

    /* Mobile Responsive */
    @media (max-width: 991.98px) {
        .visitor-form-content {
            padding: 1rem;
            padding-bottom: calc(1rem + var(--bottom-menu-height));
        }
    }

    @media (max-width: 767.98px) {
        .visitor-form-content {
            padding: 0.75rem;
            padding-bottom: calc(0.75rem + var(--bottom-menu-height));
        }

        .form-header,
        .form-body {
            padding: 1.5rem;
        }

        .form-title {
            font-size: 1.5rem;
        }

        .form-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .checkbox-group {
            flex-direction: column;
            gap: 0.75rem;
        }

        .modern-checkbox {
            min-width: auto;
        }

        .form-actions {
            flex-direction: column;
            gap: 0.75rem;
        }

        .btn-modern {
            width: 100%;
            min-width: auto;
        }
    }

    /* Disable zoom and text selection */
    body {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        touch-action: manipulation;
        -webkit-touch-callout: none;
        -webkit-tap-highlight-color: transparent;
    }

    /* Animation for page load */
    .visitor-form-card {
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Form field animations */
    .modern-form-group {
        animation: fadeInUp 0.6s ease-out forwards;
        opacity: 0;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="visitor-form-content">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-10 col-xl-8">
                <!-- Visitor Form Card -->
                <div class="visitor-form-card">
                    <!-- Form Header -->
                    <div class="form-header">
                        <h1 class="form-title">{% if visitor %}Edit Visitor{% else %}Create Visitor{% endif %}</h1>
                        <p class="form-subtitle">{% if visitor %}Update visitor information{% else %}Add a new visitor to the system{% endif %}</p>
                    </div>

                    <!-- Form Body -->
                    <div class="form-body">
                        <form method="post" id="visitorForm" novalidate>
                            {% csrf_token %}

                            <div class="form-grid">
                                <!-- Full Name -->
                                <div class="modern-form-group">
                                    <label class="form-label-modern">
                                        <div class="form-icon">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        Full Name
                                    </label>
                                    <input type="text"
                                           id="name"
                                           name="name"
                                           class="form-control-modern"
                                           placeholder="Enter visitor's full name"
                                           value="{% if visitor %}{{ visitor.name }}{% endif %}"
                                           required
                                           pattern="[A-Za-z ]+">
                                    <div class="invalid-feedback" id="name-error"></div>
                                </div>

                                <!-- Email -->
                                <div class="modern-form-group">
                                    <label class="form-label-modern">
                                        <div class="form-icon">
                                            <i class="fas fa-envelope"></i>
                                        </div>
                                        Email Address
                                    </label>
                                    <input type="email"
                                           id="email"
                                           name="email"
                                           class="form-control-modern"
                                           placeholder="Enter visitor's email"
                                           value="{% if visitor %}{{ visitor.email }}{% endif %}"
                                           required>
                                    <div class="invalid-feedback" id="email-error"></div>
                                </div>

                                <!-- Mobile Number -->
                                <div class="modern-form-group">
                                    <label class="form-label-modern">
                                        <div class="form-icon">
                                            <i class="fas fa-mobile-alt"></i>
                                        </div>
                                        Mobile Number
                                    </label>
                                    <input type="tel"
                                           id="phone"
                                           name="phone"
                                           class="form-control-modern"
                                           placeholder="Enter 10-digit mobile number"
                                           value="{% if visitor %}{{ visitor.contact }}{% endif %}"
                                           maxlength="10"
                                           pattern="[0-9]{10}"
                                           required>
                                    <div class="invalid-feedback" id="phone-error"></div>
                                </div>

                                <!-- Visit Date -->
                                <div class="modern-form-group">
                                    <label class="form-label-modern">
                                        <div class="form-icon">
                                            <i class="far fa-calendar-alt"></i>
                                        </div>
                                        Visit Date
                                    </label>
                                    <input type="date"
                                           id="visit-date"
                                           name="date"
                                           class="form-control-modern"
                                           value="{% if visitor and visitor.date %}{{ visitor.date|date:'Y-m-d' }}{% endif %}"
                                           required>
                                    <div class="invalid-feedback" id="date-error"></div>
                                </div>

                                <!-- Callback Date -->
                                <div class="modern-form-group">
                                    <label class="form-label-modern">
                                        <div class="form-icon">
                                            <i class="far fa-calendar-check"></i>
                                        </div>
                                        Callback Date
                                    </label>
                                    <input type="date"
                                           id="callback-date"
                                           name="callback"
                                           class="form-control-modern"
                                           value="{% if visitor and visitor.callback %}{{ visitor.callback|date:'Y-m-d' }}{% endif %}">
                                </div>

                                <!-- Status -->
                                <div class="modern-form-group">
                                    <label class="form-label-modern">
                                        <div class="form-icon">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                        Status
                                    </label>
                                    <select id="status"
                                            name="status"
                                            class="form-control-modern select"
                                            required>
                                        <option value="">Select Status</option>
                                        <option value="pending" {% if visitor and visitor.status == "pending" %}selected{% endif %}>Pending</option>
                                        <option value="completed" {% if visitor and visitor.status == "completed" %}selected{% endif %}>Completed</option>
                                        <option value="cancelled" {% if visitor and visitor.status == "cancelled" %}selected{% endif %}>Cancelled</option>
                                    </select>
                                    <div class="invalid-feedback" id="status-error"></div>
                                </div>
                            </div>

                            <!-- Shifts (Full Width) -->
                            <div class="modern-form-group form-grid-full">
                                <label class="form-label-modern">
                                    <div class="form-icon">
                                        <i class="far fa-clock"></i>
                                    </div>
                                    Interested Shifts
                                </label>
                                <div class="checkbox-group">
                                    {% for shift in shifts %}
                                        <label class="modern-checkbox">
                                            <input type="checkbox"
                                                   id="shift{{ shift.id }}"
                                                   name="shifts"
                                                   value="{{ shift.id }}"
                                                   {% if visitor and shift in visitor.shift.all %}checked{% endif %}>
                                            <span class="checkbox-custom"></span>
                                            <span class="checkbox-label">{{ shift.name }}</span>
                                        </label>
                                    {% endfor %}
                                </div>
                            </div>

                            <!-- Notes (Full Width) -->
                            <div class="modern-form-group form-grid-full">
                                <label class="form-label-modern">
                                    <div class="form-icon">
                                        <i class="fas fa-sticky-note"></i>
                                    </div>
                                    Notes
                                </label>
                                <textarea id="notes"
                                          name="note"
                                          class="form-control-modern textarea"
                                          placeholder="Enter any notes or comments about the visitor">{% if visitor %}{{ visitor.notes }}{% endif %}</textarea>
                            </div>

                            <!-- Form Actions -->
                            <div class="form-actions">
                                <button type="submit" class="btn-modern btn-success">
                                    <i class="fas fa-{% if visitor %}save{% else %}plus{% endif %}"></i>
                                    {% if visitor %}Update Visitor{% else %}Create Visitor{% endif %}
                                </button>
                                <a href="/visitors/" class="btn-modern btn-secondary">
                                    <i class="fas fa-arrow-left"></i>
                                    Back to Visitor List
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Visitor Form Page Functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize form animations
        initializeFormAnimations();

        // Initialize form validation
        initializeFormValidation();

        // Removed page load success message
    });

    function initializeFormAnimations() {
        // Add staggered animation to form groups
        const formGroups = document.querySelectorAll('.modern-form-group');
        formGroups.forEach((group, index) => {
            group.style.animationDelay = `${index * 0.1}s`;
        });
    }

    function initializeFormValidation() {
        const form = document.getElementById('visitorForm');
        const nameInput = document.getElementById('name');
        const emailInput = document.getElementById('email');
        const phoneInput = document.getElementById('phone');
        const statusSelect = document.getElementById('status');
        const dateInput = document.getElementById('visit-date');

        // Real-time validation
        nameInput.addEventListener('input', validateName);
        emailInput.addEventListener('input', validateEmail);
        phoneInput.addEventListener('input', validatePhone);
        statusSelect.addEventListener('change', validateStatus);
        dateInput.addEventListener('change', validateDate);

        // Form submission
        form.addEventListener('submit', handleFormSubmit);

        function validateName() {
            const value = nameInput.value.trim();
            const isValid = /^[A-Za-z ]+$/.test(value) && value.length >= 2;

            updateFieldValidation(nameInput, isValid,
                isValid ? 'Valid name' : 'Please enter a valid name (only letters and spaces, minimum 2 characters)');

            // Clean input
            nameInput.value = nameInput.value.replace(/[^A-Za-z ]/g, '');

            return isValid;
        }

        function validateEmail() {
            const value = emailInput.value.trim();
            const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);

            updateFieldValidation(emailInput, isValid,
                isValid ? 'Valid email address' : 'Please enter a valid email address');

            // Remove URL-like patterns
            emailInput.value = emailInput.value.replace(/(?:https?:\/\/|www\.)\S+/gi, '');

            return isValid;
        }

        function validatePhone() {
            const value = phoneInput.value.trim();
            const isValid = /^[0-9]{10}$/.test(value);

            updateFieldValidation(phoneInput, isValid,
                isValid ? 'Valid mobile number' : 'Please enter a valid 10-digit mobile number');

            // Clean input - only numbers, max 10 digits
            phoneInput.value = phoneInput.value.replace(/\D/g, '').slice(0, 10);

            return isValid;
        }

        function validateStatus() {
            const value = statusSelect.value;
            const isValid = value !== '';

            updateFieldValidation(statusSelect, isValid,
                isValid ? 'Status selected' : 'Please select a status');

            return isValid;
        }

        function validateDate() {
            const value = dateInput.value;
            const isValid = value !== '';

            updateFieldValidation(dateInput, isValid,
                isValid ? 'Valid date' : 'Please select a visit date');

            return isValid;
        }

        function updateFieldValidation(field, isValid, message) {
            const errorElement = document.getElementById(field.id + '-error');

            if (isValid) {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
                if (errorElement) {
                    errorElement.textContent = '';
                    errorElement.classList.remove('invalid-feedback');
                    errorElement.classList.add('valid-feedback');
                }
            } else {
                field.classList.remove('is-valid');
                field.classList.add('is-invalid');
                if (errorElement) {
                    errorElement.textContent = message;
                    errorElement.classList.remove('valid-feedback');
                    errorElement.classList.add('invalid-feedback');
                }
            }
        }

        function handleFormSubmit(e) {
            e.preventDefault();

            // Validate all fields
            const isNameValid = validateName();
            const isEmailValid = validateEmail();
            const isPhoneValid = validatePhone();
            const isStatusValid = validateStatus();
            const isDateValid = validateDate();

            const isFormValid = isNameValid && isEmailValid && isPhoneValid && isStatusValid && isDateValid;

            if (isFormValid) {
                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                submitBtn.disabled = true;

                // Submit form
                setTimeout(() => {
                    form.submit();
                }, 500);
            } else {
                // Show error toast
                if (window.modernDashboard) {
                    window.modernDashboard.showToast(
                        'Validation Error',
                        'Please correct the errors in the form before submitting.',
                        'error'
                    );
                } else {
                    alert('Please correct the errors in the form before submitting.');
                }
            }
        }
    }
</script>
{% endblock %}
