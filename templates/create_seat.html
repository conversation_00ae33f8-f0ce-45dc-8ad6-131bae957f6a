{% extends "base.html" %}

{% block title %}Seats - Librainian{% endblock %}

{% block page_title %}Seats{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Seats</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Clean Modern Seat Management */
    .page-content {
        background: var(--gradient-hero);
        min-height: calc(100vh - var(--topbar-height, 60px));
        padding: 1rem;
    }

    .main-container {
        max-width: 1400px;
        margin: 0 auto;
    }



    /* Quick Stats Bar */
    .stats-bar {
        display: flex;
        justify-content: space-around;
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        gap: 1rem;
    }

    .stat-item {
        text-align: center;
        flex: 1;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        display: block;
    }

    .stat-label {
        font-size: 0.875rem;
        color: var(--text-primary);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: 0.25rem;
    }

    .stat-item.total .stat-number { color: #3b82f6; }
    .stat-item.available .stat-number { color: #10b981; }
    .stat-item.occupied .stat-number { color: #f59e0b; }

    /* Control Panel */
    .control-panel {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 2rem;
        margin-bottom: 2rem;
        align-items: start;
    }

    .filters-section {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        padding: 1.5rem;
    }

    .filters-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1rem;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-secondary);
    }

    .filter-control {
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid var(--glass-border);
        border-radius: 8px;
        padding: 0.75rem;
        color: #1f2937;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s ease;
        width: 100%;
    }

    .filter-control:focus {
        outline: none;
        border-color: var(--accent-primary);
        box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
        background: rgba(255, 255, 255, 1);
    }

    .filter-control::placeholder {
        color: #6b7280;
    }

    /* Dark mode adjustments for filter controls */
    body.dark-mode .filter-control {
        background: rgba(31, 41, 55, 0.95);
        color: #f9fafb;
        border-color: rgba(75, 85, 99, 0.5);
    }

    body.dark-mode .filter-control:focus {
        background: rgba(31, 41, 55, 1);
        border-color: var(--accent-primary);
    }

    body.dark-mode .filter-control::placeholder {
        color: #9ca3af;
    }

    /* Dropdown option styling */
    .filter-control option {
        background: rgba(255, 255, 255, 1);
        color: #1f2937;
        padding: 0.5rem;
    }

    body.dark-mode .filter-control option {
        background: rgba(31, 41, 55, 1);
        color: #f9fafb;
    }

    .create-section {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        padding: 1.5rem;
        min-width: 300px;
    }

    /* Seats Display */
    .seats-container {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        padding: 2rem;
    }

    .seats-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--glass-border);
    }

    .seats-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .view-toggle {
        display: flex;
        gap: 0.5rem;
    }

    .toggle-btn {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 6px;
        padding: 0.5rem 1rem;
        color: var(--text-primary);
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .toggle-btn.active {
        background: var(--accent-primary);
        color: white;
        border-color: var(--accent-primary);
    }

    .shift-section {
        margin-bottom: 3rem;
    }

    .shift-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: var(--glass-bg-medium);
        border-radius: 8px;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .shift-info h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .shift-stats {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-top: 0.25rem;
    }

    .seats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    /* Clean Seat Cards */
    .seat-card {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 10px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
    }

    .seat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--glass-shadow-lg);
    }

    .seat-card.available {
        border-left: 4px solid #10b981;
    }

    .seat-card.occupied {
        border-left: 4px solid #f59e0b;
    }

    .seat-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .seat-number {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--text-primary);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
    }

    .seat-display-number {
        font-size: 1.125rem;
        font-weight: 700;
        color: var(--text-primary);
    }

    .seat-actual-number {
        font-size: 0.75rem;
        font-weight: 500;
        color: var(--text-secondary);
        opacity: 0.8;
    }

    .seat-status {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .seat-status.available {
        background: rgba(16, 185, 129, 0.15);
        color: #10b981;
    }

    .seat-status.occupied {
        background: rgba(245, 158, 11, 0.15);
        color: #f59e0b;
    }

    .seat-info {
        margin-bottom: 1.5rem;
        min-height: 60px;
    }

    .seat-info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    .info-label {
        color: var(--text-primary);
        font-weight: 500;
    }

    .info-value {
        color: var(--text-primary);
        font-weight: 600;
        text-align: right;
    }

    .seat-actions {
        display: flex;
        gap: 0.75rem;
        justify-content: center;
    }

    .action-btn {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 6px;
        padding: 0.5rem 1rem;
        color: var(--text-primary);
        text-decoration: none;
        font-size: 0.75rem;
        font-weight: 600;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        flex: 1;
        justify-content: center;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        text-decoration: none;
        color: var(--text-primary);
    }

    .action-btn.clear {
        background: rgba(16, 185, 129, 0.1);
        border-color: rgba(16, 185, 129, 0.3);
        color: #10b981;
    }

    .action-btn.clear:hover {
        background: rgba(16, 185, 129, 0.2);
        color: #10b981;
    }

    .action-btn.edit {
        background: rgba(59, 130, 246, 0.1);
        border-color: rgba(59, 130, 246, 0.3);
        color: #3b82f6;
    }

    .action-btn.edit:hover {
        background: rgba(59, 130, 246, 0.2);
        color: #3b82f6;
    }

    .action-btn.delete {
        background: rgba(239, 68, 68, 0.1);
        border-color: rgba(239, 68, 68, 0.3);
        color: #ef4444;
    }

    .action-btn.delete:hover {
        background: rgba(239, 68, 68, 0.2);
        color: #ef4444;
    }

    /* Create Seat Form */
    .form-card {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.875rem;
    }

    .form-control {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        width: 100%;
        color: var(--text-primary);
    }

    .form-control:focus {
        outline: none;
        border-color: var(--accent-primary);
        box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
    }

    .form-select {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        width: 100%;
        cursor: pointer;
        color: var(--text-primary);
    }

    .form-select:focus {
        outline: none;
        border-color: var(--accent-primary);
        box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
    }

    .btn-primary {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border: none;
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        color: white;
        font-weight: 600;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        justify-content: center;
        cursor: pointer;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        color: white;
    }

    .btn-secondary {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        color: var(--text-primary);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: var(--glass-shadow);
        margin-bottom: 1rem;
    }

    .btn-secondary:hover {
        color: var(--text-primary);
        transform: translateY(-2px);
        box-shadow: var(--glass-shadow-lg);
        text-decoration: none;
    }

    .toggle-btn {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 8px;
        padding: 0.5rem 1rem;
        color: var(--text-primary);
        font-weight: 600;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        cursor: pointer;
        margin-bottom: 1rem;
    }

    .toggle-btn:hover {
        background: var(--glass-bg-medium);
        transform: translateY(-1px);
    }

    .toggle-btn.active {
        background: rgba(99, 102, 241, 0.2);
        border-color: rgba(99, 102, 241, 0.3);
        color: var(--accent-primary);
    }

    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .input-group {
        position: relative;
    }

    .input-group-text {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-muted);
        font-size: 0.875rem;
        z-index: 2;
        background: none;
        border: none;
        pointer-events: none;
    }

    .input-group .form-control {
        padding-left: 2rem;
    }

    .help-text {
        font-size: 0.75rem;
        color: var(--text-muted);
        margin-top: 0.25rem;
        line-height: 1.3;
    }

    .no-seats-message {
        text-align: center;
        padding: 3rem 1rem;
        color: var(--text-muted);
    }

    .no-seats-message i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .no-seats-message h3 {
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
        color: var(--text-secondary);
    }

    .no-seats-message p {
        font-size: 0.875rem;
        margin: 0;
    }

    .no-seats-message {
        text-align: center;
        padding: 4rem 2rem;
        color: var(--text-muted);
    }

    .no-seats-message i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .no-seats-message h3 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        color: var(--text-secondary);
    }

    .no-seats-message p {
        font-size: 1rem;
        margin: 0;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .control-panel {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .create-section {
            min-width: auto;
        }

        .filters-grid {
            grid-template-columns: 1fr;
        }

        .stats-bar {
            flex-direction: column;
            gap: 1rem;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            text-align: left;
        }

        /* Mobile Cards - Remove container and fix flex */
        .seats-grid {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            padding: 0;
        }

        .seat-card {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            margin: 0;
            width: 100%;
            box-sizing: border-box;
        }

        .seat-actions {
            display: flex;
            flex-direction: row;
            gap: 0.5rem;
            flex-wrap: wrap;
            justify-content: flex-start;
        }

        .action-btn {
            flex: 0 0 auto;
            min-width: 70px;
            padding: 0.5rem 0.75rem;
            font-size: 0.8rem;
        }
    }

    @media (max-width: 575.98px) {
        .page-content {
            padding: 0.5rem;
            padding-bottom: calc(0.5rem + var(--bottom-menu-height, 80px));
        }

        .main-container {
            padding: 0;
        }

        .filters-section,
        .create-section,
        .seats-container {
            padding: 1rem;
        }

        /* Enhanced mobile card layout */
        .seats-grid {
            gap: 0.75rem;
            padding: 0 0.5rem;
        }

        .seat-card {
            padding: 0.875rem;
            border-radius: 8px;
        }

        .seat-actions {
            gap: 0.375rem;
        }

        .action-btn {
            padding: 0.375rem 0.625rem;
            font-size: 0.75rem;
            min-width: 60px;
        }

        .seat-info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.375rem;
            font-size: 0.8rem;
        }

        .info-label {
            color: var(--text-muted);
            font-weight: 500;
            flex-shrink: 0;
        }

        .info-value {
            color: var(--text-primary);
            font-weight: 600;
            text-align: right;
            margin-left: 0.5rem;
        }
    }

    /* Animation */
    .seat-card {
        animation: fadeInUp 0.3s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* CSS Variables */
    :root {
        --glass-bg-medium: rgba(255, 255, 255, 0.08);
        --glass-shadow-lg: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    body.dark-mode {
        --glass-bg-medium: rgba(255, 255, 255, 0.05);
        --glass-shadow-lg: 0 20px 40px rgba(0, 0, 0, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="page-content">
    <div class="main-container">


        <!-- Quick Stats -->
        <div class="stats-bar">
            <div class="stat-item total">
                <span class="stat-number" id="totalSeats">{{ seat_data|length }}</span>
                <div class="stat-label">Total Seats</div>
            </div>
            <div class="stat-item available">
                <span class="stat-number" id="availableSeats">0</span>
                <div class="stat-label">Available</div>
            </div>
            <div class="stat-item occupied">
                <span class="stat-number" id="occupiedSeats">0</span>
                <div class="stat-label">Occupied</div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="control-panel">
            <!-- Filters -->
            <div class="filters-section">
                <div class="filters-title">
                    <i class="fas fa-filter"></i>
                    Filters & Search
                </div>
                <div class="filters-grid">
                    <div class="filter-group">
                        <label class="filter-label">Status</label>
                        <select id="statusFilter" class="filter-control">
                            <option value="all">All Seats</option>
                            <option value="available">Available</option>
                            <option value="occupied">Occupied</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">Shift</label>
                        <select id="shiftFilter" class="filter-control">
                            <option value="all">All Shifts</option>
                            {% for shift in shifts %}
                                <option value="{{ shift.id }}">{{ shift.shift_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">Prefix</label>
                        <select id="prefixFilter" class="filter-control">
                            <option value="all">All Prefixes</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">Search</label>
                        <input type="text" id="searchSeats" class="filter-control" placeholder="Seat number or student name...">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">&nbsp;</label>
                        <button type="button" id="clearFilters" class="btn-secondary" style="width: 100%;">
                            <i class="fas fa-times"></i> Clear Filters
                        </button>
                    </div>
                </div>
            </div>

            <!-- Create Form -->
            <div class="create-section">
                <div class="filters-title">
                    <i class="fas fa-plus-circle"></i>
                    Create Seats
                </div>

                <!-- Information Alert -->
                <div class="alert alert-info mb-3" style="background: var(--glass-bg); border: 1px solid rgba(59, 130, 246, 0.3); border-radius: 8px; padding: 1rem;">
                    <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                        <i class="fas fa-info-circle" style="color: #3b82f6; margin-top: 0.125rem; font-size: 1.125rem;"></i>
                        <div>
                            <strong style="color: var(--text-primary); display: block; margin-bottom: 0.5rem;">Seat Creation Logic</strong>
                            <p style="color: var(--text-primary); margin: 0; font-size: 0.875rem; line-height: 1.5;">
                                Creating seats = Number of seats per shift. For example, entering <strong>5 seats</strong> with <strong>2 shifts</strong> will result in <strong>10 total seat entries</strong> (5 per shift). Enter only the number of physically available seats (not multiplied by shifts).
                            </p>
                        </div>
                    </div>
                </div>

                <form method="post" id="seatForm">
                    {% csrf_token %}

                    <div class="filter-group">
                        <label class="filter-label">Count</label>
                        <input type="number" name="total_seats" id="total_seats" class="filter-control"
                               min="1" max="999" required placeholder="Number of seats">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">Prefix</label>
                        <input type="text" name="prefix" id="prefix" class="filter-control"
                               maxlength="10" placeholder="e.g., A, B">
                    </div>

                    <div class="filter-group">
                        <label class="filter-label">&nbsp;</label>
                        <button type="submit" class="btn-primary" id="createSeatBtn">
                            <i class="fas fa-plus"></i> Create Seats
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Seats Display -->
        <div class="seats-container">
            <div class="seats-header">
                <h2 class="seats-title">Seat Overview</h2>
                <div class="view-toggle">
                    <button class="toggle-btn active" data-view="grid">
                        <i class="fas fa-th"></i> Grid
                    </button>
                    <button class="toggle-btn" data-view="list">
                        <i class="fas fa-list"></i> List
                    </button>
                </div>
            </div>

            <div id="seatsContainer">
                {% if seat_data %}
                    {% regroup seat_data by shift as shift_groups %}
                    {% for shift_group in shift_groups %}
                        <div class="shift-section" data-shift="{{ shift_group.grouper.id }}">
                            <div class="shift-header">
                                <div class="shift-info">
                                    <h3>{{ shift_group.grouper.shift_name }}</h3>
                                    <div class="shift-stats" id="shift-{{ shift_group.grouper.id }}-stats">
                                        {{ shift_group.list|length }} seats
                                    </div>
                                </div>
                            </div>
                            <div class="seats-grid">
                                {% for seat in shift_group.list %}
                                    <div class="seat-card {% if seat.student_name %}occupied{% else %}available{% endif %}"
                                         data-seat-id="{{ seat.id }}"
                                         data-status="{% if seat.student_name %}occupied{% else %}available{% endif %}"
                                         data-shift="{{ shift_group.grouper.id }}"
                                         data-search-text="{{ seat.seat_number }} {% if seat.student_name %}{{ seat.student_name }}{% endif %}">

                                        <div class="seat-top">
                                            <div class="seat-number">
                                                <span class="seat-display-number">Seat {{ forloop.counter }}</span>
                                                <span class="seat-actual-number">({{ seat.seat_number }})</span>
                                            </div>
                                            <div class="seat-status {% if seat.student_name %}occupied{% else %}available{% endif %}">
                                                {% if seat.student_name %}Occupied{% else %}Available{% endif %}
                                            </div>
                                        </div>

                                        <div class="seat-info">
                                            <div class="seat-info-row">
                                                <span class="info-label">Seat ID:</span>
                                                <span class="info-value">#{{ seat.id }}</span>
                                            </div>
                                            <div class="seat-info-row">
                                                <span class="info-label">Name:</span>
                                                <span class="info-value">{{ seat.seat_number }}</span>
                                            </div>
                                            {% if seat.student_name %}
                                                <div class="seat-info-row">
                                                    <span class="info-label">Allotted to:</span>
                                                    <span class="info-value">{{ seat.student_name }}</span>
                                                </div>
                                                {% if seat.booking_date %}
                                                    <div class="seat-info-row">
                                                        <span class="info-label">Booked:</span>
                                                        <span class="info-value">{{ seat.booking_date|date:"M d" }}</span>
                                                    </div>
                                                {% endif %}
                                                {% if seat.expire_date %}
                                                    <div class="seat-info-row">
                                                        <span class="info-label">Expires:</span>
                                                        <span class="info-value">{{ seat.expire_date|date:"M d" }}</span>
                                                    </div>
                                                {% endif %}
                                            {% else %}
                                                <div class="seat-info-row">
                                                    <span class="info-label">Allotted to:</span>
                                                    <span class="info-value">None</span>
                                                </div>
                                            {% endif %}
                                        </div>

                                        <div class="seat-actions">
                                            {% if seat.student_name %}
                                                <button type="button" class="action-btn clear" onclick="clearSeat('{{ seat.id }}')">
                                                    <i class="fas fa-broom"></i> Clear
                                                </button>
                                            {% endif %}
                                            <a href="/{{ role }}/update-seat/{{ seat.id }}/" class="action-btn edit">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <button type="button" class="action-btn delete" onclick="deleteSeat('{{ seat.id }}')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="no-seats-message">
                        <i class="fas fa-chair"></i>
                        <h3>No Seats Created Yet</h3>
                        <p>Use the form above to create your first seats.</p>
                    </div>
                {% endif %}
            </div>
        </div>

            <!-- Messages -->
            {% if messages %}
                <div class="mt-3">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="fas fa-info-circle me-2"></i>{{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Clean Seat Management System
    document.addEventListener('DOMContentLoaded', function() {
        initializeFilters();
        initializeViewToggle();
        updateStatistics();
        initializeForm();
    });

    // Filter functionality
    function initializeFilters() {
        const statusFilter = document.getElementById('statusFilter');
        const shiftFilter = document.getElementById('shiftFilter');
        const prefixFilter = document.getElementById('prefixFilter');
        const searchInput = document.getElementById('searchSeats');
        const clearFiltersBtn = document.getElementById('clearFilters');

        statusFilter.addEventListener('change', applyFilters);
        shiftFilter.addEventListener('change', applyFilters);
        prefixFilter.addEventListener('change', applyFilters);
        searchInput.addEventListener('input', applyFilters);
        clearFiltersBtn.addEventListener('click', clearAllFilters);

        // Populate prefix filter options
        populatePrefixFilter();
    }

    function clearAllFilters() {
        document.getElementById('statusFilter').value = 'all';
        document.getElementById('shiftFilter').value = 'all';
        document.getElementById('prefixFilter').value = 'all';
        document.getElementById('searchSeats').value = '';
        applyFilters();
    }

    function populatePrefixFilter() {
        const prefixFilter = document.getElementById('prefixFilter');
        const seatCards = document.querySelectorAll('.seat-card');
        const prefixes = new Set();

        seatCards.forEach(card => {
            const seatNumber = card.dataset.searchText.split(' ')[0]; // Get seat number
            const prefix = seatNumber.split('-')[0]; // Extract prefix before the dash
            if (prefix && prefix !== seatNumber) {
                prefixes.add(prefix);
            }
        });

        // Clear existing options except "All Prefixes"
        prefixFilter.innerHTML = '<option value="all">All Prefixes</option>';

        // Add prefix options
        Array.from(prefixes).sort().forEach(prefix => {
            const option = document.createElement('option');
            option.value = prefix;
            option.textContent = prefix;
            prefixFilter.appendChild(option);
        });
    }

    function applyFilters() {
        const statusFilter = document.getElementById('statusFilter').value;
        const shiftFilter = document.getElementById('shiftFilter').value;
        const prefixFilter = document.getElementById('prefixFilter').value;
        const searchTerm = document.getElementById('searchSeats').value.toLowerCase();

        const seatCards = document.querySelectorAll('.seat-card');
        const shiftSections = document.querySelectorAll('.shift-section');

        seatCards.forEach(card => {
            let show = true;

            // Status filter
            if (statusFilter !== 'all' && card.dataset.status !== statusFilter) {
                show = false;
            }

            // Shift filter
            if (shiftFilter !== 'all' && card.dataset.shift !== shiftFilter) {
                show = false;
            }

            // Prefix filter
            if (prefixFilter !== 'all') {
                const seatNumber = card.dataset.searchText.split(' ')[0]; // Get seat number
                const prefix = seatNumber.split('-')[0]; // Extract prefix before the dash
                if (prefix !== prefixFilter) {
                    show = false;
                }
            }

            // Search filter
            if (searchTerm && !card.dataset.searchText.toLowerCase().includes(searchTerm)) {
                show = false;
            }

            card.style.display = show ? 'block' : 'none';
        });

        // Hide empty shift sections
        shiftSections.forEach(section => {
            const visibleCards = section.querySelectorAll('.seat-card:not([style*="display: none"])');
            section.style.display = visibleCards.length > 0 ? 'block' : 'none';
        });

        updateStatistics();
    }

    // View toggle functionality
    function initializeViewToggle() {
        const toggleBtns = document.querySelectorAll('.toggle-btn');

        toggleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                toggleBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                const view = this.dataset.view;
                const seatsGrid = document.querySelectorAll('.seats-grid');

                if (view === 'list') {
                    seatsGrid.forEach(grid => {
                        grid.style.gridTemplateColumns = '1fr';
                    });
                } else {
                    seatsGrid.forEach(grid => {
                        grid.style.gridTemplateColumns = 'repeat(auto-fill, minmax(250px, 1fr))';
                    });
                }
            });
        });
    }

    // Statistics update
    function updateStatistics() {
        const visibleCards = document.querySelectorAll('.seat-card:not([style*="display: none"])');

        let total = 0, available = 0, occupied = 0;

        visibleCards.forEach(card => {
            total++;
            const status = card.dataset.status;
            if (status === 'available') available++;
            else if (status === 'occupied') occupied++;
        });

        document.getElementById('totalSeats').textContent = total;
        document.getElementById('availableSeats').textContent = available;
        document.getElementById('occupiedSeats').textContent = occupied;

        // Update shift statistics
        document.querySelectorAll('.shift-section').forEach(section => {
            const shiftId = section.dataset.shift;
            const shiftCards = section.querySelectorAll('.seat-card:not([style*="display: none"])');
            let shiftAvailable = 0, shiftOccupied = 0;

            shiftCards.forEach(card => {
                if (card.dataset.status === 'available') shiftAvailable++;
                else if (card.dataset.status === 'occupied') shiftOccupied++;
            });

            const statsElement = document.getElementById(`shift-${shiftId}-stats`);
            if (statsElement) {
                statsElement.textContent = `${shiftCards.length} seats • ${shiftAvailable} available • ${shiftOccupied} occupied`;
            }
        });
    }

    // Form validation and button loading state
    function initializeForm() {
        const form = document.getElementById('seatForm');
        const createBtn = document.getElementById('createSeatBtn');

        if (form && createBtn) {
            form.addEventListener('submit', function(e) {
                const seatCount = parseInt(document.getElementById('total_seats').value);

                if (seatCount > 50) {
                    if (!confirm(`Create ${seatCount} seats? This is a large number.`)) {
                        e.preventDefault();
                        return false;
                    }
                }

                // Show loading state
                const originalText = createBtn.innerHTML;
                createBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
                createBtn.disabled = true;

                // Re-enable button after 10 seconds as fallback (in case of errors)
                setTimeout(() => {
                    createBtn.innerHTML = originalText;
                    createBtn.disabled = false;
                }, 10000);
            });
        }
    }

    // Seat management functions
    function clearSeat(seatId) {
        if (confirm('Clear this seat? It will become available for new bookings.')) {
            window.location.href = `/{{ role }}/cancel-seat/${seatId}/`;
        }
    }

    function deleteSeat(seatId) {
        if (confirm('Permanently delete this seat? This cannot be undone.')) {
            window.location.href = `/{{ role }}/delete-seat/${seatId}/`;
        }
    }
</script>
{% endblock %}
