{% extends "base.html" %}

{% block title %}Student List - Librainian{% endblock %}

{% block extra_css %}
<!-- DataTables CSS for Bootstrap 5 -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">



    <style>
        /* Template-specific styles - CSS variables inherited from base.html */

        h1, h2, h3, h4, h5, h6, p, ul, li, strong, em,
        b, s, small, span {
            font-family: 'Comfortaa', sans-serif !important;
        }

        /* Glass Design Components */
        .glass-container {
            min-height: 100vh;
            padding: 1rem;
            max-width: 100%;
            overflow-x: hidden;
        }

        /* Desktop specific - remove extra padding */
        @media (min-width: 768px) {
            .glass-container {
                padding: 1rem;
            }
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .glass-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            text-align: center;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .glass-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255,255,255,0.1);
            transform: rotate(-45deg);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) rotate(-45deg); }
            50% { transform: translateX(100%) rotate(-45deg); }
        }

        .glass-header h3 {
            margin: 0;
            font-weight: 700;
            font-size: 1.75rem;
            position: relative;
            z-index: 1;
        }

        .glass-body {
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }


        /* DataTable Styling */
        .table {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 16px;
            overflow: hidden;
            border: none;
        }

        .table thead th {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            border: none;
            font-weight: 600;
            padding: 1rem;
            text-align: center;
        }

        .table tbody td {
            border: none;
            padding: 1rem;
            vertical-align: middle;
            text-align: center;
            background: rgba(255, 255, 255, 0.8);
        }

        .table tbody tr:nth-child(even) td {
            background: rgba(255, 255, 255, 0.6);
        }

        .table tbody tr:hover td {
            background: rgba(99, 102, 241, 0.1);
        }

        /* Profile Image */
        .profile-img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 50%;
            border: 3px solid var(--primary);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        /* Action Buttons */
        .btn-view {
            background: linear-gradient(135deg, var(--info), #2563eb);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-view:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(59, 130, 246, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-edit {
            background: linear-gradient(135deg, var(--warning), #d97706);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-edit:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(245, 158, 11, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-delete {
            background: linear-gradient(135deg, var(--danger), #dc2626);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-delete:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(239, 68, 68, 0.4);
            color: white;
            text-decoration: none;
        }



        /* DataTables Custom Styling */
        .dataTables_wrapper {
            padding: 0;
        }

        .dataTables_length,
        .dataTables_filter {
            margin-bottom: 1rem;
        }

        .dataTables_length select,
        .dataTables_filter input {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 0.5rem;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .dataTables_length select:focus,
        .dataTables_filter input:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
            outline: none;
        }

        .dataTables_info,
        .dataTables_paginate {
            margin-top: 1rem;
        }

        /* GLOBAL DataTables Pagination Override - Apply to ALL DataTables */
        .dataTables_paginate {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            gap: 0.25rem !important;
            margin-top: 1.5rem !important;
        }

        /* GLOBAL Bootstrap Pagination Override - Target ALL possible selectors */
        .dataTables_wrapper .dataTables_paginate .paginate_button,
        .dataTables_wrapper .dataTables_paginate .paginate_button:link,
        .dataTables_wrapper .dataTables_paginate .paginate_button:visited,
        .dataTables_wrapper .dataTables_paginate .paginate_button:active,
        .dataTables_wrapper .dataTables_paginate .paginate_button:focus,
        .dataTables_paginate .paginate_button,
        .dataTables_paginate .paginate_button:link,
        .dataTables_paginate .paginate_button:visited,
        .dataTables_paginate .paginate_button:active,
        .dataTables_paginate .paginate_button:focus,
        .paginate_button,
        .paginate_button:link,
        .paginate_button:visited,
        .paginate_button:active,
        .paginate_button:focus,
        a.paginate_button,
        a.paginate_button:link,
        a.paginate_button:visited,
        a.paginate_button:active,
        a.paginate_button:focus {
            background: transparent !important;
            background-color: transparent !important;
            background-image: none !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: 8px !important;
            margin: 0 2px !important;
            padding: 0.5rem 0.75rem !important;
            color: white !important;
            font-weight: 500 !important;
            font-size: 0.875rem !important;
            text-decoration: none !important;
            transition: all 0.2s ease !important;
            box-shadow: none !important;
            min-width: 40px !important;
            text-align: center !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            outline: none !important;
            cursor: pointer !important;
        }

        /* GLOBAL Hover States - Override Bootstrap */
        .dataTables_wrapper .dataTables_paginate .paginate_button:hover,
        .dataTables_paginate .paginate_button:hover,
        .paginate_button:hover,
        a.paginate_button:hover {
            background: rgba(255, 255, 255, 0.1) !important;
            background-color: rgba(255, 255, 255, 0.1) !important;
            background-image: none !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
            color: white !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1) !important;
            text-decoration: none !important;
        }

        /* GLOBAL Current/Active States - Override Bootstrap */
        .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        .dataTables_paginate .paginate_button.current,
        .paginate_button.current,
        a.paginate_button.current {
            background: rgba(99, 102, 241, 0.3) !important;
            background-color: rgba(99, 102, 241, 0.3) !important;
            background-image: none !important;
            border-color: rgba(99, 102, 241, 0.6) !important;
            color: white !important;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2) !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover,
        .dataTables_paginate .paginate_button.current:hover,
        .paginate_button.current:hover,
        a.paginate_button.current:hover {
            background: rgba(99, 102, 241, 0.4) !important;
            background-color: rgba(99, 102, 241, 0.4) !important;
            background-image: none !important;
            border-color: rgba(99, 102, 241, 0.7) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.3) !important;
            color: white !important;
            text-decoration: none !important;
        }

        /* GLOBAL Disabled States - Override Bootstrap */
        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
        .dataTables_paginate .paginate_button.disabled,
        .paginate_button.disabled,
        a.paginate_button.disabled {
            background: transparent !important;
            background-color: transparent !important;
            background-image: none !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            color: rgba(255, 255, 255, 0.4) !important;
            cursor: not-allowed !important;
            transform: none !important;
            box-shadow: none !important;
            opacity: 0.5 !important;
            pointer-events: none !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover,
        .dataTables_paginate .paginate_button.disabled:hover,
        .paginate_button.disabled:hover,
        a.paginate_button.disabled:hover {
            background: transparent !important;
            background-color: transparent !important;
            background-image: none !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            color: rgba(255, 255, 255, 0.4) !important;
            transform: none !important;
            box-shadow: none !important;
            opacity: 0.5 !important;
            text-decoration: none !important;
        }

        /* Previous and Next button styling */
        .paginate_button.previous,
        .paginate_button.next {
            min-width: 70px !important;
            font-weight: 600 !important;
        }

        /* GLOBAL Dark mode adjustments - Complete Bootstrap Override */
        body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button,
        body.dark-mode .dataTables_paginate .paginate_button,
        body.dark-mode .paginate_button,
        body.dark-mode a.paginate_button {
            background: transparent !important;
            background-color: transparent !important;
            background-image: none !important;
            border-color: rgba(255, 255, 255, 0.25) !important;
            color: rgba(255, 255, 255, 0.9) !important;
        }

        body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button:hover,
        body.dark-mode .dataTables_paginate .paginate_button:hover,
        body.dark-mode .paginate_button:hover,
        body.dark-mode a.paginate_button:hover {
            background: rgba(255, 255, 255, 0.08) !important;
            background-color: rgba(255, 255, 255, 0.08) !important;
            background-image: none !important;
            border-color: rgba(255, 255, 255, 0.4) !important;
            color: white !important;
        }

        body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        body.dark-mode .dataTables_paginate .paginate_button.current,
        body.dark-mode .paginate_button.current,
        body.dark-mode a.paginate_button.current {
            background: rgba(99, 102, 241, 0.25) !important;
            background-color: rgba(99, 102, 241, 0.25) !important;
            background-image: none !important;
            border-color: rgba(99, 102, 241, 0.5) !important;
            color: white !important;
        }

        body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
        body.dark-mode .dataTables_paginate .paginate_button.disabled,
        body.dark-mode .paginate_button.disabled,
        body.dark-mode a.paginate_button.disabled {
            background: transparent !important;
            background-color: transparent !important;
            background-image: none !important;
            border-color: rgba(255, 255, 255, 0.15) !important;
            color: rgba(255, 255, 255, 0.3) !important;
            opacity: 0.4 !important;
        }

        /* Responsive pagination adjustments */
        @media (max-width: 1200px) {
            .paginate_button {
                padding: 0.45rem 0.65rem !important;
                font-size: 0.85rem !important;
                min-width: 38px !important;
            }

            .paginate_button.previous,
            .paginate_button.next {
                min-width: 65px !important;
            }
        }

        @media (max-width: 992px) {
            .dataTables_paginate {
                gap: 0.2rem;
            }

            .paginate_button {
                padding: 0.4rem 0.6rem !important;
                font-size: 0.8rem !important;
                min-width: 36px !important;
                margin: 0 1px !important;
            }

            .paginate_button.previous,
            .paginate_button.next {
                min-width: 60px !important;
            }
        }

        @media (max-width: 768px) {
            /* Hide desktop pagination on mobile since we have custom mobile pagination */
            .dataTables_paginate {
                display: none !important;
            }
        }

        /* Action Buttons Container */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }


        /* Mobile Responsive */
        @media (max-width: 767.98px) {
            .glass-container {
                padding: 0.5rem !important;
                min-height: calc(100vh - 120px);
            }

            .glass-card {
                border-radius: 16px;
                margin-bottom: 0.5rem;
            }

            .glass-body {
                padding: 1rem;
            }

            .glass-header {
                padding: 1rem;
            }

            .glass-header h3,
            .glass-header h4 {
                font-size: 1.2rem;
            }



            /* Mobile card improvements */
            .mobile-student-cards {
                gap: 0.75rem;
                padding: 0;
            }

            .mobile-student-card {
                padding: 0.75rem;
                border-radius: 12px;
                margin-bottom: 0.5rem;
            }

            .mobile-student-header {
                margin-bottom: 0.75rem;
                padding-bottom: 0.5rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .student-avatar-mobile {
                width: 45px;
                height: 45px;
            }

            .student-name-mobile {
                font-size: 0.9rem;
            }

            .student-id-mobile {
                font-size: 0.75rem;
            }

            .mobile-field {
                margin-bottom: 0.5rem;
            }

            .mobile-field label {
                font-size: 0.7rem;
                margin-bottom: 0.2rem;
            }

            .mobile-value {
                font-size: 0.8rem;
            }

            .contact-item-mobile {
                font-size: 0.8rem;
                padding: 0.4rem;
            }

            .mobile-actions .btn-view,
            .mobile-actions .btn-edit,
            .mobile-actions .btn-delete {
                font-size: 0.75rem;
                padding: 0.4rem 0.6rem;
                min-width: 70px;
            }
        }

        /* Extra small screens */
        @media (max-width: 575.98px) {
            .glass-container {
                padding: 0.25rem !important;
            }

            .glass-body {
                padding: 0.75rem;
            }

            .glass-header {
                padding: 0.75rem;
            }

            .glass-header h3,
            .glass-header h4 {
                font-size: 1.1rem;
            }



            /* Mobile cards for extra small screens */
            .mobile-student-cards {
                gap: 0.5rem;
            }

            .mobile-student-card {
                padding: 0.5rem;
                border-radius: 10px;
            }

            .mobile-student-header {
                margin-bottom: 0.5rem;
                padding-bottom: 0.4rem;
            }

            .student-avatar-mobile {
                width: 40px;
                height: 40px;
            }

            .student-name-mobile {
                font-size: 0.85rem;
            }

            .student-id-mobile {
                font-size: 0.7rem;
            }

            .mobile-field {
                margin-bottom: 0.4rem;
            }

            .mobile-field label {
                font-size: 0.65rem;
                margin-bottom: 0.15rem;
            }

            .mobile-value {
                font-size: 0.75rem;
            }

            .contact-item-mobile {
                font-size: 0.75rem;
                padding: 0.3rem;
            }

            .mobile-actions .btn-view,
            .mobile-actions .btn-edit,
            .mobile-actions .btn-delete {
                font-size: 0.7rem;
                padding: 0.35rem 0.5rem;
                min-width: 60px;
            }
        }

        /* Disable zoom on mobile */
        @media (max-width: 768px) {
            body {
                touch-action: pan-x pan-y;
            }
        }

        /* Loading State */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .footer {
            /* background-color: #dad9df; */
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 10px;
            color: #677488;
            opacity: 60%;

        }

        .footer img {
            width: 300px;
            padding-top: 10px;
            opacity: 60%;

        }

        /*
        .footer p {
            font-size: 12px;
            padding-top: 5px;
        } */


        @media (max-width:766px) {
            .footer {
                margin-bottom: 50px;
                margin-top: 20px;
            }

            .footer img {
                width: 60%;
            }

            .table-responsive thead tr {
                display: grid;
                grid-template-columns: 50% 50%;
            }

            .card-text {
                font-size: 0.7rem;
            }

            .card-text strong {
                font-size: 1.1rem;
            }

        }

        #notificationBtn {
            display: none;
        }





        /* .card-header {
            background-color: #343a40;
            color: #fff;
        } */
        .card-title {
            margin: 0;
        }

        .icon-text-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .icon-text-wrapper i {
            font-size: 24px;
            /* Adjust the icon size as needed */
            margin-bottom: 5px;
            /* Space between icon and text */
        }

        .dashboard-text {
            font-size: 12px;
            /* Match the font size to the icon size */
            line-height: 24px;
            /* Set the line-height to match the icon's height */
        }

        .table-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }


        /* Table row colors */
        .bg-light-blue {
            background-color: #e6f3ff !important;
        }

        .bg-light-green {
            background-color: #e6ffe6 !important;
        }

        .bg-light-red {
            background-color: #ffe6e6 !important;
        }

        .bg-light-grey {
            background-color: #f2f2f2 !important;
        }

        /* Card styles */
        .student-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .student-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .student-card .card-header {
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            padding: 15px 20px;
        }

        .student-card .card-body {
            padding: 20px;
        }

        .student-card .card-title {
            font-weight: 600;
            color: #333;
        }

        .student-card .card-text {
            margin-bottom: 10px;
            color: #555;
        }

        .student-card .btn {
            padding: 8px 20px;
            border-radius: 20px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Status color styles */
        .student-card.status-blue {
            background-color: #e6f2ff;
        }

        .student-card.status-blue .card-header {
            background-color: #cce5ff;
        }

        .student-card.status-green {
            background-color: #e6ffe6;
        }

        .student-card.status-green .card-header {
            background-color: #ccffcc;
        }

        .student-card.status-red {
            background-color: #ffe6e6;
        }

        .student-card.status-red .card-header {
            background-color: #ffcccc;
        }

        .student-card.status-grey {
            background-color: #f2f2f2;
        }

        .student-card.status-grey .card-header {
            background-color: #e6e6e6;
        }

        /* Responsive adjustments */
        @media (max-width: 767.98px) {
            .student-cards .card {
                margin-bottom: 20px;
            }

            #table-body {
                display: none !important;
            }
        }

        @media only screen and (max-width: 767px) {

            #basic-datatables tr {
                padding: 1rem;
                border-radius: 1rem;
                background-color: #294282;
            }

            #basic-datatables table {
                border-radius: 1rem;
            }

            #basic-datatables2 tr {
                padding: 1rem;
                border-radius: 1rem;
                background-color: #e7fbfb;
            }

            #basic-datatables3 tr {
                padding: 1rem;
                border-radius: 1rem;
                background-color: #e7fbfb;
            }


            .small_p p {
                font-size: 12px !important;
            }



            .student_list_thead {
                display: none !important;
            }

            .dataTables_length,
            .dataTables_info {
                display: none !important;
            }

            #basic-datatables_paginate {
                display: none !important;
            }

        }


        @media only screen and (min-width: 768px) {
            .students_thead {
                background: #294282 !important;

            }
        }

        .make_status_false_btn{
            position: absolute;
            top: -10px;
            right: -15px;
            z-index: 10;
        }

        /* Mobile Card Layout */
        .mobile-student-cards {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .mobile-student-card {
            background: var(--glass-bg);
            backdrop-filter: var(--glass-backdrop);
            -webkit-backdrop-filter: var(--glass-backdrop);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            padding: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: var(--glass-shadow);
        }

        .mobile-student-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        /* Remove extra background elements */

        .mobile-student-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 2;
        }

        .student-info-mobile {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex: 1;
        }

        .student-avatar-mobile {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid var(--primary);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .student-avatar-mobile .avatar-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .student-avatar-mobile .avatar-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .student-details-mobile {
            display: flex;
            flex-direction: column;
        }

        .student-name-mobile {
            color: var(--text-primary);
            font-weight: 700;
            font-size: 1rem;
            margin: 0 0 0.25rem 0;
        }

        .student-id-mobile {
            color: var(--text-secondary);
            font-size: 0.8rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .due-date-mobile {
            flex-shrink: 0;
        }

        .mobile-student-body {
            position: relative;
            z-index: 2;
        }

        .mobile-field {
            margin-bottom: 0.75rem;
        }

        .mobile-field label {
            display: block;
            color: var(--text-secondary);
            font-size: 0.75rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .mobile-value {
            color: var(--text-primary);
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .mobile-contact {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .contact-item-mobile {
            color: var(--text-primary);
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba(0, 0, 0, 0.05);
            padding: 0.5rem;
            border-radius: 8px;
        }

        .contact-item-mobile i {
            color: var(--primary);
            width: 16px;
        }

        .contact-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .contact-actions i,
        .contact-actions a {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .contact-actions i:hover,
        .contact-actions a:hover {
            transform: scale(1.1);
        }

        .mobile-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-start;
            flex-wrap: wrap;
        }

        .mobile-actions .btn-view,
        .mobile-actions .btn-edit,
        .mobile-actions .btn-delete {
            flex: 1;
            min-width: 80px;
            font-size: 0.8rem;
            padding: 0.5rem 0.75rem;
            text-align: center;
            justify-content: center;
        }

        .empty-state-mobile {
            text-align: center;
            padding: 3rem 1rem;
            color: var(--text-secondary);
        }

        .empty-state-mobile i {
            color: var(--text-muted);
            margin-bottom: 1rem;
        }

        .empty-state-mobile h5 {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        /* Legend Styling */
        .legend-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            justify-content: center;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            margin: 0.5rem;
            box-sizing: border-box;
        }

        .legend-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 0.5rem;
            border-radius: 50% !important;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
            display: inline-block;
        }

        .legend-green {
            background: rgba(34, 197, 94, 0.3);
            border-color: rgba(34, 197, 94, 0.5);
        }

        .legend-blue {
            background: rgba(59, 130, 246, 0.3);
            border-color: rgba(59, 130, 246, 0.5);
        }

        .legend-red {
            background: rgba(239, 68, 68, 0.3);
            border-color: rgba(239, 68, 68, 0.5);
        }

        .legend-dark-grey {
            background: rgba(75, 85, 99, 0.4);
            border-color: rgba(75, 85, 99, 0.7);
        }

        .legend-text {
            color: var(--text-primary);
            font-size: 0.9rem;
            font-weight: 500;
            white-space: nowrap;
        }

        /* Expandable Legend Styles */
        .expandable-legend {
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0.5rem !important;
            box-sizing: border-box;
        }

        .expandable-legend:hover {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 0.5rem;
            margin: 0.5rem !important;
        }

        .legend-short {
            color: var(--text-secondary);
        }

        .legend-full {
            color: var(--text-primary);
        }

        /* Mobile Legend Styles */
        .legend-container-mobile {
            display: flex;
            flex-direction: column;
        }

        .legend-item-mobile {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            padding: 0.4rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .legend-item-mobile:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }

        /* Mobile Search and Pagination Styles */
        .mobile-controls {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 0.5rem;
            display: block !important;
            visibility: visible !important;
        }

        .search-container {
            position: relative;
            display: block !important;
            visibility: visible !important;
        }

        .search-container input {
            background: rgba(255, 255, 255, 0.9) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: 8px !important;
            padding: 0.5rem 2.5rem 0.5rem 1rem !important;
            color: #333 !important;
            width: 100% !important;
            display: block !important;
            visibility: visible !important;
        }

        .search-container input:focus {
            background: rgba(255, 255, 255, 1) !important;
            border-color: var(--primary) !important;
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1) !important;
            outline: none !important;
        }

        .search-container .search-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            pointer-events: none;
        }

        /* Specific mobile search visibility */
        #mobileSearch {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: relative !important;
            z-index: 1000 !important;
            height: 45px !important;
            font-size: 16px !important;
            background: transparent !important;
            color: white !important;
            border: none !important;
            border-radius: 1rem !important;
            padding-left: 45px !important;
        }

        #mobileSearch::placeholder {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        /* Force mobile controls to be visible */
        @media (max-width: 767.98px) {
            .d-md-none {
                display: block !important;
            }
             .legend-color {         
                margin-right: 0.2rem;
             }

            .mobile-controls {
                display: block !important;
                visibility: visible !important;
                background: rgba(255, 255, 255, 0.2) !important;
                border: 2px solid rgba(255, 255, 255, 0.5) !important;
                margin-bottom: 0.5rem !important;
                padding: 0.5rem !important;
                border-radius: 8px !important;
                height: 3.6rem !important
            }

            .mobile-controls .row {
                display: flex !important;
            }

            .mobile-controls .col-8,
            .mobile-controls .col-4 {
                display: block !important;
            }
        }

        #mobilePageSize {
            background: transparent !important;
            border: none !important;
            border-radius: 0.5rem !important;
            padding: 0.5rem !important;
            color: white !important;
        }

        #mobilePageSize:focus {
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
            outline: none !important;
        }

        .mobile-pagination-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem;
            margin-top: 1rem;
        }

        /* Mobile pagination responsive adjustments */
        @media (max-width: 767.98px) {
            .mobile-pagination-container {
                padding: 0.75rem;
                margin-top: 0.75rem;
            }

            .pagination-sm {
                margin-bottom: 0 !important;
            }

            .pagination-sm .page-link {
                padding: 0.4rem 0.6rem !important;
                font-size: 0.8rem !important;
                min-width: 36px !important;
                margin: 0 1px !important;
            }

            /* Force mobile pagination button styling */
            #mobilePagination .page-link {
                background: transparent !important;
                backdrop-filter: blur(10px) !important;
                -webkit-backdrop-filter: blur(10px) !important;
                border: 1px solid rgba(255, 255, 255, 0.3) !important;
                border-radius: 8px !important;
                color: white !important;
                font-weight: 500 !important;
                text-decoration: none !important;
                transition: all 0.2s ease !important;
                display: inline-flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            #mobilePagination .page-link:hover {
                background: rgba(255, 255, 255, 0.1) !important;
                border-color: rgba(255, 255, 255, 0.5) !important;
                color: white !important;
                transform: translateY(-1px) !important;
                text-decoration: none !important;
            }

            #mobilePagination .page-item.active .page-link {
                background: rgba(99, 102, 241, 0.3) !important;
                border-color: rgba(99, 102, 241, 0.6) !important;
                color: white !important;
            }

            #mobilePagination .page-item.disabled .page-link {
                background: transparent !important;
                border-color: rgba(255, 255, 255, 0.2) !important;
                color: rgba(255, 255, 255, 0.4) !important;
                cursor: not-allowed !important;
                opacity: 0.5 !important;
            }
        }

        .mobile-info {
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 0.5rem;
        }

        /* Mobile Pagination Button Styling */
        .pagination-sm .page-link {
            background: transparent !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: 8px !important;
            margin: 0 2px !important;
            padding: 0.5rem 0.75rem !important;
            color: white !important;
            font-weight: 500 !important;
            font-size: 0.875rem !important;
            text-decoration: none !important;
            transition: all 0.2s ease !important;
            box-shadow: none !important;
            min-width: 40px !important;
            text-align: center !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            outline: none !important;
            cursor: pointer !important;
        }

        .pagination-sm .page-link:hover {
            background: rgba(255, 255, 255, 0.1) !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
            color: white !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1) !important;
            text-decoration: none !important;
        }

        .pagination-sm .page-item.active .page-link {
            background: rgba(99, 102, 241, 0.3) !important;
            border-color: rgba(99, 102, 241, 0.6) !important;
            color: white !important;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2) !important;
        }

        .pagination-sm .page-item.active .page-link:hover {
            background: rgba(99, 102, 241, 0.4) !important;
            border-color: rgba(99, 102, 241, 0.7) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.3) !important;
            color: white !important;
            text-decoration: none !important;
        }

        .pagination-sm .page-item.disabled .page-link {
            background: transparent !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            color: rgba(255, 255, 255, 0.4) !important;
            cursor: not-allowed !important;
            transform: none !important;
            box-shadow: none !important;
            opacity: 0.5 !important;
            pointer-events: none !important;
        }

        .pagination-sm .page-item.disabled .page-link:hover {
            background: transparent !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            color: rgba(255, 255, 255, 0.4) !important;
            transform: none !important;
            box-shadow: none !important;
            opacity: 0.5 !important;
            text-decoration: none !important;
        }

        /* Dark mode mobile pagination adjustments */
        body.dark-mode .pagination-sm .page-link {
            background: transparent !important;
            border-color: rgba(255, 255, 255, 0.25) !important;
            color: rgba(255, 255, 255, 0.9) !important;
        }

        body.dark-mode .pagination-sm .page-link:hover {
            background: rgba(255, 255, 255, 0.08) !important;
            border-color: rgba(255, 255, 255, 0.4) !important;
            color: white !important;
        }

        body.dark-mode .pagination-sm .page-item.active .page-link {
            background: rgba(99, 102, 241, 0.25) !important;
            border-color: rgba(99, 102, 241, 0.5) !important;
            color: white !important;
        }

        body.dark-mode .pagination-sm .page-item.disabled .page-link {
            background: transparent !important;
            border-color: rgba(255, 255, 255, 0.15) !important;
            color: rgba(255, 255, 255, 0.3) !important;
            opacity: 0.4 !important;
        }

        /* Mobile page size select styling */
        #mobilePageSize {
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        #mobilePageSize option {
            background: #333;
            color: white;
        }

        .no-results-mobile {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
        }

        /* Fix for empty state text colors */
        .empty-state-mobile h5,
        .empty-state-mobile p,
        .empty-state-mobile .text-muted {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .empty-state-mobile i.text-muted {
            color: rgba(255, 255, 255, 0.6) !important;
        }

        /* Status-based row colors for desktop table */
        .table tbody tr.status-green {
            background: rgba(34, 197, 94, 0.2) !important;
            border-left: 4px solid rgba(34, 197, 94, 0.8) !important;
        }

        .table tbody tr.status-green:hover {
            background: rgba(34, 197, 94, 0.3) !important;
        }

        .table tbody tr.status-blue {
            background: rgba(59, 130, 246, 0.2) !important;
            border-left: 4px solid rgba(59, 130, 246, 0.8) !important;
        }

        .table tbody tr.status-blue:hover {
            background: rgba(59, 130, 246, 0.3) !important;
        }

        .table tbody tr.status-red {
            background: rgba(239, 68, 68, 0.2) !important;
            border-left: 4px solid rgba(239, 68, 68, 0.8) !important;
        }

        .table tbody tr.status-red:hover {
            background: rgba(239, 68, 68, 0.3) !important;
        }

        .table tbody tr.status-dark-grey {
            background: rgba(75, 85, 99, 0.25) !important;
            border-left: 4px solid rgba(75, 85, 99, 0.8) !important;
        }

        .table tbody tr.status-dark-grey:hover {
            background: rgba(75, 85, 99, 0.35) !important;
        }

        /* Status-based card colors for mobile */
        .mobile-student-card.status-green {
            background: rgba(34, 197, 94, 0.1) !important;
            border-color: rgba(34, 197, 94, 0.3) !important;
        }

        .mobile-student-card.status-blue {
            background: rgba(59, 130, 246, 0.1) !important;
            border-color: rgba(59, 130, 246, 0.3) !important;
        }

        .mobile-student-card.status-red {
            background: rgba(239, 68, 68, 0.1) !important;
            border-color: rgba(239, 68, 68, 0.3) !important;
        }

        .mobile-student-card.status-dark-grey {
            background: rgba(75, 85, 99, 0.15) !important;
            border-color: rgba(75, 85, 99, 0.4) !important;
        }

        /* Text truncation styles */
        .truncatable-text {
            display: inline-block;
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
            transition: all 0.3s ease;
            word-break: break-all;
        }

        .truncatable-text:hover {
            color: var(--primary);
        }

        .truncatable-text.expanded {
            max-width: none;
            white-space: normal;
            overflow: visible;
            text-overflow: unset;
        }

        /* Desktop table text truncation */
        .desktop-truncatable {
            max-width: 120px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            text-align: center;
        }

        .desktop-truncatable:hover {
            color: var(--primary);
        }

        .desktop-truncatable.expanded {
            max-width: none;
            white-space: normal;
            overflow: visible;
            text-overflow: unset;
        }

        /* Responsive container improvements */
        .container-fluid {
            max-width: 100%;
            overflow-x: hidden;
        }

        /* Prevent horizontal scroll on smaller screens */
        @media (max-width: 1200px) {
            .glass-container {
                padding: 1.5rem 0.5rem;
            }

            .desktop-truncatable {
                max-width: 100px;
            }
        }

        @media (max-width: 992px) {
            .glass-container {
                padding: 1rem 0.25rem;
            }

            .desktop-truncatable {
                max-width: 80px;
            }
        }

        /* Mobile truncation adjustments */
        @media (max-width: 767.98px) {
            .truncatable-text {
                max-width: 120px;
            }
        }

        @media (max-width: 575.98px) {
            .truncatable-text {
                max-width: 100px;
            }
        }

        /* Desktop table cell improvements */
        @media (min-width: 768px) {
            .table td {
                vertical-align: middle;
                padding: 1rem 0.5rem;
            }

            .profile-img {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                object-fit: cover;
            }

            /* Ensure proper spacing for stacked content */
            .table td .d-flex.flex-column {
                min-height: 60px;
                justify-content: center;
            }
        }



        /* Responsive legend */
        @media (max-width: 767.98px) {
            .legend-container {
                flex-direction: column;
                gap: 1rem;
            }

            .legend-item {
                width: 100%;
                justify-content: flex-start;
                padding: 0.75rem 1rem;
                margin: 0.5rem 0 !important;
                box-sizing: border-box;
            }

            .legend-text {
                font-size: 0.85rem;
                white-space: normal;
            }

            /* Ensure legend items maintain consistent spacing */
            .legend-item-mobile {
                margin: 0.3rem 0 !important;
                box-sizing: border-box;
            }

            .legend-item:not(:last-child),
            .legend-item-mobile:not(:last-child) {
                margin-bottom: 0.3rem !important;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="student-list-content fade-in">
    <div class="glass-container">
        <div class="container-fluid">




            <!-- Status Legend -->
            <div class="row mb-4">
                <div class="col-12">
                    <!-- Desktop Legend -->
                    <div class="glass-card d-none d-md-block">
                        <div class="glass-header" style="padding: 1rem 2rem;">
                            <h5 style="margin: 0; font-size: 1.2rem;"><i class="fas fa-info-circle me-2"></i>Payment Status Legend</h5>
                        </div>
                        <div class="glass-body" style="padding: 1.5rem 2rem;">
                            <div class="legend-container">
                                <div class="legend-item expandable-legend" onclick="toggleLegendItem(this)">
                                    <div class="legend-color legend-green"></div>
                                    <span class="legend-text">
                                        <span class="legend-short">Due Today...</span>
                                        <span class="legend-full" style="display: none;">Due Today - Payment Required</span>
                                    </span>
                                </div>
                                <div class="legend-item expandable-legend" onclick="toggleLegendItem(this)">
                                    <div class="legend-color legend-blue"></div>
                                    <span class="legend-text">
                                        <span class="legend-short">Due Soon...</span>
                                        <span class="legend-full" style="display: none;">Due Soon (1-6 days) - Reminder Needed</span>
                                    </span>
                                </div>
                                <div class="legend-item expandable-legend" onclick="toggleLegendItem(this)">
                                    <div class="legend-color legend-red"></div>
                                    <span class="legend-text">
                                        <span class="legend-short">Overdue...</span>
                                        <span class="legend-full" style="display: none;">Overdue (1-9 days) - Follow Up Required</span>
                                    </span>
                                </div>
                                <div class="legend-item expandable-legend" onclick="toggleLegendItem(this)">
                                    <div class="legend-color legend-dark-grey"></div>
                                    <span class="legend-text">
                                        <span class="legend-short">Long Overdue...</span>
                                        <span class="legend-full" style="display: none;">Long Overdue (10+ days) - Urgent Action</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Legend (Direct, no card) -->
                    <div class="d-md-none mb-3">
                        <h6 class="text-white mb-2"><i class="fas fa-info-circle me-2"></i>Payment Status Legend</h6>
                        <div class="row g-1">
                            <div class="col-6">
                                <div class="legend-item-mobile expandable-legend" onclick="toggleLegendItem(this)">
                                    <div class="legend-color legend-green"></div>
                                    <span class="legend-text">
                                        <span class="legend-short">Due Today...</span>
                                        <span class="legend-full" style="display: none;">Due Today - Payment Required</span>
                                    </span>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="legend-item-mobile expandable-legend" onclick="toggleLegendItem(this)">
                                    <div class="legend-color legend-blue"></div>
                                    <span class="legend-text">
                                        <span class="legend-short">Due Soon...</span>
                                        <span class="legend-full" style="display: none;">Due Soon (1-6 days) - Reminder Needed</span>
                                    </span>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="legend-item-mobile expandable-legend" onclick="toggleLegendItem(this)">
                                    <div class="legend-color legend-red"></div>
                                    <span class="legend-text">
                                        <span class="legend-short">Overdue...</span>
                                        <span class="legend-full" style="display: none;">Overdue (1-9 days) - Follow Up Required</span>
                                    </span>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="legend-item-mobile expandable-legend" onclick="toggleLegendItem(this)">
                                    <div class="legend-color legend-dark-grey"></div>
                                    <span class="legend-text">
                                        <span class="legend-short">Long Overdue...</span>
                                        <span class="legend-full" style="display: none;">Long Overdue (10+ days) - Urgent Action</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Messages Section -->
            {% if messages %}
            <div class="row mb-4">
                <div class="col-12">
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Student Table -->
            <div class="row">
                <div class="col-12">
                    <div class="glass-card d-none d-md-block">
                        <div class="glass-header">
                            <h4><i class="fas fa-table me-2"></i>Student Information</h4>
                        </div>
                        <div class="glass-body">
                            <!-- Desktop Table View -->
                            <div class="table-responsive" style="overflow-x: auto; max-width: 100%; margin: 0;">
                                <table class="table" id="basic-datatables" style="min-width: 900px; table-layout: fixed; margin-bottom: 0;">
                                    <thead>
                                        <tr>
                                            <th style="width: 12%;"><i class="fas fa-id-card me-2"></i>Student ID</th>
                                            <th style="width: 10%;"><i class="fas fa-book me-2"></i>Course</th>
                                            <th style="width: 15%;"><i class="fas fa-user me-2"></i>Name</th>
                                            <th style="width: 12%;"><i class="fas fa-phone me-2"></i>Mobile</th>
                                            <th style="width: 18%;"><i class="fas fa-envelope me-2"></i>Email</th>
                                            <th style="width: 13%;"><i class="fas fa-calendar me-2"></i>Due Date</th>
                                            <th style="width: 20%;"><i class="fas fa-cogs me-2"></i>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for data in std %}
                                        <tr class="status-{{ data.student.color }}">
                                            <td>
                                                <div class="d-flex flex-column align-items-center text-center">
                                                    <a href="/students/{{ data.student.slug }}/" class="text-decoration-none">
                                                        <strong class="desktop-truncatable" onclick="toggleTextExpansion(this)" title="Click to expand">{{ data.student.unique_id }}</strong>
                                                    </a>
                                                </div>
                                            </td>
                                            <td>{{ data.student.course }}</td>
                                            <td>
                                                <div class="d-flex flex-column align-items-center text-center">
                                                    {% if data.student.image %}
                                                    <img src="{{ data.student.image.url }}" alt="{{ data.student.name }}"
                                                         class="profile-img mb-2">
                                                    {% else %}
                                                    <div class="profile-img mb-2 d-flex align-items-center justify-content-center bg-primary text-white">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    {% endif %}
                                                    <strong>{{ data.student.name }}</strong>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column align-items-center text-center">
                                                    <span class="mb-2">{{ data.student.mobile }}</span>
                                                    <div class="d-flex gap-2">
                                                        <i class="fas fa-copy text-muted cursor-pointer"
                                                           onclick="copyToClipboard('{{ data.student.mobile }}')"
                                                           title="Copy"></i>
                                                        <a href="sms:{{ data.student.mobile }}?body=Dear%20{{ data.student.name|urlencode }},%20This%20is%20a%20reminder%20about%20your%20library%20fee%20payment.%20Student%20ID:%20{{ data.student.unique_id|urlencode }}.%20Please%20visit%20{{ data.student.librarian.library_name|default:'Librainian'|urlencode }}%20for%20payment.%20Thank%20you." class="text-success me-2" title="Send SMS">
                                                            <i class="fas fa-sms"></i>
                                                        </a>
                                                        <a href="tel:{{ data.student.mobile }}" class="text-success">
                                                            <i class="fas fa-phone" title="Call"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column align-items-center text-center">
                                                    <span class="desktop-truncatable mb-2" onclick="toggleTextExpansion(this)" title="Click to expand">{{ data.student.email }}</span>
                                                    <div class="d-flex gap-2">
                                                        <i class="fas fa-copy text-muted cursor-pointer"
                                                           onclick="copyToClipboard('{{ data.student.email }}')"
                                                           title="Copy"></i>
                                                        <a href="mailto:{{ data.student.email }}?subject=Fee%20Reminder%20-%20{{ data.student.librarian.library_name|default:'Librainian'|urlencode }}&body=Dear%20{{ data.student.name|urlencode }},%0A%0AThis%20is%20a%20reminder%20regarding%20your%20library%20fee%20payment.%0A%0AStudent%20ID:%20{{ data.student.unique_id|urlencode }}%0ALibrary:%20{{ data.student.librarian.library_name|default:'Librainian'|urlencode }}%0A%0APlease%20make%20your%20payment%20at%20your%20earliest%20convenience.%0A%0ABest%20regards,%0A{{ data.student.librarian.library_name|default:'Librainian'|urlencode }}%20Team" class="text-primary">
                                                            <i class="fas fa-envelope" title="Send Email"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning text-dark">{{ data.due_date }}</span>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <a href="/students/{{ data.student.slug }}/" class="btn-view">
                                                        <i class="fas fa-eye"></i>
                                                        View
                                                    </a>
                                                    <a href="/students/{{ data.student.slug }}/send-email/" class="btn-edit">
                                                        <i class="fas fa-envelope"></i>
                                                        Email
                                                    </a>
                                                    <a href="/students/{{ data.student.slug }}/send-sms/" class="btn-delete">
                                                        <i class="fas fa-sms"></i>
                                                        SMS
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile Card View -->
            <div class="d-md-none">
                <!-- Mobile Search and Controls -->
                <div class="mobile-controls mb-3">
                    <div class="row g-2">
                        <div class="col-8">
                            <!-- Mobile Search Bar -->
                            <div class="position-relative mb-3">
                                <input type="text" id="mobileSearch" class="form-control" placeholder="Search students..." style="background: transparent; border: none; color: white; padding-left: 45px; border-radius: 1rem;">
                                <i class="fas fa-search position-absolute" style="left: 15px; top: 50%; transform: translateY(-50%); color: rgba(255, 255, 255, 0.7);"></i>
                            </div>
                        </div>
                        <div class="col-4">
                            <!-- Mobile Pagination Controls -->
                            <div class="d-flex align-items-center justify-content-end">
                                <select id="mobilePageSize" class="form-select form-select-sm me-2" style="background: transparent; border: none; color: white; width: auto; border-radius: 0.5rem;">
                                    <option value="10" selected>10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <span class="text-white small">/ page</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">

                    {% for data in std %}
                    <div class="col-12 mb-3">
                        <div class="mobile-student-card status-{{ data.student.color }}"
                             data-name="{{ data.student.name|lower }}"
                             data-id="{{ data.student.unique_id|lower }}"
                             data-course="{{ data.student.course|lower }}"
                             data-mobile="{{ data.student.mobile }}"
                             data-email="{{ data.student.email|lower }}">
                                    <div class="mobile-student-header">
                                        <div class="student-info-mobile">
                                            <div class="student-avatar-mobile">
                                                {% if data.student.image %}
                                                <img src="{{ data.student.image.url }}" alt="{{ data.student.name }}" class="avatar-img">
                                                {% else %}
                                                <div class="avatar-placeholder">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                {% endif %}
                                            </div>
                                            <div class="student-details-mobile">
                                                <h6 class="student-name-mobile">{{ data.student.name }}</h6>
                                                <div class="student-id-mobile">
                                                    <span>ID:</span>
                                                    <span class="truncatable-text" onclick="toggleTextExpansion(this)" title="Click to expand">{{ data.student.unique_id }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="due-date-mobile">
                                            <span class="badge bg-warning text-dark">{{ data.due_date }}</span>
                                        </div>
                                    </div>

                                    <div class="mobile-student-body">
                                        <div class="row g-2">
                                            <div class="col-12">
                                                <div class="mobile-field">
                                                    <label>Course</label>
                                                    <div class="mobile-value">
                                                        <i class="fas fa-book me-2"></i>
                                                        {{ data.student.course }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <div class="mobile-field">
                                                    <label>Contact Information</label>
                                                    <div class="mobile-contact">
                                                        <div class="contact-item-mobile">
                                                            <i class="fas fa-phone me-2"></i>
                                                            <span>{{ data.student.mobile }}</span>
                                                            <div class="contact-actions">
                                                                <i class="fas fa-copy text-muted cursor-pointer"
                                                                   onclick="copyToClipboard('{{ data.student.mobile }}')"
                                                                   title="Copy"></i>
                                                                <a href="sms:{{ data.student.mobile }}?body=Dear%20{{ data.student.name|urlencode }},%20This%20is%20a%20reminder%20about%20your%20library%20fee%20payment.%20Student%20ID:%20{{ data.student.unique_id|urlencode }}.%20Please%20visit%20{{ data.student.librarian.library_name|default:'Librainian'|urlencode }}%20for%20payment.%20Thank%20you." class="text-success me-2" title="Send SMS">
                                                                    <i class="fas fa-sms"></i>
                                                                </a>
                                                                <a href="tel:{{ data.student.mobile }}" class="text-success">
                                                                    <i class="fas fa-phone" title="Call"></i>
                                                                </a>
                                                            </div>
                                                        </div>
                                                        <div class="contact-item-mobile">
                                                            <i class="fas fa-envelope me-2"></i>
                                                            <span class="truncatable-text" onclick="toggleTextExpansion(this)" title="Click to expand">{{ data.student.email }}</span>
                                                            <div class="contact-actions">
                                                                <i class="fas fa-copy text-muted cursor-pointer"
                                                                   onclick="copyToClipboard('{{ data.student.email }}')"
                                                                   title="Copy"></i>
                                                                <a href="mailto:{{ data.student.email }}?subject=Fee%20Reminder%20-%20{{ data.student.librarian.library_name|default:'Librainian'|urlencode }}&body=Dear%20{{ data.student.name|urlencode }},%0A%0AThis%20is%20a%20reminder%20regarding%20your%20library%20fee%20payment.%0A%0AStudent%20ID:%20{{ data.student.unique_id|urlencode }}%0ALibrary:%20{{ data.student.librarian.library_name|default:'Librainian'|urlencode }}%0A%0APlease%20make%20your%20payment%20at%20your%20earliest%20convenience.%0A%0ABest%20regards,%0A{{ data.student.librarian.library_name|default:'Librainian'|urlencode }}%20Team" class="text-primary">
                                                                    <i class="fas fa-envelope" title="Send Email"></i>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <div class="mobile-field">
                                                    <label>Actions</label>
                                                    <div class="mobile-actions">
                                                        <a href="/students/{{ data.student.slug }}/" class="btn-view">
                                                            <i class="fas fa-eye"></i>
                                                            View
                                                        </a>
                                                        <a href="/students/{{ data.student.slug }}/send-email/" class="btn-edit">
                                                            <i class="fas fa-envelope"></i>
                                                            Email
                                                        </a>
                                                        <a href="/students/{{ data.student.slug }}/send-sms/" class="btn-delete">
                                                            <i class="fas fa-sms"></i>
                                                            SMS
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                                {% empty %}
                                <div class="col-12">
                                    <div class="empty-state-mobile text-center py-5">
                                        <i class="fas fa-users fa-3x mb-3 text-muted"></i>
                                        <h5 class="text-muted">No Students Found</h5>
                                        <p class="text-muted">There are no students to display at the moment.</p>
                                    </div>
                                </div>
                                {% endfor %}
                                </div>

                <!-- Mobile Pagination and Info -->
                <div class="mobile-pagination-container mt-3">
                    <div class="row align-items-center">
                        <div class="col-12 col-sm-6 mb-2 mb-sm-0">
                            <div class="mobile-info text-white small">
                                Showing <span id="mobileShowingStart">1</span> to <span id="mobileShowingEnd">10</span> of <span id="mobileTotal">{{ std|length }}</span> students
                            </div>
                        </div>
                        <div class="col-12 col-sm-6">
                            <nav aria-label="Mobile pagination">
                                <ul class="pagination pagination-sm justify-content-center justify-content-sm-end mb-0" id="mobilePagination">
                                    <!-- Pagination buttons will be generated by JavaScript -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>





    <!-- JavaScript dependencies -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

    <script>
        // Modern DataTables initialization and functionality
        $(document).ready(function() {
            // Initialize DataTable only on desktop
            if (window.innerWidth >= 768) {
                $('#basic-datatables').DataTable({
                    responsive: true,
                    pageLength: 10,
                    lengthMenu: [[5, 10, 25, 50, 100], [5, 10, 25, 50, 100]],
                    language: {
                        search: "Search students:",
                        lengthMenu: "Show _MENU_ students per page",
                        info: "Showing _START_ to _END_ of _TOTAL_ students",
                        paginate: {
                            first: "First",
                            last: "Last",
                            next: "Next",
                            previous: "Prev."
                        }
                    },
                    columnDefs: [
                        { orderable: false, targets: [0, 6] }, // Disable sorting for image and actions columns
                        { className: "text-center", targets: [0, 6] }
                    ],
                    order: [[2, 'asc']], // Sort by name column by default
                    drawCallback: function() {
                        // Re-initialize tooltips after table redraw
                        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
                        tooltipTriggerList.map(function (tooltipTriggerEl) {
                            return new bootstrap.Tooltip(tooltipTriggerEl);
                        });

                        // Apply custom glassmorphism styling to pagination buttons
                        setTimeout(function() {
                            $('.dataTables_paginate .paginate_button').each(function() {
                                const $this = $(this);

                                // Ensure proper styling is applied
                                if ($this.hasClass('disabled')) {
                                    $this.css({
                                        'pointer-events': 'none',
                                        'cursor': 'not-allowed'
                                    });
                                }
                            });
                        }, 50);
                    }
                });
            }

            // Auto-hide alerts after 10 seconds
            $('.alert').each(function() {
                const alert = this;
                setTimeout(function() {
                    $(alert).fadeOut(1000);
                }, 10000);
            });

            // Copy to clipboard functionality
            window.copyToClipboard = function(text) {
                // Ensure text is a string
                const textToCopy = String(text).trim();

                if (navigator.clipboard && window.isSecureContext) {
                    // Use modern clipboard API
                    navigator.clipboard.writeText(textToCopy).then(function() {
                        showCopySuccess(textToCopy);
                    }).catch(function(err) {
                        console.error('Clipboard API failed:', err);
                        fallbackCopyTextToClipboard(textToCopy);
                    });
                } else {
                    // Fallback for older browsers or non-secure contexts
                    fallbackCopyTextToClipboard(textToCopy);
                }
            };

            // Fallback copy method
            function fallbackCopyTextToClipboard(text) {
                const textArea = document.createElement("textarea");
                textArea.value = text;
                textArea.style.position = "fixed";
                textArea.style.left = "-999999px";
                textArea.style.top = "-999999px";
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                try {
                    const successful = document.execCommand('copy');
                    if (successful) {
                        showCopySuccess(text);
                    } else {
                        showCopyError();
                    }
                } catch (err) {
                    console.error('Fallback copy failed:', err);
                    showCopyError();
                }

                document.body.removeChild(textArea);
            }

            // Show copy success message
            function showCopySuccess(text) {
                const toast = $(`
                    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
                        <div class="toast show" role="alert">
                            <div class="toast-header">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <strong class="me-auto">Success</strong>
                                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                            </div>
                            <div class="toast-body">
                                Copied: ${text}
                            </div>
                        </div>
                    </div>
                `);
                $('body').append(toast);
                setTimeout(() => toast.remove(), 3000);
            }

            // Show copy error message
            function showCopyError() {
                const toast = $(`
                    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
                        <div class="toast show" role="alert">
                            <div class="toast-header">
                                <i class="fas fa-exclamation-circle text-danger me-2"></i>
                                <strong class="me-auto">Error</strong>
                                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                            </div>
                            <div class="toast-body">
                                Failed to copy to clipboard
                            </div>
                        </div>
                    </div>
                `);
                $('body').append(toast);
                setTimeout(() => toast.remove(), 3000);
            }

            // Text expansion functionality
            window.toggleTextExpansion = function(element) {
                const $element = $(element);

                // Prevent event bubbling
                event.stopPropagation();

                if ($element.hasClass('expanded')) {
                    // Collapse text
                    $element.removeClass('expanded');
                    $element.attr('title', 'Click to expand');

                    // Determine max-width based on element type and screen size
                    let maxWidth = '150px';
                    if ($element.hasClass('desktop-truncatable')) {
                        if (window.innerWidth <= 992) {
                            maxWidth = '80px';
                        } else if (window.innerWidth <= 1200) {
                            maxWidth = '100px';
                        } else {
                            maxWidth = '120px';
                        }
                    } else if ($element.hasClass('truncatable-text')) {
                        if (window.innerWidth <= 575) {
                            maxWidth = '100px';
                        } else if (window.innerWidth <= 767) {
                            maxWidth = '120px';
                        } else {
                            maxWidth = '150px';
                        }
                    }

                    $element.css({
                        'max-width': maxWidth,
                        'white-space': 'nowrap',
                        'overflow': 'hidden',
                        'text-overflow': 'ellipsis',
                        'display': 'inline-block'
                    });
                } else {
                    // Expand text
                    $element.addClass('expanded');
                    $element.attr('title', 'Click to collapse');
                    $element.css({
                        'max-width': 'none',
                        'white-space': 'normal',
                        'overflow': 'visible',
                        'text-overflow': 'unset',
                        'display': 'inline-block',
                        'word-break': 'break-word'
                    });
                }
            };

            // Loading state for action buttons
            $('.btn-view, .btn-edit, .btn-delete').on('click', function() {
                const btn = $(this);
                const originalText = btn.html();
                btn.html('<i class="fas fa-spinner fa-spin"></i> Loading...');
                btn.prop('disabled', true);

                // Re-enable after navigation (this won't execute if page changes)
                setTimeout(() => {
                    btn.html(originalText);
                    btn.prop('disabled', false);
                }, 2000);
            });

            // Initialize mobile pagination for mobile devices
            if (window.innerWidth < 768) {
                initializeMobilePagination();
            }
        });

        // Individual legend item toggle functionality
        function toggleLegendItem(element) {
            const shortText = element.querySelector('.legend-short');
            const fullText = element.querySelector('.legend-full');

            if (shortText.style.display === 'none') {
                shortText.style.display = 'inline';
                fullText.style.display = 'none';
                // Ensure consistent spacing after collapse
                element.style.margin = '0.5rem';
                element.style.marginBottom = '0.5rem';
            } else {
                shortText.style.display = 'none';
                fullText.style.display = 'inline';
                // Maintain spacing during expansion
                element.style.margin = '0.5rem';
                element.style.marginBottom = '0.5rem';
            }

            // Force layout recalculation to maintain proper spacing
            setTimeout(() => {
                element.style.margin = '0.5rem';
                element.style.marginBottom = '0.5rem';
            }, 10);
        }

        // Mobile pagination and search functionality
        function initializeMobilePagination() {
            let currentPage = 1;
            let pageSize = 10;
            let filteredCards = [];
            let allCards = [];

            // Get all mobile cards
            function getAllCards() {
                return Array.from(document.querySelectorAll('.mobile-student-card'));
            }

            // Filter cards based on search term
            function filterCards(searchTerm) {
                const cards = getAllCards();
                return cards.filter(card => {
                    const name = card.getAttribute('data-name') || '';
                    const id = card.getAttribute('data-id') || '';
                    const course = card.getAttribute('data-course') || '';
                    const mobile = card.getAttribute('data-mobile') || '';
                    const email = card.getAttribute('data-email') || '';

                    const searchLower = searchTerm.toLowerCase();
                    return name.includes(searchLower) ||
                           id.includes(searchLower) ||
                           course.includes(searchLower) ||
                           mobile.includes(searchLower) ||
                           email.includes(searchLower);
                });
            }

            // Show cards for current page
            function showPage(page) {
                const startIndex = (page - 1) * pageSize;
                const endIndex = startIndex + pageSize;

                // Hide all cards first
                getAllCards().forEach(card => {
                    card.closest('.col-12').style.display = 'none';
                });

                // Show cards for current page
                const cardsToShow = filteredCards.slice(startIndex, endIndex);
                cardsToShow.forEach(card => {
                    card.closest('.col-12').style.display = 'block';
                });

                // Update info display
                updateInfoDisplay(startIndex + 1, Math.min(endIndex, filteredCards.length), filteredCards.length);

                // Update pagination buttons
                updatePaginationButtons();

                // Show/hide no results message
                const noResults = document.getElementById('noResultsMobile');
                if (filteredCards.length === 0) {
                    noResults.style.display = 'block';
                } else {
                    noResults.style.display = 'none';
                }
            }

            // Update info display
            function updateInfoDisplay(start, end, total) {
                document.getElementById('mobileShowingStart').textContent = total > 0 ? start : 0;
                document.getElementById('mobileShowingEnd').textContent = end;
                document.getElementById('mobileTotal').textContent = total;
            }

            // Update pagination buttons
            function updatePaginationButtons() {
                const totalPages = Math.ceil(filteredCards.length / pageSize);
                const pagination = document.getElementById('mobilePagination');

                pagination.innerHTML = '';

                // Previous button
                const prevLi = document.createElement('li');
                prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
                prevLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage - 1}">Prev.</a>`;
                pagination.appendChild(prevLi);

                // Page numbers
                const startPage = Math.max(1, currentPage - 2);
                const endPage = Math.min(totalPages, currentPage + 2);

                for (let i = startPage; i <= endPage; i++) {
                    const li = document.createElement('li');
                    li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                    li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
                    pagination.appendChild(li);
                }

                // Next button
                const nextLi = document.createElement('li');
                nextLi.className = `page-item ${currentPage === totalPages || totalPages === 0 ? 'disabled' : ''}`;
                nextLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>`;
                pagination.appendChild(nextLi);

                // Add click event listeners
                pagination.querySelectorAll('.page-link').forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const page = parseInt(this.getAttribute('data-page'));
                        if (page && page !== currentPage && page >= 1 && page <= totalPages) {
                            currentPage = page;
                            showPage(currentPage);
                        }
                    });
                });
            }

            // Initialize
            function init() {
                allCards = getAllCards();
                filteredCards = allCards;
                showPage(1);
            }

            // Search functionality
            const searchInput = document.getElementById('mobileSearch');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.trim();
                    filteredCards = filterCards(searchTerm);
                    currentPage = 1;
                    showPage(currentPage);
                });
            }

            // Page size change
            const pageSizeSelect = document.getElementById('mobilePageSize');
            if (pageSizeSelect) {
                pageSizeSelect.addEventListener('change', function() {
                    pageSize = parseInt(this.value);
                    currentPage = 1;
                    showPage(currentPage);
                });
            }

            // Initialize on load
            init();
        }

    </script>
</div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>
{% endblock %}
