<!-- Universal Loader Component -->
<!-- Usage: {% include "components/universal_loader.html" %} -->

<script>
// Universal Loader with Immediate Dark Mode Detection
(function() {
    // Check for dark mode preference immediately
    var isDarkMode = localStorage.getItem('darkMode') === 'enabled';
    var loaderBg = isDarkMode ? 'linear-gradient(135deg, #1f2937 0%, #111827 100%)' : 'linear-gradient(135deg, #6366f1 0%, #4f46e5 100%)';
    var progressColor = isDarkMode ? '#6366f1' : '#6200ee';
    
    // Create universal loader HTML
    var loaderHTML = `
    <div id="universalLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: ${loaderBg}; z-index: 9999;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
            <style>
                @font-face {
                    font-family: 'FSEX300';
                    src: url('/static/fonts/FSEX300.ttf') format('truetype');
                    font-weight: normal;
                    font-style: normal;
                }

                .universal-loader-text {
                    font-family: 'FSEX300', monospace;
                    font-size: 32px;
                    color: #ffffff;
                    letter-spacing: 2px;
                    margin-bottom: 20px;
                    text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                }

                .universal-loader-dots::after {
                    content: "";
                    animation: dots 1.5s infinite;
                }

                @keyframes dots {
                    0% { content: ""; }
                    25% { content: "."; }
                    50% { content: ".."; }
                    75% { content: "..."; }
                    100% { content: ""; }
                }

                .universal-loader-bar {
                    width: 300px;
                    height: 20px;
                    background-color: rgba(255, 255, 255, 0.2);
                    border-radius: 10px;
                    overflow: hidden;
                    margin: 20px auto;
                }

                .universal-loader-progress {
                    width: 0%;
                    height: 100%;
                    background-color: ${progressColor};
                    border-radius: 10px;
                    animation: progress 2s infinite;
                }

                @keyframes progress {
                    0% { width: 0%; }
                    50% { width: 100%; }
                    100% { width: 0%; }
                }

                .universal-spinner {
                    width: 50px;
                    height: 50px;
                    border: 4px solid rgba(255, 255, 255, 0.3);
                    border-top: 4px solid white;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 1rem;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
            
            <!-- Logo/Text Loader -->
            <div class="universal-loader-text">LIBRAINIAN<span class="universal-loader-dots"></span></div>
            <div class="universal-loader-bar">
                <div class="universal-loader-progress"></div>
            </div>
            
            <!-- Alternative: Simple Spinner -->
            <!-- <div class="universal-spinner"></div> -->
        </div>
    </div>
    `;

    // Add loader to page
    document.write(loaderHTML);

    // Remove loader when page is loaded
    window.addEventListener('load', function() {
        var loader = document.getElementById('universalLoader');
        if (loader) {
            setTimeout(function() {
                loader.style.opacity = '0';
                setTimeout(function() {
                    loader.style.display = 'none';
                }, 300);
            }, 500);
        }
    });
})();
</script>
