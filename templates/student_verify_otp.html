{% extends "base.html" %}

{% block title %}Verify OTP - Librainian{% endblock %}

{% block page_title %}Verify OTP{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/students/">Students</a></li>
<li class="breadcrumb-item active" aria-current="page">Verify OTP</li>
{% endblock %}

{% block extra_css %}
<style>
        /* OTP Page Specific Styles */
        .page-content {
            background: var(--gradient-hero);
            min-height: calc(100vh - var(--topbar-height));
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            position: relative;
        }


        * {
            box-sizing: border-box;
        }

        /* Body styles handled by base template */

        .otp-container {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
            position: relative;
        }

        .page-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(99, 102, 241, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        .otp-card {
            background: var(--glass-bg-light);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--glass-shadow-lg);
            padding: 3rem;
            width: 100%;
            max-width: 500px;
            position: relative;
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
            z-index: 1;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .otp-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-glass);
            pointer-events: none;
            opacity: 0.8;
        }

        .otp-header {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .brand-logo {
            width: 60px;
            height: 60px;
            background: var(--gradient-primary);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            box-shadow: var(--shadow-lg);
        }

        .otp-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0 0 0.5rem 0;
        }

        .otp-subtitle {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin: 0;
        }

        .otp-inputs {
            display: flex;
            gap: 0.75rem;
            justify-content: center;
            margin: 2rem 0;
            position: relative;
            z-index: 2;
        }

        .otp-input {
            width: 50px;
            height: 50px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            text-align: center;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            background: var(--glass-bg-light);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            transition: var(--transition);
            outline: none;
        }

        .otp-input:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            background: var(--bg-primary);
        }

        .otp-input:valid {
            border-color: var(--success);
            background: rgba(16, 185, 129, 0.05);
        }

        .verify-btn {
            width: 100%;
            padding: 1rem;
            background: var(--gradient-primary);
            border: none;
            border-radius: var(--border-radius);
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
            margin-bottom: 1rem;
            z-index: 2;
        }

        .verify-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .verify-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .verify-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .verify-btn:hover::before {
            left: 100%;
        }

        .back-btn {
            width: 100%;
            padding: 1rem;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            color: var(--text-secondary);
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: inline-block;
            text-align: center;
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            position: relative;
            z-index: 2;
        }

        .back-btn:hover {
            background: var(--glass-bg-medium);
            color: var(--text-primary);
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .alert-modern {
            background: var(--glass-bg-light);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-bottom: 1.5rem;
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            position: relative;
            z-index: 2;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
            color: var(--success);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.3);
            color: var(--danger);
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border-color: rgba(245, 158, 11, 0.3);
            color: var(--warning);
        }

        .alert-info {
            background: rgba(59, 130, 246, 0.1);
            border-color: rgba(59, 130, 246, 0.3);
            color: #3b82f6;
        }

        .btn-close-modern {
            background: none;
            border: none;
            font-size: 1.25rem;
            color: inherit;
            opacity: 0.7;
            cursor: pointer;
            float: right;
            margin-top: -0.25rem;
        }

        .btn-close-modern:hover {
            opacity: 1;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .page-content {
                padding: 0.5rem;
                padding-bottom: calc(0.5rem + var(--bottom-menu-height, 70px));
                min-height: calc(100vh - var(--topbar-height) - var(--bottom-menu-height, 70px));
            }

            .otp-card {
                padding: 2rem 1.5rem;
                margin: 0.5rem;
                max-width: calc(100vw - 1rem);
            }

            .otp-inputs {
                gap: 0.5rem;
            }

            .otp-input {
                width: 45px;
                height: 45px;
                font-size: 1.125rem;
            }

            .otp-title {
                font-size: 1.5rem;
            }

            .brand-logo {
                width: 50px;
                height: 50px;
                font-size: 1.25rem;
            }
        }

        @media (max-width: 575.98px) {
            .otp-card {
                padding: 1.5rem 1rem;
                margin: 0.25rem;
            }

            .otp-inputs {
                gap: 0.375rem;
            }

            .otp-input {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }

            .otp-title {
                font-size: 1.25rem;
            }

            .otp-subtitle {
                font-size: 0.8rem;
            }
        }

        /* Dark Mode Enhancements */
        body.dark-mode .page-content::before {
            background:
                radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(99, 102, 241, 0.05) 0%, transparent 50%);
        }

        body.dark-mode .otp-card {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
        }

        body.dark-mode .otp-card::before {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.08) 0%,
                rgba(255, 255, 255, 0.03) 50%,
                transparent 100%);
        }

        body.dark-mode .otp-input {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        body.dark-mode .otp-input:focus {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
        }

        body.dark-mode .otp-input:valid {
            border-color: var(--success);
            background: rgba(16, 185, 129, 0.1);
        }

        body.dark-mode .verify-btn {
            background: var(--gradient-primary);
            box-shadow: 0 10px 25px rgba(99, 102, 241, 0.4);
        }

        body.dark-mode .verify-btn:hover {
            box-shadow: 0 15px 35px rgba(99, 102, 241, 0.5);
        }

        body.dark-mode .back-btn {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
        }

        body.dark-mode .back-btn:hover {
            background: rgba(255, 255, 255, 0.08);
            color: var(--text-primary);
            border-color: rgba(255, 255, 255, 0.2);
        }

        body.dark-mode .alert-modern {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .alert-success {
            background: rgba(16, 185, 129, 0.15);
            border-color: rgba(16, 185, 129, 0.3);
            color: #34d399;
        }

        body.dark-mode .alert-danger {
            background: rgba(239, 68, 68, 0.15);
            border-color: rgba(239, 68, 68, 0.3);
            color: #f87171;
        }

        body.dark-mode .alert-warning {
            background: rgba(245, 158, 11, 0.15);
            border-color: rgba(245, 158, 11, 0.3);
            color: #fbbf24;
        }

        body.dark-mode .alert-info {
            background: rgba(59, 130, 246, 0.15);
            border-color: rgba(59, 130, 246, 0.3);
            color: #60a5fa;
        }

        body.dark-mode .brand-logo {
            box-shadow: 0 10px 25px rgba(99, 102, 241, 0.4);
        }

        body.dark-mode .otp-title {
            color: var(--text-primary);
        }

        body.dark-mode .otp-subtitle {
            color: var(--text-secondary);
        }

        /* Dark mode animation improvements */
        body.dark-mode .otp-input:focus {
            animation: darkGlow 0.3s ease-out;
        }

        @keyframes darkGlow {
            0% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.3);
            }
            100% {
                box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="otp-container">
        <div class="otp-card">
            <div class="otp-header">
                <div class="brand-logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1 class="otp-title">Verify OTP</h1>
                <p class="otp-subtitle">Enter the 6-digit code sent to your email</p>
            </div>

            <!-- Alert Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert-modern alert-{{ message.tags|default:'info' }}" role="alert">
                        <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' or message.tags == 'danger' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-circle{% else %}info-circle{% endif %} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close-modern" onclick="this.parentElement.style.display='none'">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}

            <form id="otp-form" method="POST">
                {% csrf_token %}
                <div class="otp-inputs">
                    <input type="tel" name="otp1" class="otp-input" maxlength="1" autofocus required>
                    <input type="tel" name="otp2" class="otp-input" maxlength="1" required>
                    <input type="tel" name="otp3" class="otp-input" maxlength="1" required>
                    <input type="tel" name="otp4" class="otp-input" maxlength="1" required>
                    <input type="tel" name="otp5" class="otp-input" maxlength="1" required>
                    <input type="tel" name="otp6" class="otp-input" maxlength="1" required>
                </div>

                <button type="submit" class="verify-btn" id="verifyBtn">
                    <i class="fas fa-check-circle me-2"></i>
                    Verify OTP
                </button>

                <a href="/students/" class="back-btn">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Students
                </a>
            </form>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const inputs = document.querySelectorAll('.otp-input');
        const form = document.getElementById('otp-form');
        const verifyBtn = document.getElementById('verifyBtn');

        // OTP input handling
        inputs.forEach((input, index) => {
            input.addEventListener('input', function() {
                // Remove any non-numeric characters
                this.value = this.value.replace(/[^0-9]/g, '');

                // Limit to 1 character
                if (this.value.length > 1) {
                    this.value = this.value.slice(0, 1);
                }

                // Move to next input if current is filled
                if (this.value !== '' && index < inputs.length - 1) {
                    inputs[index + 1].focus();
                }

                // Check if all inputs are filled
                checkAllInputsFilled();
            });

            input.addEventListener('keydown', function(event) {
                // Move to previous input on backspace if current is empty
                if (event.key === 'Backspace' && this.value === '' && index > 0) {
                    inputs[index - 1].focus();
                }

                // Allow only numeric keys, backspace, delete, tab, and arrow keys
                const allowedKeys = ['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight'];
                if (!allowedKeys.includes(event.key) && (event.key < '0' || event.key > '9')) {
                    event.preventDefault();
                }
            });

            input.addEventListener('paste', function(event) {
                event.preventDefault();
                const pasteData = event.clipboardData.getData('text');
                const numbers = pasteData.replace(/[^0-9]/g, '').slice(0, 6);

                // Fill inputs with pasted numbers
                for (let i = 0; i < numbers.length && i < inputs.length; i++) {
                    inputs[i].value = numbers[i];
                }

                // Focus on the next empty input or the last one
                const nextEmptyIndex = Math.min(numbers.length, inputs.length - 1);
                inputs[nextEmptyIndex].focus();

                checkAllInputsFilled();
            });
        });

        function checkAllInputsFilled() {
            const allFilled = Array.from(inputs).every(input => input.value !== '');
            verifyBtn.disabled = !allFilled;

            if (allFilled) {
                verifyBtn.style.background = 'linear-gradient(135deg, var(--success) 0%, #059669 100%)';
            } else {
                verifyBtn.style.background = 'linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%)';
            }
        }

        // Form submission
        form.addEventListener('submit', function(e) {
            const allFilled = Array.from(inputs).every(input => input.value !== '');

            if (!allFilled) {
                e.preventDefault();
                alert('Please fill in all OTP digits.');
                return;
            }

            // Show loading state
            verifyBtn.disabled = true;
            verifyBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Verifying...';
        });

        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert-modern');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.transition = 'opacity 0.5s ease-out';
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 500);
            }, 5000);
        });

        // Initial check
        checkAllInputsFilled();
    });

    // Disable right-click and keyboard shortcuts for security
    document.addEventListener('contextmenu', e => e.preventDefault());
    document.addEventListener('keydown', function(e) {
        // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
        if (e.keyCode === 123 ||
            (e.ctrlKey && e.shiftKey && (e.keyCode === 73 || e.keyCode === 74)) ||
            (e.ctrlKey && e.keyCode === 85)) {
            e.preventDefault();
            return false;
        }
    });
</script>
{% endblock %}
