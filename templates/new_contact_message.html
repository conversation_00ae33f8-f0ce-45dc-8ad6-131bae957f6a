<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>New Contact Message | Librainian</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --success-color: #10b981;
            --text-color: #1e293b;
            --text-light: #6b7280;
            --text-white: #ffffff;
            --bg-light: #f8fafc;
            --border-color: #e2e8f0;
            --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 10px 20px rgba(0, 0, 0, 0.2);
            --border-radius: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            padding: 2rem 1rem;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        /* Email container with modern design */
        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            margin: 0 auto;
            max-width: 650px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideUp 0.8s ease-out;
        }

        /* Header section */
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            padding: 2.5rem 2rem;
            text-align: center;
            color: var(--text-white);
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            position: relative;
            z-index: 1;
        }

        /* Content section */
        .content {
            padding: 2.5rem 2rem;
            background: var(--text-white);
        }

        .content p {
            font-size: 1rem;
            line-height: 1.7;
            color: var(--text-color);
            margin-bottom: 1.5rem;
        }

        .content strong {
            color: var(--secondary-color);
            font-weight: 600;
        }

        /* Message highlight box */
        .message-box {
            background: rgba(59, 130, 246, 0.05);
            border: 1px solid rgba(59, 130, 246, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
            position: relative;
        }

        .message-box::before {
            content: '"';
            position: absolute;
            top: -10px;
            left: 20px;
            font-size: 3rem;
            color: var(--primary-color);
            font-weight: bold;
            background: var(--text-white);
            padding: 0 10px;
        }

        .message-box p {
            font-style: italic;
            color: var(--secondary-color);
            margin: 0;
            font-size: 1.1rem;
        }

        /* Contact info section */
        .contact-info {
            background: rgba(16, 185, 129, 0.05);
            border: 1px solid rgba(16, 185, 129, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .contact-info i {
            font-size: 1.5rem;
            color: var(--success-color);
            width: 2rem;
            text-align: center;
        }

        .contact-info p {
            margin: 0;
            font-weight: 500;
        }

        /* CTA Button */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: var(--text-white);
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1rem;
            transition: var(--transition);
            box-shadow: var(--shadow-sm);
            margin: 1.5rem 0;
        }

        .btn:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
            color: var(--text-white);
            text-decoration: none;
        }

        /* Footer section */
        .footer {
            text-align: center;
            padding: 2rem;
            background: rgba(0, 0, 0, 0.02);
            border-top: 1px solid var(--border-color);
            color: var(--text-light);
        }

        .footer p {
            margin: 0;
            font-size: 0.9rem;
        }

        .footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }

        .footer a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        /* Animations */
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            body {
                padding: 1rem 0.5rem;
            }

            .container {
                max-width: 100%;
                margin: 0;
                border-radius: 12px;
            }

            .header {
                padding: 2rem 1.5rem;
            }

            .header h1 {
                font-size: 1.6rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .content {
                padding: 2rem 1.5rem;
            }

            .message-box,
            .contact-info {
                padding: 1rem;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body>

    <div class="container">
        <!-- Email Header -->
        <div class="header">
            <h1>
                <i class="fas fa-envelope-open"></i>
                New Contact Message
            </h1>
        </div>

        <!-- Email Content -->
        <div class="content">
            <p>Hello <strong>{{ library_name }}</strong>,</p>

            <p>🎉 Great news! You have received a new contact message through your library's website. Here are the details:</p>

            <div class="contact-info">
                <i class="fas fa-user"></i>
                <div>
                    <p><strong>From:</strong> {{ name }}</p>
                </div>
            </div>

            <div class="contact-info">
                <i class="fas fa-envelope"></i>
                <div>
                    <p><strong>Email:</strong> {{ email }}</p>
                </div>
            </div>

            <div class="message-box">
                <p>{{ message }}</p>
            </div>

            <p>This message was sent through your library's contact form. We recommend responding promptly to maintain excellent customer service.</p>

            <div style="text-align: center;">
                <a href="mailto:{{ email }}?subject=Re: Your inquiry to {{ library_name }}" class="btn">
                    <i class="fas fa-reply"></i>
                    Reply to {{ name }}
                </a>
            </div>

            <p><strong>Best practices for responding:</strong></p>
            <ul style="color: var(--text-light); padding-left: 1.5rem;">
                <li>Respond within 24 hours when possible</li>
                <li>Address their specific questions or concerns</li>
                <li>Include your library's contact information</li>
                <li>Maintain a professional and friendly tone</li>
            </ul>
        </div>

        <!-- Email Footer -->
        <div class="footer">
            <p>
                <i class="fas fa-shield-alt me-2"></i>
                &copy; {% now "Y" %} <strong>{{ library_name }}</strong> | Powered by Librainian
            </p>
            <p style="margin-top: 0.5rem;">
                <a href="#">Manage Email Preferences</a> |
                <a href="#">Contact Support</a>
            </p>
        </div>
    </div>
</body>
</html>
