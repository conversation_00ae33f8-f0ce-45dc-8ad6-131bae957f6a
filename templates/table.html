{% extends "base.html" %}

{% block title %}Table - Librainian{% endblock %}

{% block page_title %}Fees Management{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Data Table</li>
{% endblock %}

{% block content %}
<div class="table-content fade-in">
    <!-- Alert Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert-glass alert-{{ message.tags|default:'info' }} alert-dismissible fade show mb-4" role="alert">
                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' or message.tags == 'danger' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-circle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close-glass" data-bs-dismiss="alert" aria-label="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Table Management Section -->
    <div class="row g-4">
        <!-- Desktop Table View -->
        <div class="col-12 d-none d-md-block">
            <div class="modern-card">
                <div class="modern-card-header">
                    <div class="row align-items-center">
                        <div class="col-md-8 col-12">
                        </div>
                        <div class="col-md-4 col-12 text-md-end">
                            <div class="table-actions">
                                <button class="btn-primary-modern" onclick="refreshTable()">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    Refresh
                                </button>
                                <button class="btn-secondary-modern" onclick="exportTable()">
                                    <i class="fas fa-download me-1"></i>
                                    Export
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modern-card-body">
                    <!-- DataTables will add controls here automatically -->
                    <div class="table-responsive">
                        <table id="invoiceTable" class="table-glass table-hover">
                            <thead>
                                <tr>
                                    <th>S.no</th>
                                    <th>Student Name</th>
                                    <th>Shift</th>
                                    <th>Month</th>
                                    <th>Issue Date</th>
                                    <th>Payment Status</th>
                                    <th>Amount</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for student_id, data in invoice_data.items %}
                                <tr class="table-row-clickable" onclick="window.location.href='/students/invoice_student/{{ data.invoice.slug }}'">
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <div class="student-info">
                                            <div class="student-name">{{ data.invoice.student.name }}</div>
                                            <div class="student-id">ID: {{ data.invoice.student.id }}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="shifts-list">
                                            {% for shift in data.shifts %}
                                                <span class="badge-glass shift-badge">{{ shift.name }}</span>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="months-list">
                                            {% for month in data.months %}
                                                <span class="badge-glass month-badge">{{ month.name }}</span>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="payment-date">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            {{ data.invoice.issue_date|date:"d/m/Y" }}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="status-badge status-{{ data.invoice.payment_status|lower }}">
                                            <i class="fas fa-{% if data.invoice.payment_status == 'Confirm' %}check-circle{% elif data.invoice.payment_status == 'Pending' %}clock{% else %}times-circle{% endif %} me-1"></i>
                                            {{ data.invoice.payment_status }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="amount-display">
                                            <span class="currency">₹</span>
                                            <span class="amount">{{ data.invoice.total_amount|default:"0" }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-action-view" onclick="event.stopPropagation(); viewInvoice('{{ data.invoice.slug }}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-action-delete" onclick="event.stopPropagation(); deleteInvoice('{{ data.invoice.slug }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="empty-state">
                                            <i class="fas fa-inbox fa-3x mb-3"></i>
                                            <h5>No Invoice Data Available</h5>
                                            <p class="text-muted">There are no invoices to display at the moment.</p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Card View -->
        {% for student_id, data in invoice_data.items %}
        <div class="col-12 d-md-none">
            <div class="mobile-invoice-card" onclick="window.location.href='/students/invoice_student/{{ data.invoice.slug }}'">
                <div class="mobile-card-header">
                    <div class="student-info-mobile">
                        <h6 class="student-name-mobile">{{ data.invoice.student.name }}</h6>
                    </div>
                    <div class="mobile-actions-header">
                        <button class="action-btn view-btn" onclick="event.stopPropagation(); window.location.href='/students/invoice_student/{{ data.invoice.slug }}'">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn delete-btn" onclick="event.stopPropagation(); confirmDelete('{{ data.invoice.slug }}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <div class="mobile-card-body">
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="mobile-field">
                                <label>Shifts</label>
                                <div class="mobile-badges">
                                    {% for shift in data.shifts %}
                                        <span class="badge-glass shift-badge">{{ shift.name }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mobile-field">
                                <label>Months</label>
                                <div class="mobile-badges">
                                    {% for month in data.months %}
                                        <span class="badge-glass month-badge">{{ month.name }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mobile-field">
                                <label>Issue Date</label>
                                <div class="mobile-value">
                                    <i class="fas fa-calendar-alt me-1"></i>
                                    {{ data.invoice.issue_date|date:"d/m/Y" }}
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mobile-field">
                                <label>Status</label>
                                <div class="mobile-value">
                                    <span class="status-badge status-{{ data.invoice.payment_status|lower }}">
                                        <i class="fas fa-{% if data.invoice.payment_status == 'Confirm' %}check-circle{% elif data.invoice.payment_status == 'Pending' %}clock{% else %}times-circle{% endif %} me-1"></i>
                                        {{ data.invoice.payment_status }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mobile-field">
                                <label>Amount</label>
                                <div class="mobile-value amount-display">
                                    <span class="currency">₹</span>
                                    <span class="amount">{{ data.invoice.total_amount|default:"0" }}</span>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12 d-md-none">
            <div class="empty-state-mobile">
                <i class="fas fa-inbox fa-3x mb-3"></i>
                <h5>No Invoice Data Available</h5>
                <p class="text-muted">There are no invoices to display at the moment.</p>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .table-content {
        min-height: calc(100vh - 160px);
        padding: 1rem;
    }

    .table-glass {
        background: transparent;
        color: white;
    }

    .table-glass th,
    .table-glass td {
        color: white;
        border-color: rgba(255, 255, 255, 0.1);
    }

    .table-row-clickable {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .table-row-clickable:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .mobile-invoice-card {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 1rem;
        margin-bottom: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .mobile-invoice-card:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
    }

    .badge-glass {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.5rem;
        font-size: 0.75rem;
        margin: 0.125rem;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .status-confirm {
        background: rgba(34, 197, 94, 0.2);
        color: #22c55e;
        border: 1px solid rgba(34, 197, 94, 0.3);
    }

    .status-pending {
        background: rgba(251, 191, 36, 0.2);
        color: #fbbf24;
        border: 1px solid rgba(251, 191, 36, 0.3);
    }

    .btn-action-view,
    .btn-action-delete {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 0.5rem;
        border-radius: 0.5rem;
        margin: 0 0.25rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-action-view:hover {
        background: rgba(59, 130, 246, 0.3);
        border-color: #3b82f6;
    }

    .btn-action-delete:hover {
        background: rgba(239, 68, 68, 0.3);
        border-color: #ef4444;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    function refreshTable() {
        location.reload();
    }

    function exportTable() {
        alert('Export functionality would be implemented here');
    }

    function viewInvoice(slug) {
        window.location.href = `/students/invoice_student/${slug}/`;
    }

    function deleteInvoice(slug) {
        if (confirm('Are you sure you want to delete this invoice?')) {
            // Delete functionality would be implemented here
            alert('Delete functionality would be implemented here');
        }
    }

    function confirmDelete(slug) {
        deleteInvoice(slug);
    }
</script>
{% endblock %}
