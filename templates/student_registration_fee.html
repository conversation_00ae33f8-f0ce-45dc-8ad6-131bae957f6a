{% extends "base.html" %}

{% block title %}Registration Fee - Librainian{% endblock %}

{% block extra_css %}

<style>
        /* Template-specific styles - CSS variables inherited from base.html */

        /* Glass Design Components */
        .glass-container {
            min-height: 100vh;
            padding: 2rem 1rem;
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .glass-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            text-align: center;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .glass-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255,255,255,0.1);
            transform: rotate(-45deg);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) rotate(-45deg); }
            50% { transform: translateX(100%) rotate(-45deg); }
        }

        .glass-header h3 {
            margin: 0;
            font-weight: 700;
            font-size: 1.75rem;
            position: relative;
            z-index: 1;
        }

        .glass-body {
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* Form Styling */
        .form-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 0.875rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.95);
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            outline: none;
        }

        .form-control:disabled {
            background: rgba(255, 255, 255, 0.5);
            color: var(--text-muted);
        }

        /* Fee Display */
        .fee-display {
            background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
            color: white;
            padding: 1.5rem;
            border-radius: 16px;
            text-align: center;
            margin: 1.5rem 0;
            box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
        }

        .fee-amount {
            font-size: 2.5rem;
            font-weight: 800;
            margin: 0;
        }

        .fee-label {
            font-size: 1rem;
            opacity: 0.9;
            margin: 0;
        }

        /* Action Buttons */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 12px;
            padding: 0.875rem 1.5rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(99, 102, 241, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            color: white;
            text-decoration: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
            border: none;
            border-radius: 12px;
            padding: 0.875rem 1.5rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
            background: linear-gradient(135deg, var(--secondary-dark), var(--secondary));
            color: white;
            text-decoration: none;
        }

        /* Student Info Card */
        .student-info {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .student-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .student-details {
            color: var(--text-secondary);
            font-size: 1rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .glass-container {
                padding: 1rem 0.5rem;
            }

            .glass-card {
                border-radius: 20px;
            }

            .glass-body {
                padding: 1.5rem;
            }

            .glass-header {
                padding: 1.5rem;
            }

            .glass-header h3 {
                font-size: 1.5rem;
            }

            .fee-amount {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .glass-body {
                padding: 1rem;
            }

            .fee-amount {
                font-size: 1.75rem;
            }

            .btn-primary, .btn-secondary {
                padding: 0.75rem 1rem;
                font-size: 0.875rem;
            }
        }

        /* Disable zoom on mobile */
        @media (max-width: 768px) {
            body {
                touch-action: pan-x pan-y;
            }
        }

    </style>
{% endblock %}

{% block content %}
<div class="registration-fee-content fade-in">
    <div class="glass-container">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="glass-card">
                        <div class="glass-header">
                            <h3><i class="fas fa-money-bill-wave me-2"></i>Registration Fee Payment</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Messages Section -->
            {% if messages %}
            <div class="row mb-4">
                <div class="col-12">
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Registration Fee Form -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="glass-card">
                        <div class="glass-header">
                            <h4><i class="fas fa-user-graduate me-2"></i>Student Registration Fee</h4>
                        </div>
                        <div class="glass-body">
                            <!-- Student Information -->
                            <div class="student-info">
                                <div class="student-name">{{ student.name }}</div>
                                <div class="student-details">
                                    <i class="fas fa-id-card me-2"></i>ID: {{ student.unique_id }} |
                                    <i class="fas fa-book me-2"></i>Course: {{ student.course }}
                                </div>
                            </div>

                            <!-- Fee Display -->
                            <div class="fee-display">
                                <p class="fee-label">Registration Fee Amount</p>
                                <h2 class="fee-amount">₹{{ student.registration_fee }}</h2>
                            </div>

                            <!-- Payment Form -->
                            <form method="post" class="needs-validation" novalidate>
                                {% csrf_token %}
                                <input type="hidden" name="student_id" value="{{ student.id }}">

                                <div class="mb-4">
                                    <label for="student_name" class="form-label">
                                        <i class="fas fa-user"></i>
                                        Student Name
                                    </label>
                                    <input type="text" class="form-control" id="student_name"
                                           value="{{ student.name }}" disabled>
                                </div>

                                <div class="mb-4">
                                    <label for="amount" class="form-label">
                                        <i class="fas fa-rupee-sign"></i>
                                        Amount
                                    </label>
                                    <input type="number" class="form-control" id="amount"
                                           value="{{ student.registration_fee }}" disabled>
                                </div>

                                <div class="mb-4">
                                    <label for="is_paid" class="form-label">
                                        <i class="fas fa-question-circle"></i>
                                        Have you paid the Student Registration Fee?
                                    </label>
                                    <select class="form-control" id="is_paid" name="is_paid" required>
                                        <option value="">Please select an option</option>
                                        <option value="True">Yes, I have paid</option>
                                        <option value="False">No, not yet paid</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a payment status.
                                    </div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="/students/" class="btn-secondary me-md-2">
                                        <i class="fas fa-arrow-left"></i>
                                        Back to Dashboard
                                    </a>
                                    <button type="submit" class="btn-primary">
                                        <i class="fas fa-check"></i>
                                        Submit Payment Status
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- JavaScript dependencies -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Modern functionality
        $(document).ready(function() {
            // Auto-hide alerts after 10 seconds
            $('.alert').each(function() {
                const alert = this;
                setTimeout(function() {
                    $(alert).fadeOut(1000);
                }, 10000);
            });

            // Form validation
            const form = document.querySelector('.needs-validation');
            if (form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                });
            }

            // Loading state for submit button
            $('form').on('submit', function() {
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Processing...');
                submitBtn.prop('disabled', true);

                // Re-enable after a delay (in case of validation errors)
                setTimeout(() => {
                    submitBtn.html(originalText);
                    submitBtn.prop('disabled', false);
                }, 3000);
            });

            // Enhanced select styling
            $('#is_paid').on('change', function() {
                const value = $(this).val();
                if (value === 'True') {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                } else if (value === 'False') {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                } else {
                    $(this).removeClass('is-valid is-invalid');
                }
            });

            // Smooth animations for cards
            $('.glass-card').each(function(index) {
                $(this).css('animation-delay', (index * 0.1) + 's');
            });
        });
    </script>
</div>
{% endblock %}