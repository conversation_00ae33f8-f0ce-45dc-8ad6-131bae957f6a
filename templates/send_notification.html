{% extends "base.html" %}

{% block title %}Send Notification - Librainian{% endblock %}

{% block extra_css %}
<!-- Firebase SDK v8.x.x -->
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-messaging.js"></script>

    <style>
        /* Template-specific styles - CSS variables inherited from base.html */

        /* Glass Design Components */
        .glass-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
            position: relative;
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            overflow: hidden;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .glass-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            text-align: center;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .glass-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255,255,255,0.1);
            transform: rotate(-45deg);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) rotate(-45deg); }
            50% { transform: translateX(100%) rotate(-45deg); }
        }

        .glass-header h4 {
            margin: 0 0 1rem 0;
            font-weight: 700;
            font-size: 1.75rem;
            position: relative;
            z-index: 1;
        }

        .notification-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .notification-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            transform: translateY(-2px);
        }

        .glass-body {
            padding: 2.5rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            font-size: 0.95rem;
        }

        .form-label i {
            color: var(--primary);
            font-size: 1rem;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 0.875rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            outline: none;
            background: rgba(255, 255, 255, 0.95);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        .form-check {
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .form-check-input {
            margin-right: 0.75rem;
        }

        .form-check-label {
            color: var(--text-primary);
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        /* Button Styles */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            color: white;
        }

        /* Messages */
        .messages-container {
            margin-top: 2rem;
        }

        .alert {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .glass-container {
                padding: 1rem 0.5rem;
            }

            .glass-card {
                border-radius: 20px;
            }

            .glass-body {
                padding: 2rem 1.5rem;
            }

            .glass-header {
                padding: 1.5rem;
            }

            .glass-header h4 {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .glass-body {
                padding: 1.5rem 1rem;
            }

            .form-control, .form-select {
                padding: 0.75rem;
            }
        }

        /* Disable zoom on mobile */
        @media (max-width: 768px) {
            body {
                touch-action: pan-x pan-y;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="send-notification-content fade-in">
    <div class="glass-container">
        <div class="glass-card">
            <div class="glass-header">
                <h4><i class="fas fa-bell me-2"></i>Send Notification</h4>
                <button id="notificationBtn" class="notification-btn">
                    <i class="fas fa-bell me-2"></i>Enable Notifications
                </button>
            </div>
            <div class="glass-body">
                <form method="post" action="" class="notification-form">
                    {% csrf_token %}

                    <div class="form-group mb-4">
                        <label for="title" class="form-label">
                            <i class="fas fa-heading me-2"></i>
                            Notification Title
                        </label>
                        <input type="text" class="form-control" id="title" name="title" placeholder="Enter notification title" required>
                    </div>

                    <div class="form-group mb-4">
                        <label for="body" class="form-label">
                            <i class="fas fa-align-left me-2"></i>
                            Message Body
                        </label>
                        <textarea class="form-control" id="body" name="body" rows="4" placeholder="Enter notification message" required></textarea>
                    </div>

                    <div class="form-group mb-4">
                        <label for="user" class="form-label">
                            <i class="fas fa-user me-2"></i>
                            Select User
                        </label>
                        <select class="form-select" id="user" name="user">
                            <option value="">-- Select User --</option>
                            {% for user in users %}
                                <option value="{{ user.id }}">{{ user.username }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="sendToAll" name="send_to_all">
                        <label class="form-check-label" for="sendToAll">
                            <i class="fas fa-users me-2"></i>
                            Send to all users
                        </label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>
                            Send Notification
                        </button>
                    </div>
                </form>
                    </div>
                {% if messages %}
                <div class="messages-container">
                    {% for message in messages %}
                        <div class="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Bootstrap 5.3.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Firebase Configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDw-BI4MFB1PKezXYPIZyee8IizHw2QDjI",
            authDomain: "librainian-app.firebaseapp.com",
            projectId: "librainian-app",
            storageBucket: "librainian-app.firebasestorage.app",
            messagingSenderId: "623132670328",
            appId: "1:623132670328:web:982c79e13e3cc69f3df08d",
            measurementId: "G-XNDKJL6JWH"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const messaging = firebase.messaging();

        // Modern notification handling
        document.addEventListener('DOMContentLoaded', function() {
            const notificationBtn = document.getElementById('notificationBtn');

            // Check if notifications are already enabled
            if (Notification.permission === 'granted') {
                notificationBtn.innerHTML = '<i class="fas fa-check me-2"></i>Notifications Enabled';
                notificationBtn.disabled = true;
                notificationBtn.style.background = 'rgba(34, 197, 94, 0.3)';
                registerDeviceToken();
            } else {
                notificationBtn.addEventListener('click', requestAndShowNotification);
            }

            if (!('Notification' in window)) {
                notificationBtn.innerHTML = '<i class="fas fa-times me-2"></i>Not Supported';
                notificationBtn.disabled = true;
                console.log('This browser does not support notifications.');
            }

            // Form validation
            const form = document.querySelector('.notification-form');
            const sendToAllCheckbox = document.getElementById('sendToAll');
            const userSelect = document.getElementById('user');

            sendToAllCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    userSelect.disabled = true;
                    userSelect.value = '';
                } else {
                    userSelect.disabled = false;
                }
            });

            form.addEventListener('submit', function(e) {
                if (!sendToAllCheckbox.checked && !userSelect.value) {
                    e.preventDefault();
                    alert('Please select a user or choose to send to all users.');
                }
            });
        });

        function requestAndShowNotification() {
            const notificationBtn = document.getElementById('notificationBtn');

            Notification.requestPermission().then(function(permission) {
                if (permission === 'granted') {
                    notificationBtn.innerHTML = '<i class="fas fa-check me-2"></i>Notifications Enabled';
                    notificationBtn.disabled = true;
                    notificationBtn.style.background = 'rgba(34, 197, 94, 0.3)';
                    showNotification();
                    registerDeviceToken();
                } else {
                    notificationBtn.innerHTML = '<i class="fas fa-times me-2"></i>Permission Denied';
                    notificationBtn.disabled = true;
                    notificationBtn.style.background = 'rgba(239, 68, 68, 0.3)';
                    console.log('Notification permission denied');
                }
            });
        }

        function showNotification() {
            const options = {
                body: 'Welcome to Librainian! Your CRM tool for libraries and study centers.',
                icon: '/static/img/librainian-logo-black-transparent.png',
                badge: '/static/img/librainian-logo-black-transparent.png'
            };

            new Notification('Librainian Notification', options);
        }

        function registerDeviceToken() {
            messaging.getToken({ vapidKey: 'BGX2t3YXGQdWxsYD_V3WpvlzCvwS7Ob0b9oGq0Thsg_OLHs22urXWd_CKv4QBdBVJNkZJJHq-QG8zB2p4qPYdrM' }).then((currentToken) => {
                if (currentToken) {
                    const deviceType = 'web'; // Change this based on the actual device type
                    saveDeviceToken(currentToken, deviceType);
                } else {
                    console.log('No registration token available. Request permission to generate one.');
                }
            }).catch((err) => {
                console.log('An error occurred while retrieving token. ', err);
            });
        }

        function saveDeviceToken(token, deviceType) {
            fetch('/save-device-token/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': getCookie('csrftoken') // Get CSRF token for security
                },
                body: `token=${token}&device_type=${deviceType}`
            })
            .then(response => response.json())
            .then(data => {
                console.log(data.message);
                console.log('Device Token:', token); // Log the device token to the console
            })
            .catch(error => {
                console.error('Error saving device token:', error);
            });
        }

        // Function to get CSRF token from cookies
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    </script>

    <script>
        // Register the service worker
        if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/firebase-messaging-sw.js')
            .then(function(registration) {
            console.log('Service Worker registered with scope:', registration.scope);
            })
            .catch(function(error) {
            console.log('Service Worker registration failed:', error);
            });
        }

    </script>
</div>
</div>
{% endblock %}
