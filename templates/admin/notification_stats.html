{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div class="module">
    <h1>{{ title }}</h1>
    
    <div class="stats-grid">
        <div class="stat-card">
            <h3>Total Notifications Sent</h3>
            <div class="stat-number">{{ stats.total_sent }}</div>
        </div>
        
        <div class="stat-card">
            <h3>Sent Last 30 Days</h3>
            <div class="stat-number">{{ stats.sent_last_30_days }}</div>
        </div>
        
        <div class="stat-card">
            <h3>Successful Deliveries</h3>
            <div class="stat-number">{{ stats.successful_deliveries }}</div>
        </div>
        
        <div class="stat-card">
            <h3>Failed Deliveries</h3>
            <div class="stat-number">{{ stats.failed_deliveries }}</div>
        </div>
        
        <div class="stat-card">
            <h3>Clicked Notifications</h3>
            <div class="stat-number">{{ stats.clicked_notifications }}</div>
        </div>
        
        <div class="stat-card">
            <h3>Click Rate</h3>
            <div class="stat-number">
                {% if stats.total_sent > 0 %}
                    {{ stats.clicked_notifications|floatformat:0 }}%
                {% else %}
                    0%
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="charts-grid">
        <div class="chart-section">
            <h2>Notifications by Category</h2>
            <table class="stats-table">
                <thead>
                    <tr>
                        <th>Category</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    {% for category in category_stats %}
                    <tr>
                        <td>{{ category.template__category__name|default:"Unknown" }}</td>
                        <td>{{ category.count }}</td>
                        <td>
                            {% if stats.total_sent > 0 %}
                                {% widthratio category.count stats.total_sent 100 %}%
                            {% else %}
                                0%
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="3">No data available</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="chart-section">
            <h2>Notifications by Event Type</h2>
            <table class="stats-table">
                <thead>
                    <tr>
                        <th>Event Type</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    {% for event in event_stats %}
                    <tr>
                        <td>{{ event.template__event_type|title }}</td>
                        <td>{{ event.count }}</td>
                        <td>
                            {% if stats.total_sent > 0 %}
                                {% widthratio event.count stats.total_sent 100 %}%
                            {% else %}
                                0%
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="3">No data available</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="module">
        <h2>Recent Notifications</h2>
        <table class="recent-notifications-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Recipient</th>
                    <th>Title</th>
                    <th>Event Type</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for notification in recent_notifications %}
                <tr>
                    <td>{{ notification.sent_at|date:"M d, Y H:i" }}</td>
                    <td>{{ notification.recipient.username }}</td>
                    <td>{{ notification.title|truncatechars:50 }}</td>
                    <td>{{ notification.template.get_event_type_display }}</td>
                    <td>
                        <span class="status-badge status-{{ notification.status }}">
                            {{ notification.get_status_display }}
                        </span>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5">No notifications found</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.stat-card h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #495057;
}

.charts-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-section h2 {
    margin-bottom: 15px;
    color: #495057;
}

.stats-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-table th,
.stats-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.stats-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.recent-notifications-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.recent-notifications-table th,
.recent-notifications-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.recent-notifications-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-sent {
    background: #d4edda;
    color: #155724;
}

.status-delivered {
    background: #d1ecf1;
    color: #0c5460;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.status-clicked {
    background: #e2e3e5;
    color: #383d41;
}

@media (max-width: 768px) {
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}
</style>
{% endblock %}
