{% extends "base.html" %}

{% block title %}Mark Invoice Complete - {{ student.name }}{% endblock %}

{% block extra_css %}
<style>
    .complete-container {
        max-width: 600px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .complete-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
    }

    .complete-header {
        text-align: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f3f4f6;
    }

    .complete-header h1 {
        color: #059669;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .invoice-info {
        background: #f9fafb;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e5e7eb;
    }

    .info-row:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #374151;
    }

    .info-value {
        color: #1f2937;
        font-weight: 500;
    }

    .amount-due {
        color: #dc2626;
        font-weight: 700;
        font-size: 1.1rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #d1d5db;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: #059669;
        box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
    }

    .btn-group {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }

    .btn {
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-success {
        background: #059669;
        color: white;
    }

    .btn-success:hover {
        background: #047857;
        transform: translateY(-1px);
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
    }

    .btn-secondary:hover {
        background: #4b5563;
        transform: translateY(-1px);
    }

    @media (max-width: 768px) {
        .complete-container {
            margin: 1rem auto;
            padding: 0 0.5rem;
        }

        .complete-card {
            padding: 1.5rem;
        }

        .btn-group {
            flex-direction: column;
        }

        .btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="complete-container">
    <div class="complete-card">
        <!-- Header -->
        <div class="complete-header">
            <h1><i class="fas fa-check-circle me-2"></i>Mark Invoice Complete</h1>
            <p class="text-muted">Complete the payment for {{ student.name }}</p>
        </div>

        <!-- Invoice Information -->
        <div class="invoice-info">
            <div class="info-row">
                <span class="info-label">Student Name:</span>
                <span class="info-value">{{ student.name }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Invoice ID:</span>
                <span class="info-value">#{{ invoice.invoice_id }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Total Amount:</span>
                <span class="info-value">₹{{ invoice.total_amount|floatformat:0 }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Amount Paid:</span>
                <span class="info-value">₹{{ invoice.total_paid|floatformat:0 }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Amount Due:</span>
                <span class="info-value amount-due">₹{{ invoice.remaining_due|floatformat:0 }}</span>
            </div>
        </div>

        <!-- Payment Form -->
        <form method="POST" action="{% url 'mark_invoice_complete' invoice.slug %}">
            {% csrf_token %}
            
            <div class="form-group">
                <label for="payment_mode" class="form-label">Payment Mode *</label>
                <select class="form-control" id="payment_mode" name="payment_mode" required>
                    <option value="">Select payment mode</option>
                    <option value="Cash">Cash</option>
                    <option value="UPI">UPI</option>
                    <option value="Card">Card</option>
                    <option value="Bank Transfer">Bank Transfer</option>
                    <option value="Cheque">Cheque</option>
                    <option value="Online">Online</option>
                </select>
            </div>

            <div class="btn-group">
                <a href="{% url 'partial_payments_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>Cancel
                </a>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-check"></i>Mark Complete
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
