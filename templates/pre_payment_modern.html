<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Complete your Librainian membership checkout - Modern, secure, and fast payment processing">
    <meta name="theme-color" content="#6366f1">
    
    <title>Checkout - {{ plan.name }} Plan | Librainian</title>
    
    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">

    <!-- Import master CSS variables from code_temp.html -->
    {% include "code_temp.html" %}

    <style>
        :root {
            /* Pre-payment modern specific overrides only */
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Comfortaa', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: var(--gray-900);
            font-size: 16px;
        }

        /* Layout Components */
        .checkout-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .checkout-header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem 0;
        }

        .checkout-title {
            font-family: 'Comfortaa', sans-serif;
            font-weight: 800;
            font-size: clamp(2rem, 5vw, 3.5rem);
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            line-height: 1.2;
        }

        .checkout-subtitle {
            font-size: 1.125rem;
            color: var(--gray-600);
            font-weight: 500;
        }

        .checkout-grid {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 3rem;
            flex: 1;
            align-items: start;
        }

        /* Card Components */
        .modern-card {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
            overflow: hidden;
            transition: var(--transition-slow);
        }

        .modern-card:hover {
            box-shadow: var(--shadow-xl);
            transform: translateY(-2px);
        }

        .card-header-modern {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .card-header-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .card-header-modern:hover::before {
            transform: translateX(100%);
        }

        .card-title {
            font-family: 'Comfortaa', sans-serif;
            font-weight: 700;
            font-size: 1.5rem;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .card-body-modern {
            padding: 2rem;
        }

        /* Plan Display */
        .plan-showcase {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: var(--gray-50);
            border-radius: var(--border-radius);
            border: 2px solid var(--gray-200);
        }

        .plan-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            flex-shrink: 0;
        }

        .plan-details h3 {
            font-family: 'Comfortaa', sans-serif;
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--gray-900);
            margin: 0 0 0.25rem 0;
        }

        .plan-price {
            font-size: 2rem;
            font-weight: 800;
            color: var(--primary);
            margin: 0;
        }

        .plan-period {
            font-size: 0.875rem;
            color: var(--gray-500);
            font-weight: 500;
        }

        /* Features List */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            background: white;
            border-radius: var(--border-radius-sm);
            border: 1px solid var(--gray-200);
            transition: var(--transition);
        }

        .feature-item:hover {
            border-color: var(--primary);
            box-shadow: var(--shadow-md);
        }

        .feature-icon {
            width: 24px;
            height: 24px;
            background: var(--success);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
            flex-shrink: 0;
        }

        .feature-text {
            font-weight: 500;
            color: var(--gray-700);
        }

        /* Form Components */
        .form-group-modern {
            margin-bottom: 1.5rem;
        }

        .form-label-modern {
            display: block;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .form-control-modern {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 500;
            background: white;
            transition: var(--transition);
            appearance: none;
        }

        .form-control-modern:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
        }

        .form-select-modern {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.75rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 3rem;
        }

        /* Button Components */
        .btn-modern {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: 1rem;
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            min-height: 56px;
        }

        .btn-primary-modern {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: white;
        }

        .btn-secondary-modern {
            background: var(--gray-100);
            color: var(--gray-700);
            border: 2px solid var(--gray-200);
        }

        .btn-secondary-modern:hover {
            background: var(--gray-200);
            border-color: var(--gray-300);
        }

        .btn-success-modern {
            background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-success-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: white;
        }

        /* Loading State */
        .btn-loading {
            color: transparent;
            pointer-events: none;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        @keyframes spin {
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .checkout-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
        }

        @media (max-width: 768px) {
            .checkout-container {
                padding: 1rem;
            }
            
            .card-body-modern {
                padding: 1.5rem;
            }
            
            .plan-showcase {
                flex-direction: column;
                text-align: center;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="checkout-container">
        <!-- Header -->
        <header class="checkout-header">
            <h1 class="checkout-title">Complete Your Purchase</h1>
            <p class="checkout-subtitle">Secure checkout for your {{ plan.name }} membership</p>
        </header>

        <!-- Main Content Grid -->
        <div class="checkout-grid">
            <!-- Left Column - Plan Details & Configuration -->
            <div class="checkout-main">
                <!-- Plan Showcase -->
                <div class="modern-card">
                    <div class="card-header-modern">
                        <h2 class="card-title">
                            <i class="fas fa-crown me-2"></i>
                            {{ plan.name }} Plan
                        </h2>
                    </div>
                    <div class="card-body-modern">
                        <div class="plan-showcase">
                            <div class="plan-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="plan-details">
                                <h3>{{ plan.name }}</h3>
                                <div class="plan-price">
                                    ₹<span id="unit-price">{{ plan.discount_price|floatformat:0 }}</span>
                                    <span class="plan-period">per month</span>
                                </div>
                            </div>
                        </div>

                        <!-- Features Grid -->
                        <div class="features-grid">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-check"></i>
                                </div>
                                <span class="feature-text">{{ plan.description_line_01 }}</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-check"></i>
                                </div>
                                <span class="feature-text">{{ plan.description_line_02 }}</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-check"></i>
                                </div>
                                <span class="feature-text">{{ plan.description_line_03 }}</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-check"></i>
                                </div>
                                <span class="feature-text">{{ plan.description_line_04 }}</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-check"></i>
                                </div>
                                <span class="feature-text">{{ plan.description_line_05 }}</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-sms"></i>
                                </div>
                                <span class="feature-text">{{ plan.sms_quantity }} SMS Credits</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Duration Selection -->
                <div class="modern-card mt-4">
                    <div class="card-header-modern">
                        <h2 class="card-title">
                            <i class="fas fa-calendar-alt me-2"></i>
                            Choose Duration
                        </h2>
                    </div>
                    <div class="card-body-modern">
                        <div class="form-group-modern">
                            <label for="num-months" class="form-label-modern">Subscription Duration</label>
                            <select id="num-months" class="form-control-modern form-select-modern" onchange="updateTotal()" name="num_months">
                                <option value="1" {% if num_months == 1 %}selected{% endif %}>1 Month</option>
                                <option value="2" {% if num_months == 2 %}selected{% endif %}>2 Months</option>
                                <option value="3" {% if num_months == 3 %}selected{% endif %}>3 Months - Save 5%</option>
                                <option value="4" {% if num_months == 4 %}selected{% endif %}>4 Months</option>
                                <option value="5" {% if num_months == 5 %}selected{% endif %}>5 Months</option>
                                <option value="6" {% if num_months == 6 %}selected{% endif %}>6 Months - Save 10%</option>
                                <option value="7" {% if num_months == 7 %}selected{% endif %}>7 Months</option>
                                <option value="8" {% if num_months == 8 %}selected{% endif %}>8 Months</option>
                                <option value="9" {% if num_months == 9 %}selected{% endif %}>9 Months</option>
                                <option value="10" {% if num_months == 10 %}selected{% endif %}>10 Months</option>
                                <option value="11" {% if num_months == 11 %}selected{% endif %}>11 Months</option>
                                <option value="12" {% if num_months == 12 %}selected{% endif %}>12 Months - Save 20%</option>
                            </select>
                        </div>

                        <!-- Discount Info -->
                        <div class="alert alert-info d-flex align-items-center" style="background: rgba(99, 102, 241, 0.1); border: 1px solid rgba(99, 102, 241, 0.2); border-radius: var(--border-radius);">
                            <i class="fas fa-info-circle me-2" style="color: var(--primary);"></i>
                            <small>Save more with longer subscriptions! Get up to 20% off with annual plans.</small>
                        </div>
                    </div>
                </div>

                <!-- Coupon Section -->
                <div class="modern-card mt-4">
                    <div class="card-body-modern">
                        <div class="coupon-section">
                            <div class="coupon-trigger" onclick="toggleCouponForm()">
                                <i class="fas fa-tag me-2"></i>
                                <span>Have a coupon code?</span>
                                <i class="fas fa-chevron-down ms-auto" id="coupon-chevron"></i>
                            </div>
                            <div class="coupon-form-container" id="coupon-form-container">
                                <div class="coupon-form-card">
                                    <div class="form-group-modern">
                                        <label for="coupon-code" class="form-label-modern">Coupon Code</label>
                                        <div class="input-group">
                                            <input type="text" id="coupon-code" class="form-control-modern" placeholder="Enter coupon code" style="border-radius: var(--border-radius) 0 0 var(--border-radius);">
                                            <button type="button" class="btn-primary-modern" onclick="applyCoupon()" id="apply-coupon-btn" style="border-radius: 0 var(--border-radius) var(--border-radius) 0;">
                                                <i class="fas fa-check me-1"></i>Apply
                                            </button>
                                        </div>
                                    </div>
                                    <div id="coupon-message" class="mt-2"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Order Summary & Payment -->
            <div class="checkout-sidebar">
                <!-- Order Summary -->
                <div class="modern-card">
                    <div class="card-header-modern">
                        <h2 class="card-title">
                            <i class="fas fa-receipt me-2"></i>
                            Order Summary
                        </h2>
                    </div>
                    <div class="card-body-modern">
                        <!-- Cart Items -->
                        <div class="cart-item mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <h6 class="mb-1 fw-bold">{{ plan.name }} Plan</h6>
                                    <small class="text-muted">Duration: <span id="cart-duration">{{ num_months }} month{{ num_months|pluralize }}</span></small>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold">₹<span id="total-price">{{ final_amount|floatformat:0 }}</span></div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-3">

                        <!-- Pricing Breakdown -->
                        <div class="pricing-breakdown">
                            <!-- Subtotal -->
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">
                                    <i class="fas fa-calculator me-1"></i>
                                    Subtotal:
                                </span>
                                <span class="fw-semibold">₹<span id="total-amount">{{ base_amount|floatformat:0 }}</span></span>
                            </div>

                            <!-- Duration Discount -->
                            <div class="d-flex justify-content-between align-items-center mb-2" id="discount-row" {% if discount_amount == 0 %}style="display: none !important;"{% endif %}>
                                <span class="text-success">
                                    <i class="fas fa-percent me-1"></i>
                                    Duration Discount ({{ discount_percentage }}%):
                                </span>
                                <span class="text-success fw-semibold">-₹<span id="discount-amount">{{ discount_amount|floatformat:0 }}</span></span>
                            </div>

                            <!-- Coupon Discount -->
                            <div class="d-flex justify-content-between align-items-center mb-2" id="coupon-discount-row" style="display: none;">
                                <span class="text-success">
                                    <i class="fas fa-tag me-1"></i>
                                    Coupon Discount:
                                </span>
                                <span class="text-success fw-semibold">-₹<span id="coupon-discount-amount">0</span></span>
                            </div>

                            <hr class="my-3">

                            <!-- Grand Total -->
                            <div class="d-flex justify-content-between align-items-center mb-4" style="background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%); color: white; padding: 1rem; border-radius: var(--border-radius); margin: 0 -1rem;">
                                <span class="fw-bold fs-5">
                                    <i class="fas fa-rupee-sign me-1"></i>
                                    Total:
                                </span>
                                <span class="fw-bold fs-4">₹<span id="grand-total-display">{{ final_amount|floatformat:0 }}</span></span>
                            </div>
                        </div>

                        <!-- Payment Button -->
                        <button id="pay-button" type="button" class="btn-success-modern w-100 mb-3" onclick="initiatePayment()">
                            <i class="fas fa-lock me-2"></i>
                            Pay Securely
                            <i class="fas fa-arrow-right ms-2"></i>
                        </button>

                        <!-- Security Badges -->
                        <div class="security-info text-center">
                            <div class="d-flex justify-content-center align-items-center gap-3 mb-2">
                                <i class="fab fa-cc-visa text-primary fs-4"></i>
                                <i class="fab fa-cc-mastercard text-warning fs-4"></i>
                                <i class="fab fa-cc-amex text-info fs-4"></i>
                                <i class="fas fa-university text-success fs-4"></i>
                                <i class="fas fa-mobile-alt text-secondary fs-4"></i>
                            </div>
                            <small class="text-muted d-flex align-items-center justify-content-center">
                                <i class="fas fa-shield-alt me-1 text-success"></i>
                                Secured by 256-bit SSL encryption
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Trust Indicators -->
                <div class="modern-card mt-4">
                    <div class="card-body-modern">
                        <h6 class="fw-bold mb-3">
                            <i class="fas fa-shield-check me-2 text-success"></i>
                            Why Choose Us?
                        </h6>
                        <div class="trust-indicators">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <small>Instant activation</small>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-undo text-primary me-2"></i>
                                <small>30-day money back guarantee</small>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-headset text-info me-2"></i>
                                <small>24/7 customer support</small>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-award text-warning me-2"></i>
                                <small>99.9% uptime guarantee</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hidden Payment Form -->
        <form action="{% url 'verify_payment' %}" method="post" id="payment-form" style="display: none;">
            {% csrf_token %}
            <input type="hidden" name="razorpay_order_id" id="razorpay_order_id">
            <input type="hidden" name="razorpay_payment_id" id="razorpay_payment_id">
            <input type="hidden" name="razorpay_signature" id="razorpay_signature">
            <input type="hidden" name="plan_id" value="{{ plan.id }}">
            <input type="hidden" name="num_months" id="num-months-hidden" value="{{ num_months }}">
            <input type="hidden" name="coupon_code" id="coupon-code-hidden" value="">
        </form>
    </div>

    <!-- Bootstrap 5.3.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Razorpay JS -->
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>

    <script>
        // Modern JavaScript with ES6+ features
        class CheckoutManager {
            constructor() {
                this.unitPrice = parseFloat(document.getElementById('unit-price').textContent) || 0;
                this.currentMonths = parseInt(document.getElementById('num-months').value) || 1;
                this.couponApplied = false;
                this.init();
            }

            init() {
                this.updateTotal();
                this.bindEvents();
                console.log('Checkout Manager initialized');
            }

            bindEvents() {
                // Month selection change
                const monthsSelect = document.getElementById('num-months');
                monthsSelect?.addEventListener('change', () => {
                    this.currentMonths = parseInt(monthsSelect.value) || 1;
                    this.updateTotal();
                    this.addVisualFeedback(monthsSelect);
                });
            }

            addVisualFeedback(element) {
                element.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 150);
            }

            updateTotal() {
                console.log('Updating total calculation...');

                // Calculate subtotal
                const subtotal = this.unitPrice * this.currentMonths;

                // Calculate duration discount
                let discountPercentage = 0;
                switch(this.currentMonths) {
                    case 3: discountPercentage = 5; break;
                    case 6: discountPercentage = 10; break;
                    case 12: discountPercentage = 20; break;
                }

                const durationDiscountAmount = Math.round(subtotal * discountPercentage / 100);
                let finalPrice = subtotal - durationDiscountAmount;

                // Apply existing coupon discount
                const couponDiscountRow = document.getElementById('coupon-discount-row');
                let couponDiscountAmount = 0;
                if (couponDiscountRow && couponDiscountRow.style.display !== 'none') {
                    couponDiscountAmount = parseFloat(document.getElementById('coupon-discount-amount').textContent) || 0;
                    finalPrice = Math.max(0, finalPrice - couponDiscountAmount);
                }

                // Update displays with smooth animations
                this.animateValue('total-amount', subtotal);
                this.animateValue('grand-total-display', finalPrice);
                this.animateValue('total-price', finalPrice);

                // Update duration discount display
                this.updateDiscountDisplay(discountPercentage, durationDiscountAmount);

                // Update cart duration
                const cartDuration = document.getElementById('cart-duration');
                if (cartDuration) {
                    cartDuration.textContent = `${this.currentMonths} month${this.currentMonths > 1 ? 's' : ''}`;
                }

                // Update hidden form values
                document.getElementById('num-months-hidden').value = this.currentMonths;

                console.log(`Calculation: ${this.unitPrice} × ${this.currentMonths} = ${subtotal}, Discount: ${durationDiscountAmount}, Final: ${finalPrice}`);
            }

            updateDiscountDisplay(percentage, amount) {
                const discountRow = document.getElementById('discount-row');
                const discountAmountSpan = document.getElementById('discount-amount');
                const discountLabel = discountRow?.querySelector('span');

                if (percentage > 0 && amount > 0) {
                    if (discountRow) discountRow.style.display = 'flex';
                    if (discountAmountSpan) discountAmountSpan.textContent = amount;
                    if (discountLabel) {
                        discountLabel.innerHTML = `<i class="fas fa-percent me-1"></i>Duration Discount (${percentage}%):`;
                    }
                } else {
                    if (discountRow) discountRow.style.display = 'none';
                }
            }

            animateValue(elementId, targetValue) {
                const element = document.getElementById(elementId);
                if (!element) return;

                const currentValue = parseFloat(element.textContent) || 0;
                const difference = targetValue - currentValue;
                const duration = 300;
                const steps = 20;
                const stepValue = difference / steps;
                const stepDuration = duration / steps;

                let currentStep = 0;
                const timer = setInterval(() => {
                    currentStep++;
                    const newValue = currentValue + (stepValue * currentStep);
                    element.textContent = Math.round(newValue);

                    if (currentStep >= steps) {
                        clearInterval(timer);
                        element.textContent = Math.round(targetValue);
                    }
                }, stepDuration);
            }

            async applyCoupon(couponCode) {
                if (!couponCode.trim()) {
                    this.showCouponMessage('Please enter a coupon code', 'error');
                    return;
                }

                const applyBtn = document.getElementById('apply-coupon-btn');
                const originalText = applyBtn.innerHTML;

                // Show loading state
                applyBtn.classList.add('btn-loading');
                applyBtn.disabled = true;

                try {
                    const response = await fetch('/membership/validate-coupon', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                        },
                        body: new URLSearchParams({ 'coupon_code': couponCode })
                    });

                    const data = await response.json();

                    if (data.valid) {
                        this.applyCouponDiscount(data, couponCode);
                        this.showCouponMessage(`Coupon applied! You saved ₹${this.calculateCouponDiscount(data)}`, 'success');

                        // Disable further applications
                        document.getElementById('coupon-code').disabled = true;
                        applyBtn.innerHTML = '<i class="fas fa-check me-1"></i>Applied';
                        applyBtn.classList.remove('btn-primary-modern');
                        applyBtn.classList.add('btn-success-modern');
                        this.couponApplied = true;
                    } else {
                        this.showCouponMessage(data.message || 'Invalid coupon code', 'error');
                    }
                } catch (error) {
                    console.error('Coupon validation error:', error);
                    this.showCouponMessage('Error validating coupon. Please try again.', 'error');
                } finally {
                    if (!this.couponApplied) {
                        applyBtn.classList.remove('btn-loading');
                        applyBtn.disabled = false;
                        applyBtn.innerHTML = originalText;
                    }
                }
            }

            calculateCouponDiscount(data) {
                const subtotal = parseFloat(document.getElementById('total-amount').textContent);
                if (data.discount_type === 'percentage') {
                    return Math.round(subtotal * parseFloat(data.discount) / 100);
                } else {
                    return parseFloat(data.discount);
                }
            }

            applyCouponDiscount(data, couponCode) {
                const couponDiscountAmount = this.calculateCouponDiscount(data);
                const currentGrandTotal = parseFloat(document.getElementById('grand-total-display').textContent);
                const newGrandTotal = Math.max(0, currentGrandTotal - couponDiscountAmount);

                // Show coupon discount row
                const couponDiscountRow = document.getElementById('coupon-discount-row');
                const couponDiscountAmountSpan = document.getElementById('coupon-discount-amount');

                if (couponDiscountRow) couponDiscountRow.style.display = 'flex';
                if (couponDiscountAmountSpan) couponDiscountAmountSpan.textContent = couponDiscountAmount;

                // Update totals
                this.animateValue('grand-total-display', newGrandTotal);
                this.animateValue('total-price', newGrandTotal);

                // Update hidden form field
                document.getElementById('coupon-code-hidden').value = couponCode;
            }

            showCouponMessage(message, type) {
                const messageDiv = document.getElementById('coupon-message');
                if (!messageDiv) return;

                const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';

                messageDiv.innerHTML = `
                    <div class="alert ${alertClass} d-flex align-items-center" style="border-radius: var(--border-radius-sm);">
                        <i class="fas fa-${icon} me-2"></i>
                        ${message}
                    </div>
                `;

                // Auto-hide success messages
                if (type === 'success') {
                    setTimeout(() => {
                        messageDiv.innerHTML = '';
                    }, 5000);
                }
            }

            async initiatePayment() {
                const payButton = document.getElementById('pay-button');
                const grandTotal = parseFloat(document.getElementById('grand-total-display').textContent) || 0;
                const couponCode = document.getElementById('coupon-code-hidden').value || '';

                // Show loading state
                payButton.classList.add('btn-loading');
                payButton.disabled = true;

                try {
                    const response = await fetch(`/membership/create_order/{{ plan.id }}/`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                        },
                        body: new URLSearchParams({
                            'plan_id': '{{ plan.id }}',
                            'num_months': this.currentMonths,
                            'grand_total': grandTotal,
                            'coupon_code': couponCode
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.openRazorpay(data);
                    } else {
                        throw new Error(data.message || 'Failed to create payment order');
                    }
                } catch (error) {
                    console.error('Payment initiation error:', error);
                    alert('Error initiating payment: ' + error.message);
                    this.resetPayButton();
                }
            }

            openRazorpay(orderData) {
                const options = {
                    "key": orderData.razorpay_key,
                    "amount": orderData.amount,
                    "currency": orderData.currency,
                    "name": "Librainian",
                    "description": `${orderData.plan_name} - ${this.currentMonths} month${this.currentMonths > 1 ? 's' : ''}`,
                    "order_id": orderData.order_id,
                    "handler": (response) => this.handlePaymentSuccess(response),
                    "prefill": {
                        "name": "{{ user.get_full_name|default:user.username }}",
                        "email": "{{ user.email }}"
                    },
                    "theme": {
                        "color": "#6366f1"
                    },
                    "modal": {
                        "ondismiss": () => this.resetPayButton()
                    }
                };

                const rzp = new Razorpay(options);
                rzp.open();
            }

            handlePaymentSuccess(response) {
                console.log('Payment successful:', response);

                // Fill hidden form
                document.getElementById('razorpay_order_id').value = response.razorpay_order_id;
                document.getElementById('razorpay_payment_id').value = response.razorpay_payment_id;
                document.getElementById('razorpay_signature').value = response.razorpay_signature;

                // Submit verification form
                document.getElementById('payment-form').submit();
            }

            resetPayButton() {
                const payButton = document.getElementById('pay-button');
                payButton.classList.remove('btn-loading');
                payButton.disabled = false;
            }
        }

        // Global functions for backward compatibility
        let checkoutManager;

        function updateTotal() {
            checkoutManager?.updateTotal();
        }

        function toggleCouponForm() {
            const container = document.getElementById('coupon-form-container');
            const chevron = document.getElementById('coupon-chevron');

            if (container.classList.contains('show')) {
                container.classList.remove('show');
                chevron.style.transform = 'rotate(0deg)';
            } else {
                container.classList.add('show');
                chevron.style.transform = 'rotate(180deg)';
            }
        }

        function applyCoupon() {
            const couponCode = document.getElementById('coupon-code').value;
            checkoutManager?.applyCoupon(couponCode);
        }

        function initiatePayment() {
            checkoutManager?.initiatePayment();
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            checkoutManager = new CheckoutManager();

            // Add smooth transitions
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.3s ease';
                document.body.style.opacity = '1';
            }, 100);
        });

        // Add additional CSS for coupon form animation
        const additionalCSS = `
            .coupon-form-container {
                transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .coupon-form-container.show {
                max-height: 300px;
            }

            .input-group {
                display: flex;
            }

            .input-group .form-control-modern {
                flex: 1;
            }

            #coupon-chevron {
                transition: transform 0.3s ease;
            }

            .alert {
                margin-bottom: 0;
                padding: 0.75rem 1rem;
                border: 1px solid transparent;
            }

            .alert-success {
                background-color: rgba(16, 185, 129, 0.1);
                border-color: rgba(16, 185, 129, 0.2);
                color: #059669;
            }

            .alert-danger {
                background-color: rgba(239, 68, 68, 0.1);
                border-color: rgba(239, 68, 68, 0.2);
                color: #dc2626;
            }
        `;

        const style = document.createElement('style');
        style.textContent = additionalCSS;
        document.head.appendChild(style);
    </script>
</body>
</html>
