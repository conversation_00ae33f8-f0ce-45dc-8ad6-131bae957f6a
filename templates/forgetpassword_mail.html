{% extends "email_base.html" %}

{% block email_title %}Password Reset Request - Librainian{% endblock %}

{% block email_subject %}Password Reset Request{% endblock %}

{% block email_description %}Secure password reset request for your Librainian account.{% endblock %}

{% block preview_text %}Password reset request for your Librainian account. Click to reset securely.{% endblock %}

{% block header_icon %}🔑{% endblock %}

{% block email_header_title %}Password Reset{% endblock %}

{% block email_header_subtitle %}Secure password reset for your account{% endblock %}
{% block email_styles %}
<style>
    .reset-section {
        background: linear-gradient(135deg, #f8f9ff 0%, #e8eaff 100%);
        border-radius: 16px;
        padding: 30px;
        text-align: center;
        margin: 30px 0;
        border: 2px solid #f59e0b;
        position: relative;
        overflow: hidden;
    }

    .reset-section::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(245, 158, 11, 0.1), transparent);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(30deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(30deg); }
    }

    .reset-icon {
        font-size: 48px;
        margin-bottom: 20px;
        position: relative;
        z-index: 1;
    }

    .reset-title {
        font-size: 24px;
        font-weight: 700;
        color: #2c3e50;
        margin: 0 0 15px 0;
        position: relative;
        z-index: 1;
    }

    .reset-description {
        font-size: 16px;
        color: #6c757d;
        margin: 0 0 25px 0;
        position: relative;
        z-index: 1;
    }

    .action-button {
        display: inline-block;
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: #ffffff !important;
        text-decoration: none;
        padding: 16px 32px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;
        box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);
    }

    .action-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
        text-decoration: none;
        color: #ffffff !important;
    }
    .security-notice {
        background-color: #fef3c7;
        border: 1px solid #fbbf24;
        border-radius: 8px;
        padding: 20px;
        margin: 25px 0;
        border-left: 4px solid #f59e0b;
    }

    .notice-title {
        font-size: 16px;
        font-weight: 600;
        color: #92400e;
        margin: 0 0 10px 0;
        display: flex;
        align-items: center;
    }

    .notice-title::before {
        content: "🔒";
        margin-right: 8px;
        font-size: 18px;
    }

    .notice-text {
        font-size: 14px;
        color: #92400e;
        margin: 0;
        line-height: 1.5;
    }

    .timer-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        margin: 25px 0;
    }

    .timer-icon {
        font-size: 24px;
        margin-bottom: 10px;
    }

    .timer-text {
        font-size: 16px;
        color: #dc3545;
        font-weight: 600;
        margin: 0;
    }

    .timer-subtext {
        font-size: 14px;
        color: #6c757d;
        margin: 5px 0 0 0;
    }
    @media only screen and (max-width: 600px) {
        .reset-section {
            padding: 25px 20px !important;
            margin: 20px 0 !important;
        }

        .reset-title {
            font-size: 20px !important;
        }

        .action-button {
            padding: 14px 28px !important;
            font-size: 14px !important;
        }

        .security-notice,
        .timer-section {
            padding: 15px !important;
            margin: 20px 0 !important;
        }
    }
</style>
{% endblock %}

{% block email_content %}
<h2 class="greeting">Hello!</h2>
<p class="message">
    We received a request to reset the password for your Librainian account.
    If you made this request, click the button below to create a new password.
</p>

<!-- Reset Section -->
<div class="reset-section">
    <div class="reset-icon">🔐</div>
    <h3 class="reset-title">Reset Your Password</h3>
    <p class="reset-description">
        Click the button below to securely reset your password. You'll be redirected to a secure page where you can create a new password.
    </p>
    <a href="{{ reset_url }}" class="action-button">Reset Password Now</a>
</div>

<!-- Timer Section -->
<div class="timer-section">
    <div class="timer-icon">⏰</div>
    <p class="timer-text">Link expires in 24 hours</p>
    <p class="timer-subtext">For security reasons, this reset link will expire automatically</p>
</div>

<!-- Security Notice -->
<div class="security-notice">
    <h4 class="notice-title">Security Notice</h4>
    <p class="notice-text">
        • If you didn't request this password reset, please ignore this email<br>
        • Never share your password or reset links with anyone<br>
        • Always use a strong, unique password for your account<br>
        • Contact support if you have any security concerns
    </p>
</div>

<p style="margin: 30px 0 0 0; color: #6c757d; font-size: 14px; text-align: center;">
    This is an automated security message. Please do not reply to this email.
</p>
{% endblock %}
