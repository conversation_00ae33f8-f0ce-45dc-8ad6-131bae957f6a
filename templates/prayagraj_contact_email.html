{% extends "email_base.html" %}

{% block email_title %}Contact Form Submission - Prayagraj{% endblock %}

{% block email_subject %}New Contact Form Submission from {{ contact.name }}{% endblock %}

{% block email_description %}Contact form submission from the Prayagraj website with user inquiry.{% endblock %}

{% block preview_text %}New contact from {{ contact.name }} ({{ contact.email }}) regarding "{{ contact.subject }}".{% endblock %}

{% block header_icon %}📧{% endblock %}

{% block email_header_title %}New Contact Form Submission{% endblock %}

{% block email_header_subtitle %}Someone has reached out through your website{% endblock %}

{% block email_styles %}
<style>
    .contact-notification {
        background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
        border-radius: 16px;
        padding: 25px;
        margin: 25px 0;
        border: 2px solid #0288d1;
        position: relative;
        overflow: hidden;
    }

    .contact-notification::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(2, 136, 209, 0.1), transparent);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(30deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(30deg); }
    }

    .contact-icon {
        font-size: 48px;
        text-align: center;
        margin-bottom: 20px;
        position: relative;
        z-index: 1;
    }

    .notification-title {
        font-size: 20px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 20px 0;
        text-align: center;
        position: relative;
        z-index: 1;
    }

    .contact-details {
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 12px;
        padding: 20px;
        margin: 20px 0;
        position: relative;
        z-index: 1;
    }

    .detail-item {
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid rgba(2, 136, 209, 0.2);
    }

    .detail-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .detail-label {
        font-weight: 600;
        color: #0288d1;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 5px;
    }

    .detail-value {
        color: #2c3e50;
        font-size: 16px;
        line-height: 1.5;
        margin: 0;
    }

    .message-content {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #0288d1;
        white-space: pre-line;
        font-style: italic;
    }

    .priority-section {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
        border-left: 4px solid #fdcb6e;
    }

    .priority-title {
        font-size: 16px;
        font-weight: 600;
        color: #856404;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
    }

    .priority-title::before {
        content: "⚡";
        margin-right: 8px;
        font-size: 18px;
    }

    .priority-text {
        font-size: 14px;
        color: #856404;
        margin: 0;
        line-height: 1.5;
    }

    .meta-section {
        background-color: #f3f4f6;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
        border-left: 4px solid #6b7280;
    }

    .meta-title {
        font-size: 16px;
        font-weight: 600;
        color: #6b7280;
        margin: 0 0 10px 0;
        display: flex;
        align-items: center;
    }

    .meta-title::before {
        content: "ℹ️";
        margin-right: 8px;
        font-size: 18px;
    }

    @media only screen and (max-width: 600px) {
        .contact-notification {
            padding: 20px !important;
            margin: 20px 0 !important;
        }

        .contact-details {
            padding: 15px !important;
            margin: 15px 0 !important;
        }
    }
</style>
{% endblock %}

{% block email_content %}
<h2 class="greeting">Hello Admin!</h2>
<p class="message">
    A new contact form submission has been received through the Prayagraj website. Please review the details below and respond appropriately.
</p>

<!-- Contact Notification Section -->
<div class="contact-notification">
    <div class="contact-icon">📧</div>
    <h3 class="notification-title">New Contact Form Submission</h3>

    <div class="contact-details">
        <div class="detail-item">
            <div class="detail-label">Name</div>
            <div class="detail-value">{{ contact.name }}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Email Address</div>
            <div class="detail-value">{{ contact.email }}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Phone Number</div>
            <div class="detail-value">{{ contact.phone|default:'Not provided' }}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Subject</div>
            <div class="detail-value">{{ contact.subject }}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Message</div>
            <div class="detail-value message-content">{{ contact.message }}</div>
        </div>
    </div>
</div>

<!-- Priority Section -->
<div class="priority-section">
    <h4 class="priority-title">Response Required</h4>
    <p class="priority-text">
        Please review this contact form submission and respond to the user within 24-48 hours. Timely responses help maintain good customer relationships and service quality.
    </p>
</div>

<!-- Meta Information -->
<div class="meta-section">
    <h4 class="meta-title">Submission Details</h4>
    <div class="detail-item">
        <div class="detail-label">Submitted On</div>
        <div class="detail-value">{{ contact.created_at|date:"F j, Y, g:i a" }}</div>
    </div>
    <div class="detail-item">
        <div class="detail-label">IP Address</div>
        <div class="detail-value">{{ contact.ip_address|default:'Not recorded' }}</div>
    </div>
    <div class="detail-item">
        <div class="detail-label">User Agent</div>
        <div class="detail-value">{{ contact.user_agent|default:'Not recorded' }}</div>
    </div>
</div>

<p class="message">
    This is an automated notification from the Prayagraj contact form system. Please log into the admin panel to manage contact submissions and send responses.
</p>
{% endblock %}