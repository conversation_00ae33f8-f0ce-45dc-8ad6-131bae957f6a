<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta http-equiv="content-language" content="en">
    <meta name="geo.region" content="IN">

    <!-- Include Navbar Head Section -->
    {% with navbar_section="head" %}
        {% include "public_navbar.html" %}
    {% endwith %}

    <!-- Primary Meta Tags -->
    <title>{% if category %}{{ category|title }} Articles{% elif tag %}{{ tag|title }} Articles{% else %}Librainian Blog{% endif %} | Library Management Insights</title>
    
    <meta name="description" content="{% if category %}Explore our {{ category|title }} articles and resources for libraries and educational institutions. {% elif tag %}Browse all articles tagged with {{ tag|title }} on the Librainian blog. {% else %}Discover insights, tips, and best practices for library management, student engagement, and educational resources on the Librainian blog.{% endif %} Stay informed with the latest trends in library science." id="metaDescription">
    
    <meta name="keywords" content="{% if category %}{{ category }}, {{ category }} articles, {{ category }} library resources, {{ category }} library management{% elif tag %}{{ tag }}, {{ tag }} library, {{ tag }} resources, articles about {{ tag }}{% else %}library blog, library management, student resources, educational content, library science, digital libraries, library technology, reading resources, study spaces, library administration{% endif %}, Librainian blog, library resources">
    
    <meta name="author" content="Librainian">
    <meta name="robots" content="index, follow, max-image-preview:large">

    <!-- Canonical URL -->
    <link rel="canonical" href="{% if category %}https://www.librainian.com/blogs/category/{{ category|slugify }}/{% elif tag %}https://www.librainian.com/blogs/tag/{{ tag|slugify }}/{% else %}https://www.librainian.com/blogs/p/{% endif %}">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Librainian Blog">
    <meta property="og:title" content="{% if category %}{{ category|title }} Articles{% elif tag %}{{ tag|title }} Articles{% else %}Librainian Blog{% endif %} | Library Management Insights">
    <meta property="og:description" content="{% if category %}Explore our {{ category|title }} articles and resources for libraries and educational institutions. {% elif tag %}Browse all articles tagged with {{ tag|title }} on the Librainian blog. {% else %}Discover insights, tips, and best practices for library management, student engagement, and educational resources on the Librainian blog.{% endif %} Stay informed with the latest trends in library science." id="ogDescription">
    <meta property="og:url" content="{% if category %}https://www.librainian.com/blogs/category/{{ category|slugify }}/{% elif tag %}https://www.librainian.com/blogs/tag/{{ tag|slugify }}/{% else %}https://www.librainian.com/blogs/p/{% endif %}">
    <meta property="og:image" content="https://www.librainian.com/static/img/blog-header-image.jpg">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@librainian_app">
    <meta name="twitter:title" content="{% if category %}{{ category|title }} Articles{% elif tag %}{{ tag|title }} Articles{% else %}Librainian Blog{% endif %} | Library Management Insights">
    <meta name="twitter:description" content="{% if category %}Explore our {{ category|title }} articles and resources for libraries and educational institutions. {% elif tag %}Browse all articles tagged with {{ tag|title }} on the Librainian blog. {% else %}Discover insights, tips, and best practices for library management, student engagement, and educational resources on the Librainian blog.{% endif %} Stay informed with the latest trends in library science." id="twitterDescription">
    <meta name="twitter:image" content="https://www.librainian.com/static/img/blog-header-image.jpg">

    <!-- Favicon -->
    <link href="https://i.postimg.cc/ZY3b6dw9/librainian-logo-black-transparent-med.png" rel="icon">

    <!-- CollectionPage Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": "{% if category %}{{ category|title }} Articles{% elif tag %}{{ tag|title }} Articles{% else %}Librainian Blog{% endif %} | Library Management Insights",
        "description": "{% if category %}Explore our {{ category|title }} articles and resources for libraries and educational institutions.{% elif tag %}Browse all articles tagged with {{ tag|title }} on the Librainian blog.{% else %}Discover insights, tips, and best practices for library management, student engagement, and educational resources on the Librainian blog.{% endif %}",
        "url": "{% if category %}https://librainian.com/blogs/p/cat/{{ category|slugify }}/{% elif tag %}https://librainian.com/blogs/p/tag/{{ tag|slugify }}/{% else %}https://librainian.com/blogs/p/{% endif %}",
        "isPartOf": {
            "@type": "WebSite",
            "name": "Librainian",
            "url": "https://librainian.com/"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "logo": {
                "@type": "ImageObject",
                "url": "https://librainian.com/static/img/logo_trans_name.png",
                "width": 300,
                "height": 100
            },
            "url": "https://librainian.com/"
        },
        "mainEntity": {
            "@type": "ItemList",
            "itemListElement": [
                {% for blog in blogs %}
                {
                    "@type": "ListItem",
                    "position": {{ forloop.counter }},
                    "item": {
                        "@type": "BlogPosting",
                        "headline": "{{ blog.title|escapejs }}",
                        "description": "{{ blog.short_content|default:blog.introduction|truncatewords:20|escapejs }}",
                        "url": "https://librainian.com/blogs/p/{{ blog.slug }}/",
                        "datePublished": "{{ blog.published_date|date:'c' }}",
                        "author": {
                            "@type": "Person",
                            "name": "{{ blog.author.user.first_name }} {{ blog.author.user.last_name }}"
                        },
                        "image": {
                            "@type": "ImageObject",
                            "url": "https://librainian.com{{ blog.image.url }}"
                        }
                    }
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ]
        }
    }
    </script>

    <!-- BreadcrumbList Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://librainian.com/"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Blog",
                "item": "https://librainian.com/blogs/p/"
            }{% if category %},
            {
                "@type": "ListItem",
                "position": 3,
                "name": "{{ category|title }}",
                "item": "https://librainian.com/blogs/p/cat/{{ category|slugify }}/"
            }{% elif tag %},
            {
                "@type": "ListItem",
                "position": 3,
                "name": "{{ tag|title }}",
                "item": "https://librainian.com/blogs/p/tag/{{ tag|slugify }}/"
            }{% endif %}
        ]
    }
    </script>

    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --border-radius: 12px;
            --border-radius-lg: 20px;
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Comfortaa', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            margin: 0;
            padding: 0;
            color: var(--gray-900);
            line-height: 1.7;
        }

        /* Modern Navigation */
        .modern-navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: 1rem 0;
        }

        .navbar-brand {
            font-family: 'Playfair Display', serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary) !important;
            text-decoration: none;
        }

        .navbar-nav .nav-link {
            color: var(--gray-700) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: var(--border-radius);
            transition: var(--transition);
            margin: 0 0.25rem;
        }

        .navbar-nav .nav-link:hover {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary) !important;
        }

        .btn-modern {
            padding: 0.5rem 1.5rem;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: 0.875rem;
            transition: var(--transition);
            border: 2px solid;
        }

        .btn-outline-modern {
            background: transparent;
            border-color: var(--primary);
            color: var(--primary);
        }

        .btn-outline-modern:hover {
            background: var(--primary);
            color: white;
        }

        .btn-primary-modern {
            background: var(--primary);
            border-color: var(--primary);
            color: white;
        }

        .btn-primary-modern:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
            color: white;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
        }

        .hero-title {
            font-family: 'Playfair Display', serif;
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        /* Search Section */
        .search-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xl);
            padding: 2rem;
            margin: -3rem auto 3rem;
            max-width: 800px;
            position: relative;
            z-index: 10;
        }

        .search-input {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius);
            padding: 1rem 1.5rem;
            font-size: 1rem;
            transition: var(--transition);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
            background: white;
        }

        .search-btn {
            background: var(--primary);
            border: 2px solid var(--primary);
            border-radius: var(--border-radius);
            padding: 1rem 1.5rem;
            color: white;
            font-weight: 600;
            transition: var(--transition);
        }

        .search-btn:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
            color: white;
        }

        /* Filter Section */
        .filter-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .filter-title {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 1rem;
        }

        .category-btn {
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius);
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            color: var(--gray-700);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: var(--transition);
            display: inline-block;
        }

        .category-btn:hover,
        .category-btn.active {
            background: var(--primary);
            border-color: var(--primary);
            color: white;
            text-decoration: none;
        }

        .tag-link {
            background: rgba(99, 102, 241, 0.1);
            border: 1px solid rgba(99, 102, 241, 0.3);
            border-radius: var(--border-radius);
            padding: 0.25rem 0.75rem;
            margin: 0.25rem;
            color: var(--primary);
            text-decoration: none;
            font-size: 0.75rem;
            font-weight: 500;
            transition: var(--transition);
            display: inline-block;
        }

        .tag-link:hover,
        .tag-link.active {
            background: var(--primary);
            border-color: var(--primary);
            color: white;
            text-decoration: none;
        }

        /* Blog Grid */
        .blog-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .blog-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            transition: var(--transition);
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .blog-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }

        .blog-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: var(--transition);
        }

        .blog-card:hover .blog-image {
            transform: scale(1.05);
        }

        .blog-card-body {
            padding: 1.5rem;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .blog-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.75rem;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .blog-meta {
            font-size: 0.75rem;
            color: var(--gray-500);
            margin-bottom: 1rem;
        }

        .blog-description {
            color: var(--gray-600);
            font-size: 0.875rem;
            line-height: 1.5;
            flex-grow: 1;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .read-more-btn {
            background: var(--primary);
            border: none;
            border-radius: var(--border-radius);
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            text-decoration: none;
            transition: var(--transition);
            text-align: center;
            margin-top: auto;
        }

        .read-more-btn:hover {
            background: var(--primary-dark);
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
        }

        /* No Results */
        .no-results {
            text-align: center;
            padding: 4rem 2rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
        }

        .no-results i {
            font-size: 4rem;
            color: var(--gray-400);
            margin-bottom: 1rem;
        }

        .no-results h3 {
            color: var(--gray-700);
            margin-bottom: 1rem;
        }

        .no-results p {
            color: var(--gray-500);
            margin-bottom: 2rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-subtitle {
                font-size: 1rem;
            }
            
            .search-section {
                margin: -2rem 1rem 2rem;
                padding: 1.5rem;
            }
            
            .blog-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .filter-section {
                padding: 1rem;
            }
        }

        /* Dark Mode Styles for Blog Page */
        body.dark-mode {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            color: white;
        }

        body.dark-mode .hero-section {
            background: rgba(17, 24, 39, 0.9);
        }

        body.dark-mode .blog-card {
            background: rgba(31, 41, 55, 0.8);
            border-color: rgba(75, 85, 99, 0.3);
            color: white;
        }

        body.dark-mode .blog-title {
            color: white;
        }

        body.dark-mode .blog-description {
            color: rgba(255, 255, 255, 0.8);
        }

        body.dark-mode .blog-meta {
            color: rgba(255, 255, 255, 0.7);
        }

        body.dark-mode .category-btn,
        body.dark-mode .tag-link {
            background: rgba(31, 41, 55, 0.8);
            color: white;
            border-color: rgba(75, 85, 99, 0.3);
        }

        body.dark-mode .category-btn:hover,
        body.dark-mode .tag-link:hover {
            background: rgba(99, 102, 241, 0.2);
            color: white;
        }

        body.dark-mode .search-container input {
            background: rgba(31, 41, 55, 0.8);
            border-color: rgba(75, 85, 99, 0.3);
            color: white;
        }

        body.dark-mode .search-container input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        body.dark-mode .no-results {
            color: white;
        }

        body.dark-mode .btn-primary-modern {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
        }
    </style>
</head>
<body style="padding-top: 80px;">
    <!-- Include Public Navigation Body -->
    {% with navbar_section="body" %}
        {% include "public_navbar.html" %}
    {% endwith %}
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Blog",
      "name": "{% if category %}{{ category|title }} Articles{% elif tag %}{{ tag|title }} Articles{% else %}Librainian Blog{% endif %}",
      "description": "{% if category %}Explore our {{ category|title }} articles and resources for libraries and educational institutions. {% elif tag %}Browse all articles tagged with {{ tag|title }} on the Librainian blog. {% else %}Discover insights, tips, and best practices for library management, student engagement, and educational resources on the Librainian blog.{% endif %}",
      "url": "{% if category %}https://www.librainian.com/blogs/category/{{ category|slugify }}/{% elif tag %}https://www.librainian.com/blogs/tag/{{ tag|slugify }}/{% else %}https://www.librainian.com/blogs/p/{% endif %}",
      "publisher": {
        "@type": "Organization",
        "name": "Librainian",
        "logo": {
          "@type": "ImageObject",
          "url": "https://www.librainian.com/static/img/librainian-logo-black-transparent.png"
        }
      }
    }
    </script>
</head>
<body style="padding-top: 80px;">
    <!-- Include Public Navigation -->
    {% include "public_navbar.html" %}

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    {% if category %}
                        {{ category|title }} Articles
                    {% elif tag %}
                        {{ tag|title }} Articles
                    {% else %}
                        Librainian Blog
                    {% endif %}
                </h1>
                <p class="hero-subtitle">
                    {% if category %}
                        Explore our {{ category|title }} articles and resources for libraries and educational institutions.
                    {% elif tag %}
                        Browse all articles tagged with {{ tag|title }} on the Librainian blog.
                    {% else %}
                        Discover insights, tips, and best practices for library management, student engagement, and educational resources.
                    {% endif %}
                </p>

                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb justify-content-center bg-transparent">
                        <li class="breadcrumb-item">
                            <a href="/" class="text-white text-decoration-none">
                                <i class="fas fa-home me-1"></i>Home
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="/blogs/p/" class="text-white text-decoration-none">Blog</a>
                        </li>
                        {% if category %}
                        <li class="breadcrumb-item active text-white" aria-current="page">{{ category|title }}</li>
                        {% elif tag %}
                        <li class="breadcrumb-item active text-white" aria-current="page">{{ tag|title }}</li>
                        {% endif %}
                    </ol>
                </nav>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="container">
        <!-- Search Section -->
        <div class="search-section">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="search"
                               id="searchInput"
                               class="form-control search-input"
                               placeholder="Search articles..."
                               aria-label="Search articles">
                        <button class="btn search-btn" type="button" onclick="filterBlogs()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-4 text-md-end mt-3 mt-md-0">
                    <span class="text-muted">
                        <i class="fas fa-newspaper me-1"></i>
                        {{ blogs|length }} Article{{ blogs|length|pluralize }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <!-- Categories -->
            <div class="mb-3">
                <h6 class="filter-title">
                    <i class="fas fa-folder me-2"></i>
                    Browse by Category
                </h6>
                <div class="category-filters">
                    <a href="/blogs/p/" class="category-btn {% if not category %}active{% endif %}">
                        All Categories
                    </a>
                    {% for cat in categories %}
                    <a href="/blogs/p/cat/{{ cat|slugify }}/"
                       class="category-btn {% if category == cat %}active{% endif %}">
                        {{ cat }}
                    </a>
                    {% endfor %}
                </div>
            </div>

            <!-- Tags -->
            {% if popular_tags %}
            <div>
                <h6 class="filter-title">
                    <i class="fas fa-tags me-2"></i>
                    Popular Tags
                </h6>
                <div class="tag-filters">
                    {% for t in popular_tags %}
                    <a href="/blogs/p/tag/{{ t|slugify }}/"
                       class="tag-link {% if tag == t %}active{% endif %}">
                        #{{ t }}
                    </a>
                    {% empty %}
                    <span class="text-muted">No tags available</span>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Blog Grid -->
        <div class="blog-grid" id="blogContainer">
            {% for blog in blogs %}
            <article class="blog-card"
                     data-title="{{ blog.title|lower }}"
                     data-description="{{ blog.short_content|default:blog.introduction|lower }}"
                     data-category="{{ blog.category|lower }}"
                     data-tags=""

                {% if blog.image %}
                <div class="blog-image-container">
                    <img src="{{ blog.image.url }}"
                         class="blog-image"
                         alt="{{ blog.title }}"
                         loading="lazy">
                </div>
                {% endif %}

                <div class="blog-card-body">
                    <div class="blog-meta">
                        <i class="fas fa-user me-1"></i>
                        {{ blog.author.user.first_name }} {{ blog.author.user.last_name }}
                        <span class="mx-2">•</span>
                        <i class="fas fa-calendar me-1"></i>
                        {{ blog.created_at|date:"M d, Y" }}
                        <span class="mx-2">•</span>
                        <i class="fas fa-tag me-1"></i>
                        {{ blog.category }}
                    </div>

                    <h2 class="blog-title">{{ blog.title }}</h2>

                    <p class="blog-description">{{ blog.short_content|default:blog.introduction|truncatewords:20|safe }}</p>

                    <a href="/blogs/p/{{ blog.slug }}/"
                       class="read-more-btn"
                       aria-label="Read full article: {{ blog.title }}">
                        Read Article
                        <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </article>
            {% empty %}
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h3>No articles found</h3>
                <p>We couldn't find any articles matching your criteria. Try adjusting your search or browse all categories.</p>
                <a href="/blogs/p/" class="btn btn-primary-modern">
                    <i class="fas fa-list me-1"></i>
                    Browse All Articles
                </a>
            </div>
            {% endfor %}
        </div>

        <!-- No Search Results -->
        <div id="noResults" class="no-results" style="display: none;">
            <i class="fas fa-search"></i>
            <h3>No articles found</h3>
            <p>We couldn't find any articles matching your search. Try different keywords or browse our categories.</p>
            <button class="btn btn-primary-modern" onclick="clearSearch()">
                <i class="fas fa-times me-1"></i>
                Clear Search
            </button>
        </div>
    </main>

    <!-- Bootstrap 5.3.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Search and filter functionality
        function filterBlogs() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const blogCards = document.querySelectorAll('.blog-card');
            const noResults = document.getElementById('noResults');
            let visibleCount = 0;

            blogCards.forEach(card => {
                const title = card.dataset.title;
                const description = card.dataset.description;
                const category = card.dataset.category;
                const tags = card.dataset.tags;

                const isVisible = title.includes(searchTerm) ||
                                description.includes(searchTerm) ||
                                category.includes(searchTerm) ||
                                tags.includes(searchTerm);

                if (isVisible) {
                    card.style.display = 'flex';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            // Show/hide no results message
            if (visibleCount === 0 && searchTerm !== '') {
                noResults.style.display = 'block';
            } else {
                noResults.style.display = 'none';
            }
        }

        function clearSearch() {
            document.getElementById('searchInput').value = '';
            filterBlogs();
        }

        // Real-time search
        document.getElementById('searchInput').addEventListener('input', filterBlogs);

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add loading animation to blog cards
        document.addEventListener('DOMContentLoaded', function() {
            const blogCards = document.querySelectorAll('.blog-card');
            blogCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
