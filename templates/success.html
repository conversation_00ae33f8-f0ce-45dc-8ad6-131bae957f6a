<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - Librainian</title>

    <!-- Bootstrap 5.3.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">

    <!-- Favicon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            height: 100%;
            font-family: 'Comfortaa', sans-serif;
            overflow: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
        }

        /* Animated Background */
        .success-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            opacity: 0;
            animation: fadeInBackground 1.5s ease-out forwards;
            z-index: -2;
        }

        .success-background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.15) 0%, transparent 50%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes fadeInBackground {
            from { opacity: 0; transform: scale(1.1); }
            to { opacity: 1; transform: scale(1); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(1deg); }
        }

        /* Success Container */
        .success-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 2rem;
            position: relative;
            z-index: 10;
        }

        /* Success Card */
        .success-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 24px;
            padding: 3rem 2rem;
            text-align: center;
            max-width: 500px;
            width: 100%;
            box-shadow:
                0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset,
                0 2px 4px rgba(255, 255, 255, 0.1) inset;
            opacity: 0;
            transform: translateY(30px) scale(0.9);
            animation: slideInCard 1s ease-out 0.5s forwards;
        }

        @keyframes slideInCard {
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Success Icon */
        .success-icon {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            border: 3px solid rgba(255, 255, 255, 0.4);
            position: relative;
            overflow: hidden;
        }

        .success-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .success-icon i {
            font-size: 3rem;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            z-index: 2;
            position: relative;
            opacity: 0;
            animation: popIn 0.6s ease-out 1.2s forwards;
        }

        @keyframes popIn {
            0% { opacity: 0; transform: scale(0.5); }
            50% { transform: scale(1.2); }
            100% { opacity: 1; transform: scale(1); }
        }

        /* Success Content */
        .success-title {
            color: white;
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            opacity: 0;
            animation: fadeInUp 0.8s ease-out 1.5s forwards;
        }

        .success-message {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 2rem;
            line-height: 1.6;
            opacity: 0;
            animation: fadeInUp 0.8s ease-out 1.7s forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Action Buttons */
        .success-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            opacity: 0;
            animation: fadeInUp 0.8s ease-out 1.9s forwards;
        }

        .btn-success-primary {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.4);
            color: white;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .btn-success-primary:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.6);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .btn-success-secondary {
            background: transparent;
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.9);
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 500;
            font-size: 0.95rem;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn-success-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: translateY(-1px);
        }

        /* Floating Particles */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: floatUp 4s infinite ease-in-out;
        }

        .particle:nth-child(1) { left: 10%; animation-delay: 0s; width: 4px; height: 4px; }
        .particle:nth-child(2) { left: 20%; animation-delay: 0.5s; width: 6px; height: 6px; }
        .particle:nth-child(3) { left: 30%; animation-delay: 1s; width: 3px; height: 3px; }
        .particle:nth-child(4) { left: 40%; animation-delay: 1.5s; width: 5px; height: 5px; }
        .particle:nth-child(5) { left: 50%; animation-delay: 2s; width: 4px; height: 4px; }
        .particle:nth-child(6) { left: 60%; animation-delay: 2.5s; width: 6px; height: 6px; }
        .particle:nth-child(7) { left: 70%; animation-delay: 3s; width: 3px; height: 3px; }
        .particle:nth-child(8) { left: 80%; animation-delay: 3.5s; width: 5px; height: 5px; }
        .particle:nth-child(9) { left: 90%; animation-delay: 4s; width: 4px; height: 4px; }

        @keyframes floatUp {
            0% {
                bottom: -10px;
                opacity: 0;
                transform: translateX(0px) rotate(0deg);
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                bottom: 100vh;
                opacity: 0;
                transform: translateX(100px) rotate(360deg);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .success-card {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }

            .success-title {
                font-size: 1.75rem;
            }

            .success-message {
                font-size: 1rem;
            }

            .success-icon {
                width: 100px;
                height: 100px;
            }

            .success-icon i {
                font-size: 2.5rem;
            }
        }

        @media (max-width: 480px) {
            .success-actions {
                gap: 0.75rem;
            }

            .btn-success-primary,
            .btn-success-secondary {
                padding: 0.875rem 1.5rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Animated Background -->
    <div class="success-background"></div>

    <!-- Floating Particles -->
    <div class="particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <!-- Success Container -->
    <div class="success-container">
        <div class="success-card">
            <!-- Success Icon -->
            <div class="success-icon">
                <i class="fas fa-check"></i>
            </div>

            <!-- Success Content -->
            <h1 class="success-title">Payment Successful!</h1>
            <p class="success-message">
                Thank you for your purchase. Your transaction has been completed successfully and you will receive a confirmation email shortly.
            </p>

            <!-- Action Buttons -->
            <div class="success-actions">
                <a href="/{{ role|default:'librarian' }}/dashboard/" class="btn-success-primary">
                    <i class="fas fa-home"></i>
                    Back to Dashboard
                </a>
                <a href="/{{ role|default:'librarian' }}/invoice/" class="btn-success-secondary">
                    <i class="fas fa-receipt"></i>
                    View Invoice
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5.3.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto redirect after 10 seconds
            setTimeout(function() {
                const dashboardUrl = document.querySelector('.btn-success-primary').href;
                window.location.href = dashboardUrl;
            }, 10000);

            // Add click sound effect (optional)
            document.querySelectorAll('.btn-success-primary, .btn-success-secondary').forEach(button => {
                button.addEventListener('click', function() {
                    // Add a subtle click effect
                    this.style.transform = 'translateY(-2px) scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    const primaryButton = document.querySelector('.btn-success-primary');
                    if (primaryButton) {
                        primaryButton.click();
                    }
                }
            });

            // Add confetti effect (simple version)
            createConfetti();
        });

        function createConfetti() {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd'];
            const confettiContainer = document.createElement('div');
            confettiContainer.style.position = 'fixed';
            confettiContainer.style.top = '0';
            confettiContainer.style.left = '0';
            confettiContainer.style.width = '100%';
            confettiContainer.style.height = '100%';
            confettiContainer.style.pointerEvents = 'none';
            confettiContainer.style.zIndex = '5';
            document.body.appendChild(confettiContainer);

            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.style.position = 'absolute';
                    confetti.style.left = Math.random() * 100 + '%';
                    confetti.style.top = '-10px';
                    confetti.style.width = '10px';
                    confetti.style.height = '10px';
                    confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                    confetti.style.borderRadius = '50%';
                    confetti.style.animation = `confettiFall ${2 + Math.random() * 3}s linear forwards`;
                    confettiContainer.appendChild(confetti);

                    setTimeout(() => {
                        confetti.remove();
                    }, 5000);
                }, i * 100);
            }

            // Add confetti animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes confettiFall {
                    to {
                        transform: translateY(100vh) rotate(720deg);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    </script>
</body>
</html>
