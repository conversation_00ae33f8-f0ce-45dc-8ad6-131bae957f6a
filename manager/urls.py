from django.urls import path
from . import views


urlpatterns = [
    path("signup/", views.manager_signup, name="signup"),
    path("login/", views.manager_login, name="login"),
    path("logout/", views.manager_logout, name="logout"),
    path("profile/", views.manager_profile, name="profile"),
    path("edit-profile/", views.edit_manager_profile, name="edit_profile"),
    path("table/", views.library_data, name="library-data"),
    path("approve/<slug:slug>/", views.approve_library, name="approve_library"),
    path("reject/<slug:slug>/", views.reject_library, name="reject_library"),
    path("dashboard/", views.manager_dashboard, name="dashboard"),
    path("analytics/", views.manager_analytics_data, name="analytics"),
    path(
        "librarian-approval/<slug:slug>/",
        views.librarian_approval,
        name="librarian-approval",
    ),
    path("help/", views.help_page, name="help_page"),
    path("feedback/", views.feedback_page, name="feedback_page"),
]
