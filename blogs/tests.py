from django.test import TestCase
from django.core.cache import cache
from .models import PageView

class PageViewTest(TestCase):
    def setUp(self):
        cache.clear()  # Ensure clean cache before test

    def test_page_view_tracking(self):
        response = self.client.get("/blogs/api/track-page-view/?url=/test-page/")
        self.assertEqual(response.status_code, 200)  # Ensure the response is OK


        # Check if cache is updated
        self.assertEqual(cache.get("pageviews:/test-page/"), 1)
        self.assertIn("/test-page/", cache.get("tracked_pages"))

    def test_sync_to_database(self):
        cache.set("pageviews:/test-page/", 10)
        cache.set("tracked_pages", {"/test-page/"})

        # Run sync command
        from django.core.management import call_command
        call_command("sync_page_views")

        # Check database
        page = PageView.objects.get(url="/test-page/")
        self.assertEqual(page.count, 10)
