from django.db import models
from django.utils import timezone
from libraryCommander.models import Library<PERSON>ommander_param
from django.utils.text import slugify
from bs4 import BeautifulSoup
# class Blog(models.Model):
#     title = models.CharField(max_length=200)
#     category = models.CharField(max_length=100)
#     description = models.TextField()
#     image = models.ImageField(upload_to="blog_images/")
#     author = models.ForeignKey(
#         LibraryCommander_param, on_delete=models.CASCADE, related_name="authored_blogs"
#     )
#     date_created = models.DateTimeField(default=timezone.now)
#     date_updated = models.DateTimeField(auto_now=True)
#     content = models.TextField()
#     keyword = models.CharField(max_length=250)
#     slug = models.SlugField(max_length=250, unique=True, blank=True)

#     def __str__(self):
#         return f"{self.title} | {self.category} | {self.date_created}"

#     def save(self, *args, **kwargs):
#         if not self.slug:
#             self.slug = slugify(self.title)
#         try:
#             super().save(*args, **kwargs)
#         except Exception as e:
#             print(f"Error saving Blog: {e}")

#     def get_absolute_url(self):
#         return reverse("blog_display", args=[str(self.slug)])

#     class Meta:
#         ordering = ["-date_updated"]


class Blog(models.Model):
    blacklist_keywords = models.JSONField(default=list, blank=True)  # Store bla
    # Basic Fields
    # blacklist_keywords = models.JSONField(default=list, blank=True)
    title = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    author = models.ForeignKey(
        LibraryCommander_param,
        on_delete=models.CASCADE,
        related_name="authored_blogs",
    )
    category = models.CharField(max_length=100)
    introduction = models.TextField(blank=True, null=True)
    content = models.TextField()
    short_content = models.TextField(
        blank=True, null=True, help_text="A brief excerpt of the blog post"
    )

    # Media Fields
    image = models.ImageField(upload_to="blog_images/")
    image_caption = models.CharField(max_length=255, blank=True, null=True)

    # Links
    internals_link = models.URLField(blank=True, null=True)
    externals_link = models.URLField(blank=True, null=True)

    # Timestamps
    published_date = models.DateTimeField(default=timezone.now)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Status and Workflow
    STATUS_CHOICES = [
        ("draft", "Draft"),
        ("pending", "Pending"),
        ("published", "Published"),
        ("archived", "Archived"),
    ]
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default="draft")

    # SEO Fields
    meta_title = models.CharField(max_length=255, blank=True, null=True)
    meta_description = models.TextField(
        blank=True, null=True, help_text="Recommended length: 150-160 characters"
    )
    meta_keywords = models.CharField(
        max_length=255, blank=True, null=True, help_text="Comma-separated keywords"
    )
    canonical_url = models.URLField(blank=True, null=True)

    # Structured Data
    open_graph = models.JSONField(
        blank=True, null=True, help_text="OpenGraph metadata for social sharing"
    )
    twitter_cards = models.JSONField(
        blank=True, null=True, help_text="Twitter Card metadata"
    )
    breadcrumb_schema = models.JSONField(
        blank=True, null=True, help_text="Schema.org BreadcrumbList markup"
    )
    article_schema = models.JSONField(
        blank=True, null=True, help_text="Schema.org Article markup"
    )

    class Meta:
        ordering = ["-published_date"]
        verbose_name = "Blog Post"
        verbose_name_plural = "Blog Posts"
        indexes = [
            models.Index(fields=["slug"]),
            models.Index(fields=["status"]),
            models.Index(fields=["published_date"]),
            models.Index(fields=["category"]),
        ]

    def __str__(self):
        return f"{self.title} | {self.category} | {self.published_date}"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)

        # Generate meta title if not provided
        if not self.meta_title:
            self.meta_title = self.title

        # Generate meta description if not provided
        if not self.meta_description:
            self.meta_description = (
                self.short_content[:160] if self.short_content else self.content[:160]
            )

        self.internals_link =  self.get_absolute_url()

        # Insert internal links into the content
        self.content = self.insert_internal_links(self.content)

        # # Generate schema markup
        # self.article_schema = {
        #     "@context": "https://schema.org",
        #     "@type": "Article",
        #     "headline": self.title,
        #     "author": {"@type": "Person", "name": str(self.author)},
        #     "datePublished": self.published_date,
        #     "dateModified": self.updated_at,
        #     "description": self.meta_description,
        # }

        try:
            super().save(*args, **kwargs)
        except Exception as e:
            print(f"Error saving BlogPost: {e}")
            raise

    def insert_internal_links(self, content):
        # Find related articles based on keywords, category, and date
        related_articles = Blog.objects.filter(
            category=self.category,
            published_date__lt=self.published_date
        ).exclude(pk=self.pk).order_by('-published_date')[:5]

        # Convert content to BeautifulSoup object for parsing
        soup = BeautifulSoup(content, 'html.parser')

        # Avoid linking within specific sections like quotes or code blocks
        for tag in soup.find_all(['blockquote', 'code']):
            tag.unwrap()

        # Insert links inline within the content
        # print(f"Related Articles: {related_articles}")
        for article in related_articles:
            # Add null check for meta_keywords
            if article.meta_keywords:
                keywords = article.meta_keywords.split(',')

                for keyword in keywords:
                    if keyword and keyword in content:
                        link = f'<a href="{article.get_absolute_url()}" style="color: maroon; font-weight: bold; text-decoration: underline;" title="{article.title}">{keyword}</a>'
                        content = content.replace(keyword, link, 1)

        # Add "Related Articles" section at the end of the content
        # related_section = '<h3>Related Articles</h3><ul>'
        # for article in related_articles:
        #     related_section += f'<li><a href="{article.get_absolute_url()}" style="color: maroon; font-weight: bold; text-decoration: underline;" title="{article.title}">{article.title}</a></li>'
        # related_section += '</ul>'
        # soup.append(BeautifulSoup(related_section, 'html.parser'))

        # return str(soup)
        return content

    def get_absolute_url(self):
        return f"/blogs/p/{self.slug}/"


class PageCounter(models.Model):
    url = models.CharField(max_length=255)
    count = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    def __str__(self):
        return f"{self.url}: {self.count}"



# class Coupon(models.Model):
#     DISCOUNT_TYPE_CHOICES = [
#         ('amount', 'Discount Amount'),
#         ('percentage', 'Percentage Discount'),
#     ]
#     code = models.CharField(max_length=50,null=True, blank=True)
#     discount_type = models.CharField(max_length=100, choices=DISCOUNT_TYPE_CHOICES)
#     discount_value = models.DecimalField(max_digits=100, decimal_places=2)
#     expiration_date = models.DateField()
#     usage_limit = models.PositiveIntegerField(default=0)
#     used_count = models.PositiveIntegerField(default=0)
#     # percentage = models.DecimalField(max_digits=10, decimal_places=2)
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)
#     overallDiscountAmount = models.DecimalField(max_digits=100, decimal_places=2, default=0 )
#     def is_valid(self):
#         return True

#     def __str__(self):
#         return f"{self.code} - {self.discount_value} {self.discount_type}"