from django.contrib.sitemaps import Sitemap
from .models import Blog
from typing import Any, Optional
from django.db.models.query import QuerySet


class PostSitemap(Sitemap):
    changefreq = "daily"
    priority = 0.7

    def items(self) -> QuerySet[Blog]:
        items = Blog.objects.all()
        print(f"Number of items: {items.count()}")
        return items

    def lastmod(self, obj: Blog) -> Optional[Any]:
        return obj.updated_at

    def location(self, obj: Blog) -> str:
        url = obj.get_absolute_url()
        print(f"Generated URL for {obj.title}: {url}")
        return url
