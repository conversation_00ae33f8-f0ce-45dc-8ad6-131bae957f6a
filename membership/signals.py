from django.db.models.signals import pre_save
from django.dispatch import receiver
from django.utils import timezone
from .models import Membership


@receiver(pre_save, sender=Membership)
def update_librarian_status(sender, instance, **kwargs):
    today = timezone.now().date()
    if instance.expiry_date < today:
        instance.librarian.is_librarian = False
    else:
        instance.librarian.is_librarian = True

    instance.librarian.save()
