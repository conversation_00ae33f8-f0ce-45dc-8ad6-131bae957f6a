from django.shortcuts import render, get_object_or_404, redirect
from .models import Advertisement
from django.http import JsonR<PERSON>ponse, HttpResponse

from django.core.exceptions import ValidationError
from datetime import datetime

import razorpay
from django.conf import settings


def advertisement_list(request):
    ads = Advertisement.objects.filter(status=True)
    print(ads)
    return render(request, "ad_list.html", {"ads": ads})


def advertisement_detail(request, pk):
    ad = get_object_or_404(Advertisement, pk=pk)
    return render(request, "ad_detail.html", {"ad": ad})


def advertisement_create(request):
    if request.method == "POST":
        subject = request.POST.get("subject")
        amount = request.POST.get("amount")
        start_date = request.POST.get("start_date")
        end_date = request.POST.get("end_date")
        max_views = request.POST.get("max_views")
        priority = request.POST.get("priority")
        age_group = request.POST.get("age_group")
        gender = request.POST.get("gender")
        image_vertical = request.FILES.get("image_vertical")
        image_horizontal = request.FILES.get("image_horizontal")

        date_format = "%Y-%m-%d"
        try:
            if start_date and end_date:
                start_date_obj = datetime.strptime(start_date, date_format).date()
                end_date_obj = datetime.strptime(end_date, date_format).date()

                if end_date_obj < start_date_obj:
                    raise ValidationError("End date must be after start date.")

                no_of_days = (end_date_obj - start_date_obj).days

            # Ensure amount is converted to integer paise
            try:
                amount = float(amount)
                order_amount = int(amount * 100)  # Convert to paise
            except ValueError:
                raise ValidationError("Invalid amount format.")

            # Store advertisement details in the session
            request.session["advertisement_data"] = {
                "subject": subject,
                "amount": amount,
                "start_date": start_date,
                "end_date": end_date,
                "max_views": (
                    int(max_views) if max_views else None
                ),  # Convert to integer
                "priority": priority,
                "age_group": age_group,
                "gender": gender,
                "image_vertical": image_vertical.name if image_vertical else None,
                "image_horizontal": image_horizontal.name if image_horizontal else None,
                "no_of_days": no_of_days,
            }

            # Initialize Razorpay client
            client = razorpay.Client(
                auth=(settings.RAZORPAY_KEY_ID, settings.RAZORPAY_KEY_SECRET)
            )

            # Create order
            order_currency = "INR"
            order = client.order.create( # type: ignore
                dict(amount=order_amount, currency=order_currency, payment_capture="1")
            )

            # Store order ID in the session
            request.session["razorpay_order_id"] = order["id"]

            # Pass order details to checkout page
            return render(
                request,
                "ad_checkout.html",
                {
                    "order_id": order["id"],
                    "amount": amount,
                    "currency": order_currency,
                    "key_id": settings.RAZORPAY_KEY_ID,
                },
            )

        except ValueError as e:
            return render(
                request,
                "ad_form.html",
                {
                    "action": "Create",
                    "error": "Invalid data format. Please enter valid data.",
                },
            )
        except ValidationError as e:
            return render(
                request, "ad_form.html", {"action": "Create", "error": str(e)}
            )

    return render(request, "ad_form.html", {"action": "Create"})


def payment_success(request):
    if request.method == "POST":
        razorpay_order_id = request.POST.get("razorpay_order_id")
        razorpay_payment_id = request.POST.get("razorpay_payment_id")
        razorpay_signature = request.POST.get("razorpay_signature")
        # Verify the payment
        client = razorpay.Client(
            auth=(settings.RAZORPAY_KEY_ID, settings.RAZORPAY_KEY_SECRET)
        )
        try:
            client.utility.verify_payment_signature( # type: ignore
                {
                    "razorpay_order_id": razorpay_order_id,
                    "razorpay_payment_id": razorpay_payment_id,
                    "razorpay_signature": razorpay_signature,
                }
            )

            # Payment is verified, now create the advertisement
            advertisement_data = request.session.pop("advertisement_data", None)

            if advertisement_data is None:
                return redirect(
                    "advertisement_form"
                )  # Redirect back to the form if no data is found

            Advertisement.objects.create(
                subject=advertisement_data["subject"],
                amount=advertisement_data["amount"],
                start_date=advertisement_data["start_date"],
                end_date=advertisement_data["end_date"],
                max_views=advertisement_data["max_views"],
                priority=advertisement_data["priority"],
                age_group=advertisement_data["age_group"],
                gender=advertisement_data["gender"],
                image_vertical=advertisement_data["image_vertical"],
                image_horizontal=advertisement_data["image_horizontal"],
                no_of_days=advertisement_data["no_of_days"],
            )

            return redirect(
                "advertisement_list"
            )  # Redirect to the advertisement list page or any other appropriate page

        except Exception as e:
            # Handle payment verification errors
            print(e)
            return redirect(
                "advertisement_create"
            )  # Redirect back to the form or show an error message
    else:
        return redirect("advertisement_create")  # Handle non-POST requests


def advertisement_update(request, pk):
    ad = get_object_or_404(Advertisement, pk=pk)
    if request.method == "POST":
        ad.subject = request.POST.get("subject")
        ad.amount = request.POST.get("amount")
        ad.start_date = request.POST.get("start_date")
        ad.end_date = request.POST.get("end_date")
        ad.max_views = request.POST.get("max_views")
        ad.priority = request.POST.get("priority")
        ad.age_group = request.POST.get("age_group")
        ad.gender = request.POST.get("gender")
        ad.image_vertical = request.FILES.get("image_vertical", ad.image_vertical)
        ad.image_horizontal = request.FILES.get("image_horizontal", ad.image_horizontal)
        ad.save()
        return redirect("advertisement_detail", pk=ad.pk)
    return render(request, "ad_form.html", {"ad": ad, "action": "Update"})


def advertisement_delete(request, pk):
    ad = get_object_or_404(Advertisement, pk=pk)
    if request.method == "POST":
        ad.delete()
        return redirect("advertisement_list")
    return render(request, "ad_confirm_delete.html", {"ad": ad})


def place_bid_view(request, ad_id):
    ad = get_object_or_404(Advertisement, pk=ad_id)
    bid_amount = request.POST.get("bid_amount")

    if bid_amount:
        bid_amount = float(bid_amount)
        success = ad.place_bid(bid_amount)

        if success:
            return JsonResponse(
                {"message": "Bid placed successfully!", "success": True}
            )
        else:
            return JsonResponse(
                {"message": "Your bid is lower than the current bid.", "success": False}
            )

    return JsonResponse({"message": "Invalid bid amount.", "success": False})
