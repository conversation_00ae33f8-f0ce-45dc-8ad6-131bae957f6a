from django.utils import timezone
from django.db import models
from django.core.exceptions import ValidationError
from datetime import date, datetime


class Advertisement(models.Model):
    subject = models.CharField(max_length=255)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    no_of_days = models.PositiveIntegerField(default=0)
    image_vertical = models.ImageField(upload_to="advertisement/vertical/")
    image_horizontal = models.ImageField(upload_to="advertisement/horizontal/")
    max_views = models.PositiveIntegerField()
    views_left = models.PositiveIntegerField(default=0)
    priority = models.IntegerField(default=0)
    bid_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)  # type: ignore
    status = models.BooleanField(default=False)
    age_group = models.Char<PERSON>ield(max_length=50)
    gender = models.Char<PERSON><PERSON>(max_length=50)

    def clean(self):
        """Custom validation for start and end dates."""
        if self.start_date and self.end_date:
            if self.end_date < self.start_date:
                raise ValidationError("End date must be after start date.")

    def save(self, *args, **kwargs):
        today = timezone.now().date()

        # Ensure dates are in correct format
        if isinstance(self.start_date, str):
            self.start_date = datetime.strptime(self.start_date, "%Y-%m-%d").date()
        if isinstance(self.end_date, str):
            self.end_date = datetime.strptime(self.end_date, "%Y-%m-%d").date()

        # Calculate no_of_days if both dates are set
        if self.start_date and self.end_date:
            self.no_of_days = (self.end_date - self.start_date).days
        else:
            self.no_of_days = 0

        # Set default start_date and end_date to today if they are None
        if not self.start_date:
            self.start_date = today
        if not self.end_date:
            self.end_date = today

        # Initialize views_left when the advertisement is first created
        if not self.pk:
            self.views_left = self.max_views

        # Update status based on current date and views left
        if self.views_left == 0 or self.end_date < today:
            self.status = False
        elif self.start_date <= today <= self.end_date and self.views_left > 0:
            self.status = True

        super().save(*args, **kwargs)

    def decrement_views(self):
        """Decrement views_left when an ad is viewed."""
        if self.views_left > 0:
            self.views_left -= 1
            self.save()

    def place_bid(self, amount):
        """Place a bid for the ad. If the bid is higher than the current amount, update it."""
        if amount > self.bid_amount:
            self.bid_amount = amount
            self.save()
            return True
        return False

    def __str__(self):
        return f"{self.subject} - Priority {self.priority}"
