from django.urls import path
from . import views


urlpatterns = [
    path("signup/", views.sublibrarian_signup, name="sub_signup"),
    path("login/", views.sublibrarian_login, name="sub_login"),
    path("logout/", views.sublibrarian_logout, name="logout"),
    path("profile/", views.sublibrarian_profile, name="profile"),
    path("table/", views.library_data, name="student-data"),
    path("edit-profile/", views.edit_sublibrarian_profile, name="edit_profile"),
    path("help/", views.help_page, name="help_page"),
    path("dashboard/", views.dashboard, name="dashboard"),
    path("feedback/", views.feedback_page, name="feedback_page"),
    path("daily-transaction/", views.daily_transactions, name="daily-transaction-sublib"),
    path("shifts/", views.shifts_create, name="shifts_create_sub"),

    # Register URLs
    path("register/", views.register_page, name="register_page_sub"),
    path("student-register/", views.student_register, name="student_register_sub"),
    path("invoice-register/", views.invoice_register, name="invoice_register_sub"),
    path("all-invoices/", views.library_data, name="all_invoices_sub"),

    # API endpoints
    path("api/student-invoices/<slug:student_slug>/", views.get_student_invoices, name="get_student_invoices_sub"),

    # Partial Payments
    path("partial-payments/", views.partial_payments_list, name="partial_payments_list_sub"),
    path("add-payment/<slug:invoice_slug>/", views.add_payment_page, name="add_payment_page_sub"),
    path("record-payment/<slug:invoice_slug>/", views.record_payment, name="record_payment_sub"),
    path("mark-complete/<slug:invoice_slug>/", views.mark_complete_page, name="mark_complete_page_sub"),
    path("mark-invoice-complete/<slug:invoice_slug>/", views.mark_invoice_complete, name="mark_invoice_complete_sub"),
    path("send-payment-reminder/<slug:invoice_slug>/", views.send_payment_reminder, name="send_payment_reminder_sub"),
    path("shifts/update/<int:pk>/", views.shifts_update, name="shifts_update_sub"),
    path("shifts/delete/<int:pk>/", views.shifts_delete, name="shifts_delete_sub"),
    path("seats/", views.create_seat, name="seats-list_sub"),
    path("update-seat/<int:pk>", views.update_seat, name="update_seat_sub"),
    path("cancel-seat/<int:pk>/", views.cancel_seat, name="cancel_seat_sub"),
    path("delete-seat/<int:pk>/", views.delete_seat, name="delete_seat_sub"),
]
