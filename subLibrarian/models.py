from django.db import models
from django.contrib.auth.models import User
from librarian.models import Librarian_param
from django.utils import timezone


# Admin Model


class Sublibrarian_param(models.Model):
    librarian = models.ForeignKey(
        Librarian_param,
        on_delete=models.CASCADE,
    )
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    sublibrarian_phone_num = models.BigIntegerField(default=9999999999)
    sublibrarian_role = models.CharField(max_length=50, default="SubLibrarian")
    sublibrarian_address = models.TextField()
    is_sublibrarian = models.BooleanField(default=False)

    def __str__(self):
        return (
            f"{self.user.first_name} {self.user.last_name} | {self.sublibrarian_role}"
        )

class DailyTransactionSub(models.Model):
    sublibrarian = models.ForeignKey(Sublibrarian_param, on_delete=models.CASCADE) 
    date = models.DateField(default=timezone.now)
    opening_balance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    closing_balance_online = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )  # type: ignore
    closing_balance_cash = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    sales_cash = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    sales_online = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    other_income_cash = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    other_income_online = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )  # type: ignore
    deposit = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    sales_return_cash = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    sales_return_online = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    other_expenses_cash = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    other_expenses_online = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    transaction_count = models.IntegerField(default=0)
    total_student_fees_collection = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    total_invoice_collection = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    total_daily_collection = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )

    def __str__(self):
        return f"Transaction Date : {self.date}"